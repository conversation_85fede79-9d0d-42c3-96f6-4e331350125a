
import sys
import yaml
import ccxt
import os
import pandas as pd
import numpy as np
import random
import time
import json
import traceback
from threading import Thread
from datetime import datetime, timedelta
import workers as workers
import sys
import secure as secure  # Import our secure login module
import checkbox_functions  # Import our checkbox functions module

# Debug flag - set to False in production to disable print statements
DEBUG = False

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTabWidget, QFrame, QStatusBar, QGridLayout, QMessageBox,
    QCheckBox, QComboBox, QTableWidget, QHeaderView, QTableWidgetItem,
    QSpinBox, QDoubleSpinBox, QGroupBox, QSlider, QMenu, QAbstractItemView,
    QInputDialog, QSizePolicy, QDialog, QStackedWidget, QProgressBar,
    QGraphicsOpacityEffect, QLineEdit, QDialogButtonBox, QFileDialog
)
from PySide6.QtGui import QFont, QAction, QShortcut, QKeySequence, QColor, QIcon, QMovie, QDesktopServices, QCursor
from PySide6.QtCore import Qt, QTimer, QUrl, QPropertyAnimation, QEasingCurve, QSize, QThread, Signal, QPointF
from PySide6.QtWidgets import QHBoxLayout
from datetime import datetime
import pyqtgraph as pg
import math
from indicator_manager import IndicatorSettingsDialog

# Import custom dialogs
try:
    # First try direct import
    from dialogs.auto_trade_settings_dialog import AutoTradeSettingsDialog
except ImportError:
    try:
        # Then try from the dialogs package
        from dialogs import AutoTradeSettingsDialog
    except ImportError:
        print("Warning: Could not import AutoTradeSettingsDialog")

# Performance Settings
SETTINGS = {
    'chart_update_ms': 5000,
    'orderbook_update_ms': 5000,
    'account_update_ms': 15000,
    'positions_update_ms': 10000,
    'orders_update_ms': 15000,
    'mini_widgets_update_ms': 15000,
    'analysis_update_ms': 10000,
    'scanner_update_ms': 5000,
    'performance_update_ms': 120000,
    'metrics_update_ms': 15000,
    'use_gpu': False,
    'position_cache_ttl': 30,
    'orderbook_cache_ttl': 5.0,
    'account_cache_ttl': 60,
    'max_cached_trades': 1500,
    'max_chart_points': 500
}

# Custom movable trend line class
class MovableTrendLine(pg.InfiniteLine):
    """A trend line that can be moved by dragging with the mouse"""

    def __init__(self, pos, angle=90, pen=None, movable=True, bounds=None,
                 hoverPen=None, label=None, labelOpts=None, span=(0, 1)):
        # Set default label options if not provided
        if labelOpts is None:
            labelOpts = {
                'position': 0.5,  # Position at the middle of the line
                'color': (255, 255, 0),  # Yellow text
                'fill': (0, 0, 0, 100),  # Semi-transparent black background
                'border': {'color': 'y', 'width': 1},  # Yellow border
                'movable': True  # Make the label movable too
            }

        # For non-orthogonal lines, pos must be a QPointF
        if angle != 0 and angle != 90 and angle != 180 and angle != 270:
            if not isinstance(pos, QPointF):
                pos = QPointF(pos, 0)

        # Call the parent constructor
        super().__init__(pos, angle, pen, movable, bounds, hoverPen, label, labelOpts, span)

        # Set cursor to indicate it's movable
        self.setCursor(QCursor(Qt.SizeAllCursor))

        # Set hover pen to highlight on hover
        if hoverPen is None:
            self.setHoverPen(pg.mkPen(color='yellow', width=3))

        # Store the label text if provided
        self.label_text = label

    def setLength(self, length):
        """Set the length of the line"""
        # InfiniteLine doesn't have a length property, so we need to adjust the span
        # to create the appearance of a finite line with the given length
        if length <= 0:
            return

        # Calculate the span factor - this works for any angle
        span_factor = length / 2

        # Set the span to create a line of the desired length
        # The span is relative to the position, so we need to normalize it
        # The setSpan method requires two separate arguments, not a tuple
        self.setSpan(0.5 - span_factor, 0.5 + span_factor)

# Configure PyQtGraph based on GPU flag
# This will be updated after the USE_GPU flag is defined
def configure_pyqtgraph():
    """Configure PyQtGraph based on the USE_GPU flag"""
    print("\n===== CONFIGURING PYQTGRAPH =====")
    print(f"USE_GPU flag is set to: {USE_GPU}")

    try:
        if USE_GPU:
            # Try to enable enhanced GPU acceleration for PyQtGraph
            print("Attempting to configure PyQtGraph for GPU acceleration...")

            # Check if OpenGL is available
            try:
                from OpenGL import GL
                print("OpenGL module is available")
                opengl_available = True
            except ImportError:
                print("OpenGL module is NOT available - falling back to CPU mode")
                opengl_available = False

            if opengl_available:
                # Enable enhanced GPU acceleration for PyQtGraph
                pg.setConfigOptions(
                    useOpenGL=True,          # Use OpenGL for hardware acceleration
                    antialias=True,          # Enable antialiasing for smoother graphics
                    enableExperimental=True  # Enable experimental features for better performance
                )
                print("PyQtGraph successfully configured for GPU acceleration")
            else:
                # Basic configuration without GPU acceleration
                pg.setConfigOptions(
                    useOpenGL=False,         # Disable OpenGL
                    antialias=True           # Keep antialiasing for smoother graphics
                )
                print("PyQtGraph configured for CPU mode (OpenGL not available)")
        else:
            # Basic configuration without GPU acceleration
            pg.setConfigOptions(
                useOpenGL=False,         # Disable OpenGL
                antialias=True           # Keep antialiasing for smoother graphics
            )
            print("PyQtGraph configured for CPU mode (USE_GPU=False)")

        # Print current PyQtGraph configuration
        print("\nCurrent PyQtGraph configuration:")
        config = pg.getConfigOption('useOpenGL')
        print(f"useOpenGL: {config}")
        config = pg.getConfigOption('antialias')
        print(f"antialias: {config}")

    except Exception as e:
        print(f"Error configuring PyQtGraph: {str(e)}")
        # Fallback to basic configuration
        pg.setConfigOptions(
            useOpenGL=False,
            antialias=True
        )
        print("PyQtGraph configured with fallback settings due to error")

    print("===================================\n")

# Initial configuration with default settings
# Will be reconfigured after USE_GPU is defined
pg.setConfigOptions(useOpenGL=True, antialias=True)


# Global variables
DEBUG = False  # Set to False in production to disable debug output

# Debug print wrapper function
def debug_print(*args, **kwargs):
    """Print only if DEBUG is True"""
    if DEBUG:
        print(*args, **kwargs)

# Performance Settings
PERFORMANCE_SETTINGS = {
    'SLOW': {
        'chart_update_ms': 5000,
        'orderbook_update_ms': 5000,
        'account_update_ms': 15000,
        'positions_update_ms': 10000,
        'orders_update_ms': 15000,
        'mini_widgets_update_ms': 15000,
        'analysis_update_ms': 10000,
        'scanner_update_ms': 5000,
        'performance_update_ms': 120000,
        'metrics_update_ms': 15000,
        'use_gpu': False,
        'position_cache_ttl': 30,
        'orderbook_cache_ttl': 5.0,
        'account_cache_ttl': 60,
        'max_cached_trades': 1000,
        'max_chart_points': 500
    },
    'MEDIUM': {
        'chart_update_ms': 2000,
        'orderbook_update_ms': 2000,
        'account_update_ms': 10000,
        'positions_update_ms': 5000,
        'orders_update_ms': 10000,
        'mini_widgets_update_ms': 10000,
        'analysis_update_ms': 5000,
        'scanner_update_ms': 3000,
        'performance_update_ms': 60000,
        'metrics_update_ms': 10000,
        'use_gpu': True,
        'position_cache_ttl': 15,
        'orderbook_cache_ttl': 2.0,
        'account_cache_ttl': 30,
        'max_cached_trades': 2000,
        'max_chart_points': 1000
    },
    'FAST': {
        'chart_update_ms': 1000,
        'orderbook_update_ms': 1000,
        'account_update_ms': 5000,
        'positions_update_ms': 2000,
        'orders_update_ms': 5000,
        'mini_widgets_update_ms': 5000,
        'analysis_update_ms': 2000,
        'scanner_update_ms': 1000,
        'performance_update_ms': 30000,
        'metrics_update_ms': 5000,
        'use_gpu': True,
        'position_cache_ttl': 5,
        'orderbook_cache_ttl': 1.0,
        'account_cache_ttl': 15,
        'max_cached_trades': 5000,
        'max_chart_points': 2000
    }
}

# Apply performance settings based on system capability
def apply_performance_settings(preset='SLOW'):
    """Apply performance settings based on preset"""
    global SETTINGS
    SETTINGS = PERFORMANCE_SETTINGS[preset]
    print(f"\n===== USING {preset} PERFORMANCE PRESET =====")
    for key, value in SETTINGS.items():
        print(f"{key}: {value}")
    print("="*49 + "\n")
    return SETTINGS

epinnox_thread_flag = False
pnl_perc_thread_flag = False
auto_transfer_thread_flag = False
auto_audit_thread_flag = False
auto_advice_thread_flag = False
monitor_notifications_thread_flag = False

# Default trading parameters
default_symbol = "MOODENG/USDT:USDT"
default_timeframe = "1m"
default_leverage = 20
default_quantity = 50

# Global exchange variable
exchange = None
demo_mode = False  # Default to live mode - will be set to demo only if no valid API credentials

# Cache for API data to reduce API calls - Using "Slow" preset by default
position_cache = {
    'data': [],
    'timestamp': 0,
    'ttl': 30  # Set to 30 seconds for slow mode
}
order_cache = {
    'data': {},  # Symbol -> orders mapping
    'timestamp': 0,
    'ttl': 30  # Cache TTL in seconds
}
account_cache = {
    'data': {},
    'timestamp': 0,
    'ttl': 60  # Cache TTL in seconds
}
orderbook_cache = {
    'data': {},  # Symbol -> orderbook mapping
    'timestamp': 0,
    'ttl': 5.0  # Set to 5 seconds for slow mode
}

# Initialize worker module with necessary functions
def init_workers():
    """Initialize the worker module with necessary functions and settings"""
    workers.exchange = exchange
    workers.demo_mode = demo_mode
    workers.DEBUG = DEBUG
    workers.USE_GPU = USE_GPU  # Pass GPU flag to workers module
    workers.fetch_open_positions = fetch_open_positions
    workers.fetch_order_book = fetch_order_book
    workers.fetch_account_info = fetch_account_info
    workers.fetch_ohlcv = fetch_ohlcv
    workers.fetch_trades = fetch_trades

def initialize_exchange():
    """Initialize the exchange and load markets. Called during splash screen."""
    global exchange, demo_mode

    # Set demo_mode to False by default - we'll try to use real API keys
    demo_mode = False

    try:
        # Try to load credentials from file
        credentials_path = 'credentials.yaml'
        if not os.path.exists(credentials_path):
            # Try in the me3 - Copy directory
            credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

        if os.path.exists(credentials_path):
            try:
                print(f"Loading credentials from {credentials_path}")
                with open(credentials_path, 'r') as file:
                    credentials = yaml.safe_load(file)
                    print(f"Credentials loaded successfully: {list(credentials.keys())}")

                    # Get default account name
                    default_account = credentials.get('default_account', '')
                    print(f"Default account: {default_account}")

                    # Get exchange name from credentials or default to huobi
                    exchange_name = 'huobi'  # Default exchange

                    # Try to get exchange name from default account
                    if default_account and 'accounts' in credentials:
                        for account in credentials['accounts']:
                            if account.get('name') == default_account:
                                exchange_name = account.get('exchange', 'huobi')
                                print(f"Using exchange from default account: {exchange_name}")
                                break

                    # Get API keys - check multiple possible formats
                    api_key = credentials.get('apiKey', '')
                    if not api_key:
                        api_key = credentials.get(exchange_name, {}).get('api_key', '')

                    # Try to get from accounts list with default_account
                    if not api_key and 'accounts' in credentials and credentials.get('accounts'):
                        default_account = credentials.get('default_account', '')
                        if default_account:
                            # Find the account with the matching name
                            for account in credentials['accounts']:
                                if account.get('name') == default_account:
                                    api_key = account.get('api_key', '')
                                    break

                        # If still no API key, use the first account
                        if not api_key and credentials['accounts'] and len(credentials['accounts']) > 0:
                            api_key = credentials['accounts'][0].get('api_key', '')
                            print(f"Using first account API key: {api_key}")

                    # Get secret key - check multiple possible formats
                    secret_key = credentials.get('secret', '')
                    if not secret_key:
                        secret_key = credentials.get(exchange_name, {}).get('secret_key', '')

                    # Try to get from accounts list with default_account
                    if not secret_key and 'accounts' in credentials and credentials.get('accounts'):
                        default_account = credentials.get('default_account', '')
                        if default_account:
                            # Find the account with the matching name
                            for account in credentials['accounts']:
                                if account.get('name') == default_account:
                                    secret_key = account.get('secret_key', '')
                                    break

                        # If still no secret key, use the first account
                        if not secret_key and credentials['accounts'] and len(credentials['accounts']) > 0:
                            secret_key = credentials['accounts'][0].get('secret_key', '')
                            print(f"Using first account secret key")

                    # Check if we have valid API credentials
                    if api_key and secret_key:
                        # Initialize the exchange with API credentials and updated settings
                        exchange_config = {
                            'apiKey': api_key,
                            'secret': secret_key,
                            'enableRateLimit': True,
                            'options': {
                                'defaultType': 'swap',  # Use swap for futures trading
                            },
                            'sandbox': False,  # Ensure we're not in sandbox mode
                        }

                        # Add HTX-specific configuration if using Huobi
                        if exchange_name == 'huobi':
                            # Use the original Huobi endpoints that still work
                            exchange_config['urls'] = {
                                'api': {
                                    'public': 'https://api.huobi.pro',
                                    'private': 'https://api.huobi.pro',
                                    'swap': 'https://api.hbdm.com',
                                }
                            }
                            # Add additional options for HTX compatibility
                            exchange_config['options'].update({
                                'hostname': 'api.huobi.pro',  # Force hostname
                                'adjustForTimeDifference': True,  # Handle time sync issues
                            })

                        exchange = getattr(ccxt, exchange_name)(exchange_config)

                        # Load markets to enable derivatives positions
                        print("Loading markets for account with API key...")
                        exchange.load_markets()
                        print("Markets loaded successfully")

                        # Set demo_mode to False since we have valid API credentials
                        demo_mode = False
                    else:
                        print("Warning: No valid API credentials found in credentials.yaml")
                        raise ValueError("Missing API credentials")
            except Exception as e:
                print(f"Error loading credentials: {e}")
                # Fall back to demo mode with unauthenticated exchange
                exchange = ccxt.huobi({
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'swap',
                    }
                })
                print("Falling back to demo mode due to credential error.")

                # Load markets in demo mode
                try:
                    print("Loading markets in demo mode...")
                    exchange.load_markets()
                    print("Markets loaded successfully in demo mode")
                except Exception as market_e:
                    print(f"Warning: Could not load markets in demo mode: {market_e}")

                # Set demo_mode to True since we couldn't use API credentials
                demo_mode = True
        else:
            # No credentials file found, use exchange without authentication
            print("Warning: No credentials.yaml file found.")
            exchange = ccxt.huobi({
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',
                }
            })
            print("Running in demo mode without credentials file.")

            # Load markets in demo mode
            try:
                print("Loading markets in demo mode...")
                exchange.load_markets()
                print("Markets loaded successfully in demo mode")
            except Exception as e:
                print(f"Warning: Could not load markets in demo mode: {e}")

            # Set demo_mode to True since we have no credentials file
            demo_mode = True
    except Exception as e:
        print(f"Critical error initializing exchange: {e}")
        import traceback
        traceback.print_exc()

        # Create a demo exchange for UI testing as fallback
        exchange = None
        demo_mode = True

    # Double-check demo_mode flag based on exchange state
    if exchange is None:
        # Create a fallback exchange with demo API keys
        print("Exchange is None, creating fallback exchange with demo API keys")
        try:
            exchange = ccxt.huobi({
                'apiKey': 'demo_api_key',
                'secret': 'demo_secret_key',
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',
                }
            })

            # Try to load markets
            try:
                print("Loading markets with demo API keys...")
                exchange.load_markets()
                print("Markets loaded successfully with demo API keys")
                demo_mode = False  # Use live mode with demo API keys
            except Exception as market_e:
                print(f"Warning: Could not load markets with demo API keys: {market_e}")
                demo_mode = True
        except Exception as e:
            print(f"Error creating fallback exchange: {e}")
            demo_mode = True
    elif not hasattr(exchange, 'apiKey') or not exchange.apiKey:
        demo_mode = True

    # Only print this once during initialization
    if not hasattr(initialize_exchange, 'demo_mode_printed'):
        print("🔍 demo_mode =", demo_mode)
        initialize_exchange.demo_mode_printed = True

    # Initialize worker module with the updated exchange and demo_mode
    init_workers()

    # Initialize checkbox functions module with necessary functions
    checkbox_functions.init_checkbox_functions(
        exchange,
        demo_mode,
        fetch_open_positions,
        fetch_best_bid,
        fetch_best_ask,
        place_limit_order,
        place_market_order,
        close_position,
        fetch_ohlcv,
        set_leverage,
        DEBUG
    )

    return exchange

# Debug and optimization flags
DEBUG = False
CHART_DEBUG = False
POSITION_DEBUG = False
USE_GPU = False  # Disable GPU acceleration for lowest CPU usage

# Global settings for timer intervals (milliseconds) - Using "Slow" preset by default
CHART_UPDATE_INTERVAL = 5000
ORDERBOOK_UPDATE_INTERVAL = 5000
ACCOUNT_UPDATE_INTERVAL = 15000
POSITIONS_UPDATE_INTERVAL = 10000
ORDERS_UPDATE_INTERVAL = 15000
MINI_WIDGETS_UPDATE_INTERVAL = 15000
ANALYSIS_UPDATE_INTERVAL = 10000
SCANNER_UPDATE_INTERVAL = 5000
PERFORMANCE_TAB_UPDATE_INTERVAL = 120000
ANALYSIS_METRICS_UPDATE_INTERVAL = 15000

def debug_print(message):
    """Print debug messages if DEBUG is enabled"""
    if DEBUG:
        print(f"DEBUG: {message}")

def chart_debug_print(message):
    """Print chart debug messages if CHART_DEBUG is enabled"""
    if CHART_DEBUG:
        print(f"CHART: {message}")

def position_debug_print(message):
    """Print position debug messages if POSITION_DEBUG is enabled"""
    if POSITION_DEBUG:
        print(f"POSITION TABLE: {message}")

# Now that USE_GPU is defined, configure PyQtGraph
configure_pyqtgraph()

# Settings dialog for adjusting update speeds
class SettingsDialog(QDialog):
    """Dialog for adjusting update speeds for different components"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setWindowTitle("Update Speed Settings")
        self.setMinimumWidth(400)
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                color: #e0e0e0;
            }
            QLabel {
                color: #e0e0e0;
            }
            QSpinBox, QDoubleSpinBox {
                background-color: #2d2d2d;
                color: #e0e0e0;
                border: 1px solid #3d3d3d;
                padding: 5px;
            }
            QPushButton {
                background-color: #2d2d2d;
                color: #e0e0e0;
                border: 1px solid #3d3d3d;
                padding: 5px 15px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
            }
            QGroupBox {
                color: #e0e0e0;
                border: 1px solid #3d3d3d;
                margin-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)

        self.init_ui()

    def init_ui(self):
        """Initialize the UI components"""
        layout = QVBoxLayout()

        # Create group box for update intervals
        group_box = QGroupBox("Update Intervals (milliseconds)")
        group_layout = QGridLayout()

        # Chart update interval
        group_layout.addWidget(QLabel("Chart Updates:"), 0, 0)
        self.chart_spin = QSpinBox()
        self.chart_spin.setRange(100, 10000)
        self.chart_spin.setSingleStep(100)
        self.chart_spin.setValue(CHART_UPDATE_INTERVAL)
        self.chart_spin.setToolTip("Interval in milliseconds between chart updates")
        group_layout.addWidget(self.chart_spin, 0, 1)

        # Orderbook update interval
        group_layout.addWidget(QLabel("Orderbook Updates:"), 1, 0)
        self.orderbook_spin = QSpinBox()
        self.orderbook_spin.setRange(100, 10000)
        self.orderbook_spin.setSingleStep(100)
        self.orderbook_spin.setValue(ORDERBOOK_UPDATE_INTERVAL)
        self.orderbook_spin.setToolTip("Interval in milliseconds between orderbook updates")
        group_layout.addWidget(self.orderbook_spin, 1, 1)

        # Account update interval
        group_layout.addWidget(QLabel("Account Updates:"), 2, 0)
        self.account_spin = QSpinBox()
        self.account_spin.setRange(500, 30000)
        self.account_spin.setSingleStep(500)
        self.account_spin.setValue(ACCOUNT_UPDATE_INTERVAL)
        self.account_spin.setToolTip("Interval in milliseconds between account updates")
        group_layout.addWidget(self.account_spin, 2, 1)

        # Positions update interval
        group_layout.addWidget(QLabel("Positions Updates:"), 3, 0)
        self.positions_spin = QSpinBox()
        self.positions_spin.setRange(100, 10000)
        self.positions_spin.setSingleStep(100)
        self.positions_spin.setValue(POSITIONS_UPDATE_INTERVAL)
        self.positions_spin.setToolTip("Interval in milliseconds between positions updates")
        group_layout.addWidget(self.positions_spin, 3, 1)

        # Orders update interval
        group_layout.addWidget(QLabel("Orders Updates:"), 4, 0)
        self.orders_spin = QSpinBox()
        self.orders_spin.setRange(500, 20000)
        self.orders_spin.setSingleStep(500)
        self.orders_spin.setValue(ORDERS_UPDATE_INTERVAL)
        self.orders_spin.setToolTip("Interval in milliseconds between orders updates")
        group_layout.addWidget(self.orders_spin, 4, 1)

        # Mini widgets update interval
        group_layout.addWidget(QLabel("Mini Widgets Updates:"), 5, 0)
        self.mini_widgets_spin = QSpinBox()
        self.mini_widgets_spin.setRange(500, 20000)
        self.mini_widgets_spin.setSingleStep(500)
        self.mini_widgets_spin.setValue(MINI_WIDGETS_UPDATE_INTERVAL)
        self.mini_widgets_spin.setToolTip("Interval in milliseconds between mini widgets updates")
        group_layout.addWidget(self.mini_widgets_spin, 5, 1)

        # Analysis update interval
        group_layout.addWidget(QLabel("Analysis Updates:"), 6, 0)
        self.analysis_spin = QSpinBox()
        self.analysis_spin.setRange(500, 20000)
        self.analysis_spin.setSingleStep(500)
        self.analysis_spin.setValue(ANALYSIS_UPDATE_INTERVAL)
        self.analysis_spin.setToolTip("Interval in milliseconds between analysis updates")
        group_layout.addWidget(self.analysis_spin, 6, 1)

        # Scanner update interval
        group_layout.addWidget(QLabel("Scanner Updates:"), 7, 0)
        self.scanner_spin = QSpinBox()
        self.scanner_spin.setRange(500, 20000)
        self.scanner_spin.setSingleStep(500)
        self.scanner_spin.setValue(SCANNER_UPDATE_INTERVAL)
        self.scanner_spin.setToolTip("Interval in milliseconds between scanner updates")
        group_layout.addWidget(self.scanner_spin, 7, 1)

        # Performance tab update interval
        group_layout.addWidget(QLabel("Performance Tab Updates:"), 8, 0)
        self.performance_tab_spin = QSpinBox()
        self.performance_tab_spin.setRange(5000, 120000)
        self.performance_tab_spin.setSingleStep(5000)
        self.performance_tab_spin.setValue(PERFORMANCE_TAB_UPDATE_INTERVAL)
        self.performance_tab_spin.setToolTip("Interval in milliseconds between performance tab updates")
        group_layout.addWidget(self.performance_tab_spin, 8, 1)

        # Analysis metrics update interval
        group_layout.addWidget(QLabel("Analysis Metrics Updates:"), 9, 0)
        self.analysis_metrics_spin = QSpinBox()
        self.analysis_metrics_spin.setRange(1000, 30000)
        self.analysis_metrics_spin.setSingleStep(1000)
        self.analysis_metrics_spin.setValue(ANALYSIS_METRICS_UPDATE_INTERVAL)
        self.analysis_metrics_spin.setToolTip("Interval in milliseconds between analysis metrics updates")
        group_layout.addWidget(self.analysis_metrics_spin, 9, 1)

        # GPU Acceleration toggle
        group_layout.addWidget(QLabel("GPU Acceleration:"), 10, 0)
        self.gpu_checkbox = QCheckBox()
        self.gpu_checkbox.setChecked(USE_GPU)
        self.gpu_checkbox.setToolTip("Enable GPU acceleration for charts and visualizations")
        group_layout.addWidget(self.gpu_checkbox, 10, 1)

        # Preset buttons
        preset_layout = QHBoxLayout()
        preset_layout.setSpacing(10)  # Add spacing between buttons

        # Create a label for the presets
        preset_label = QLabel("Presets:")
        preset_layout.addWidget(preset_label)

        # Create the preset buttons with consistent styling
        button_style = "QPushButton { background-color: #333; color: #00ff44; padding: 5px; min-width: 80px; }"

        slow_btn = QPushButton("Slow")
        slow_btn.setStyleSheet(button_style)
        slow_btn.clicked.connect(self.set_slow_preset)
        preset_layout.addWidget(slow_btn)

        medium_btn = QPushButton("Medium")
        medium_btn.setStyleSheet(button_style)
        medium_btn.clicked.connect(self.set_medium_preset)
        preset_layout.addWidget(medium_btn)

        fast_btn = QPushButton("Fast")
        fast_btn.setStyleSheet(button_style)
        fast_btn.clicked.connect(self.set_fast_preset)
        preset_layout.addWidget(fast_btn)

        super_fast_btn = QPushButton("Super Fast")
        super_fast_btn.setStyleSheet(button_style)
        super_fast_btn.clicked.connect(self.set_super_fast_preset)
        preset_layout.addWidget(super_fast_btn)

        # Add the preset layout after the GPU acceleration checkbox
        group_layout.addLayout(preset_layout, 11, 0, 1, 2)

        group_box.setLayout(group_layout)
        layout.addWidget(group_box)

        # Cache settings
        cache_group = QGroupBox("Cache TTL Settings (seconds)")
        cache_layout = QGridLayout()

        # Position cache TTL
        cache_layout.addWidget(QLabel("Position Cache TTL:"), 0, 0)
        self.position_cache_spin = QSpinBox()
        self.position_cache_spin.setRange(1, 60)
        self.position_cache_spin.setValue(position_cache['ttl'])
        self.position_cache_spin.setToolTip("Time to live for position cache in seconds")
        cache_layout.addWidget(self.position_cache_spin, 0, 1)

        # Orderbook cache TTL
        cache_layout.addWidget(QLabel("Orderbook Cache TTL:"), 1, 0)
        self.orderbook_cache_spin = QDoubleSpinBox()
        self.orderbook_cache_spin.setRange(0.1, 10.0)
        self.orderbook_cache_spin.setSingleStep(0.1)
        self.orderbook_cache_spin.setValue(orderbook_cache['ttl'])
        self.orderbook_cache_spin.setToolTip("Time to live for orderbook cache in seconds")
        cache_layout.addWidget(self.orderbook_cache_spin, 1, 1)

        # Account cache TTL
        cache_layout.addWidget(QLabel("Account Cache TTL:"), 2, 0)
        self.account_cache_spin = QSpinBox()
        self.account_cache_spin.setRange(5, 120)
        self.account_cache_spin.setValue(account_cache['ttl'])
        self.account_cache_spin.setToolTip("Time to live for account cache in seconds")
        cache_layout.addWidget(self.account_cache_spin, 2, 1)

        cache_group.setLayout(cache_layout)
        layout.addWidget(cache_group)

        # Buttons
        button_layout = QHBoxLayout()
        apply_btn = QPushButton("Apply")
        apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(apply_btn)

        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def set_slow_preset(self):
        """Set slow update intervals"""
        self.chart_spin.setValue(5000)
        self.orderbook_spin.setValue(5000)
        self.account_spin.setValue(15000)
        self.positions_spin.setValue(10000)
        self.orders_spin.setValue(15000)
        self.mini_widgets_spin.setValue(15000)
        self.analysis_spin.setValue(10000)
        self.scanner_spin.setValue(5000)
        self.performance_tab_spin.setValue(120000)
        self.analysis_metrics_spin.setValue(15000)

        self.position_cache_spin.setValue(30)
        self.orderbook_cache_spin.setValue(5.0)
        self.account_cache_spin.setValue(60)

        # Disable GPU acceleration for lowest CPU usage
        self.gpu_checkbox.setChecked(False)

    def set_medium_preset(self):
        """Set medium update intervals"""
        self.chart_spin.setValue(2000)
        self.orderbook_spin.setValue(2000)
        self.account_spin.setValue(10000)
        self.positions_spin.setValue(5000)
        self.orders_spin.setValue(10000)
        self.mini_widgets_spin.setValue(10000)
        self.analysis_spin.setValue(5000)
        self.scanner_spin.setValue(2000)
        self.performance_tab_spin.setValue(60000)
        self.analysis_metrics_spin.setValue(10000)

        self.position_cache_spin.setValue(20)
        self.orderbook_cache_spin.setValue(2.0)
        self.account_cache_spin.setValue(45)

        # Enable GPU acceleration for medium preset if hardware supports it
        self.gpu_checkbox.setChecked(True)

    def set_fast_preset(self):
        """Set fast update intervals"""
        self.chart_spin.setValue(1000)
        self.orderbook_spin.setValue(1000)
        self.account_spin.setValue(5000)
        self.positions_spin.setValue(2000)
        self.orders_spin.setValue(5000)
        self.mini_widgets_spin.setValue(5000)
        self.analysis_spin.setValue(2000)
        self.scanner_spin.setValue(1000)
        self.performance_tab_spin.setValue(30000)
        self.analysis_metrics_spin.setValue(5000)

        self.position_cache_spin.setValue(10)
        self.orderbook_cache_spin.setValue(1.0)
        self.account_cache_spin.setValue(30)

        # Enable GPU acceleration for fast preset
        self.gpu_checkbox.setChecked(True)

    def set_super_fast_preset(self):
        """Set super fast update intervals"""
        self.chart_spin.setValue(500)
        self.orderbook_spin.setValue(500)
        self.account_spin.setValue(2000)
        self.positions_spin.setValue(1000)
        self.orders_spin.setValue(2000)
        self.mini_widgets_spin.setValue(3000)
        self.analysis_spin.setValue(1000)
        self.scanner_spin.setValue(500)
        self.performance_tab_spin.setValue(15000)
        self.analysis_metrics_spin.setValue(2000)

        self.position_cache_spin.setValue(5)
        self.orderbook_cache_spin.setValue(0.5)
        self.account_cache_spin.setValue(15)

        # Enable GPU acceleration for super fast preset
        self.gpu_checkbox.setChecked(True)

    def apply_settings(self):
        """Apply the settings to the global variables and update timers"""
        global CHART_UPDATE_INTERVAL, ORDERBOOK_UPDATE_INTERVAL, ACCOUNT_UPDATE_INTERVAL
        global POSITIONS_UPDATE_INTERVAL, ORDERS_UPDATE_INTERVAL, MINI_WIDGETS_UPDATE_INTERVAL
        global ANALYSIS_UPDATE_INTERVAL, SCANNER_UPDATE_INTERVAL, PERFORMANCE_TAB_UPDATE_INTERVAL
        global ANALYSIS_METRICS_UPDATE_INTERVAL, USE_GPU

        # Update global variables
        CHART_UPDATE_INTERVAL = self.chart_spin.value()
        ORDERBOOK_UPDATE_INTERVAL = self.orderbook_spin.value()
        ACCOUNT_UPDATE_INTERVAL = self.account_spin.value()
        POSITIONS_UPDATE_INTERVAL = self.positions_spin.value()
        ORDERS_UPDATE_INTERVAL = self.orders_spin.value()
        MINI_WIDGETS_UPDATE_INTERVAL = self.mini_widgets_spin.value()
        ANALYSIS_UPDATE_INTERVAL = self.analysis_spin.value()
        SCANNER_UPDATE_INTERVAL = self.scanner_spin.value()
        PERFORMANCE_TAB_UPDATE_INTERVAL = self.performance_tab_spin.value()
        ANALYSIS_METRICS_UPDATE_INTERVAL = self.analysis_metrics_spin.value()

        # Update GPU acceleration setting
        USE_GPU = self.gpu_checkbox.isChecked()

        # Reconfigure PyQtGraph based on GPU setting
        configure_pyqtgraph()

        # Print debug info
        print("\n===== APPLYING SPEED SETTINGS =====")
        print(f"Chart updates: {CHART_UPDATE_INTERVAL}ms")
        print(f"Orderbook updates: {ORDERBOOK_UPDATE_INTERVAL}ms")
        print(f"Account updates: {ACCOUNT_UPDATE_INTERVAL}ms")
        print(f"Positions updates: {POSITIONS_UPDATE_INTERVAL}ms")
        print(f"Orders updates: {ORDERS_UPDATE_INTERVAL}ms")
        print(f"Mini widgets updates: {MINI_WIDGETS_UPDATE_INTERVAL}ms")
        print(f"Analysis updates: {ANALYSIS_UPDATE_INTERVAL}ms")
        print(f"Scanner updates: {SCANNER_UPDATE_INTERVAL}ms")
        print(f"Performance tab updates: {PERFORMANCE_TAB_UPDATE_INTERVAL}ms")
        print(f"Analysis metrics updates: {ANALYSIS_METRICS_UPDATE_INTERVAL}ms")
        print(f"GPU Acceleration: {USE_GPU}")

        # Update cache TTLs
        global position_cache, orderbook_cache, account_cache
        position_cache['ttl'] = self.position_cache_spin.value()
        orderbook_cache['ttl'] = self.orderbook_cache_spin.value()
        account_cache['ttl'] = self.account_cache_spin.value()

        print(f"Position cache TTL: {position_cache['ttl']}s")
        print(f"Orderbook cache TTL: {orderbook_cache['ttl']}s")
        print(f"Account cache TTL: {account_cache['ttl']}s")
        print("===================================\n")

        # Update timers in the parent window if it exists
        if self.parent:
            # Stop all timers first
            self.parent.plot_timer.stop()
            self.parent.ob_timer.stop()
            self.parent.acct_timer.stop()
            self.parent.positions_timer.stop()
            self.parent.orders_timer.stop()
            self.parent.mini_widgets_timer.stop()
            self.parent.analysis_timer.stop()

            # Set new intervals
            self.parent.plot_timer.setInterval(CHART_UPDATE_INTERVAL)
            self.parent.ob_timer.setInterval(ORDERBOOK_UPDATE_INTERVAL)
            self.parent.acct_timer.setInterval(ACCOUNT_UPDATE_INTERVAL)
            self.parent.positions_timer.setInterval(POSITIONS_UPDATE_INTERVAL)
            self.parent.orders_timer.setInterval(ORDERS_UPDATE_INTERVAL)
            self.parent.mini_widgets_timer.setInterval(MINI_WIDGETS_UPDATE_INTERVAL)
            self.parent.analysis_timer.setInterval(ANALYSIS_UPDATE_INTERVAL)

            # Update analysis panel timer if it exists
            if hasattr(self.parent, 'analysis_panel') and hasattr(self.parent.analysis_panel, 'timer'):
                self.parent.analysis_panel.timer.stop()
                self.parent.analysis_panel.timer.setInterval(ANALYSIS_METRICS_UPDATE_INTERVAL)
                self.parent.analysis_panel.timer.start()
                print(f"Updated analysis panel timer to {ANALYSIS_METRICS_UPDATE_INTERVAL}ms")

            # Update scanner timers if they exist
            try:
                # Find scanner tab
                for i in range(self.parent.tabs.count()):
                    tab = self.parent.tabs.widget(i)
                    if hasattr(tab, 'timer') and self.parent.tabs.tabText(i).lower().find('scanner') >= 0:
                        tab.timer.stop()
                        tab.timer.setInterval(SCANNER_UPDATE_INTERVAL)
                        tab.timer.start()
                        print(f"Updated scanner tab timer to {SCANNER_UPDATE_INTERVAL}ms")

                    # Check for performance tab
                    if hasattr(tab, 'refresh_timer') and self.parent.tabs.tabText(i).lower().find('performance') >= 0:
                        tab.refresh_timer.stop()
                        tab.refresh_timer.setInterval(PERFORMANCE_TAB_UPDATE_INTERVAL)
                        tab.refresh_timer.start()
                        print(f"Updated performance tab timer to {PERFORMANCE_TAB_UPDATE_INTERVAL}ms")
            except Exception as e:
                print(f"Error updating tab timers: {e}")

            # Restart all timers
            self.parent.plot_timer.start()
            self.parent.ob_timer.start()
            self.parent.acct_timer.start()
            self.parent.positions_timer.start()
            self.parent.orders_timer.start()
            self.parent.mini_widgets_timer.start()
            self.parent.analysis_timer.start()

            # Force immediate updates
            self.parent._scheduled_chart_update()
            self.parent._scheduled_orderbook_update()
            self.parent._scheduled_positions_update()

            # Show confirmation message
            QMessageBox.information(self, "Settings Applied",
                                   "Update speed settings have been applied successfully. All timers have been updated.")

class AnalysisPanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.periods = ["5m","15m","1h","4h","1d"]
        self.idx = 0

        h = QHBoxLayout(self)
        h.setContentsMargins(0,4,0,4)
        h.setSpacing(20)

        # 1) Open-duration
        self.time_label   = QLabel("Open Duration: --:--:--")
        self.time_label.  setStyleSheet("color:#00ff44; font-weight:bold;")
        h.addWidget(self.time_label, alignment=Qt.AlignLeft)

        # 2) Prev/Next + timeframe
        btn_prev = QPushButton("❮"); btn_next = QPushButton("❯")
        for b in (btn_prev, btn_next):
            b.setFixedSize(20,20)
            b.setStyleSheet("background:#222;color:#00ff44;border:none;")
        self.tf_label     = QLabel(self.periods[self.idx])
        self.tf_label.    setStyleSheet("color:#00ff44;")
        h.addWidget(btn_prev)
        h.addWidget(self.tf_label)
        h.addWidget(btn_next)

        # 3) Metrics
        self.chg_label    = QLabel("Δ%: ----")
        self.high_label   = QLabel("H: ----")
        self.low_label    = QLabel("L: ----")
        self.vol_label    = QLabel("Vol: ----")
        self.cnt_label    = QLabel("Trd: ----")
        for lbl in (self.chg_label, self.high_label, self.low_label, self.vol_label, self.cnt_label):
            lbl.setStyleSheet("color:#00ff44;")
            h.addWidget(lbl)

        # Add stretch at the end to push everything to the left
        h.addStretch()

        btn_prev.clicked.connect(self.prev)
        btn_next.clicked.connect(self.next)

        # Use global setting for refresh interval
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_metrics)
        self.timer.start(ANALYSIS_METRICS_UPDATE_INTERVAL)

    def prev(self):
        self.idx = (self.idx - 1) % len(self.periods)
        self.update_metrics()

    def next(self):
        self.idx = (self.idx + 1) % len(self.periods)
        self.update_metrics()

    def update_metrics(self):
        self.update_time()
        self.update_period_metrics()

        # Only apply fade effect once every 5 seconds to reduce overhead
        current_time = datetime.now()
        if not hasattr(self, 'last_fade_time') or (current_time - self.last_fade_time).total_seconds() >= 5:
            self.update_fade(self.chg_label)
            self.last_fade_time = current_time

    def update_time(self):
        sym = self.parent.get_symbol()
        pos = fetch_open_positions(sym)
        if pos:
            ts = pos[0].get("timestamp")
            delta = datetime.now() - datetime.fromtimestamp(ts/1000)
            text  = str(delta).split('.')[0]
        else:
            text  = "--:--:--"
        self.time_label.setText(f"Open Duration: {text}")

    def update_period_metrics(self):
        # Update metrics more frequently with GPU acceleration
        current_time = datetime.now()
        if hasattr(self, 'last_metrics_update') and (current_time - self.last_metrics_update).total_seconds() < 2:
            return  # Still throttle slightly to prevent excessive API calls

        self.last_metrics_update = current_time
        tf  = self.periods[self.idx]
        sym = self.parent.get_symbol()

        # Cache data to reduce API calls
        cache_key = f"{sym}_{tf}"
        if not hasattr(self, 'data_cache'):
            self.data_cache = {}

        if cache_key in self.data_cache and (current_time - self.data_cache[cache_key]['timestamp']).total_seconds() < 5:
            # Use cached data if less than 5 seconds old - reduced from 30s for faster updates
            cached = self.data_cache[cache_key]
            pct, high, low, vol, cnt = cached['pct'], cached['high'], cached['low'], cached['vol'], cached['cnt']
        else:
            # Fetch new data
            df = fetch_ohlcv(sym, tf, limit=2)
            # price change + high/low/vol
            if df is not None and len(df)>=2:
                old,new = df.iloc[0]['close'], df.iloc[-1]['close']
                pct     = (new/old-1)*100
                high    = df.iloc[-1]['high']
                low     = df.iloc[-1]['low']
                vol     = df.iloc[-1]['volume']

                # Fetch trades more frequently with GPU acceleration
                if not hasattr(self, 'trades_cache') or (current_time - self.trades_cache['timestamp']).total_seconds() >= 5:
                    # Reduced from 30s to 5s for faster updates
                    trades = fetch_trades(sym, limit=500)
                    self.trades_cache = {'data': trades, 'timestamp': current_time}
                else:
                    trades = self.trades_cache['data']

                cutoff  = current_time - {"5m":timedelta(minutes=5),
                                          "15m":timedelta(minutes=15),
                                          "1h":timedelta(hours=1),
                                          "4h":timedelta(hours=4),
                                          "1d":timedelta(days=1)}[tf]
                recent  = [t for t in trades if datetime.fromtimestamp(t['timestamp']/1000) >= cutoff]
                cnt     = len(recent)

                # Cache the results
                self.data_cache[cache_key] = {
                    'pct': pct, 'high': high, 'low': low, 'vol': vol, 'cnt': cnt, 'timestamp': current_time
                }
            else:
                pct=high=low=vol=cnt=None

        # Update UI - batch all setText operations together
        self.tf_label.setText(tf)
        self.chg_label.setText(f"Δ%: {pct:+.2f}%" if pct is not None else "Δ%: N/A")
        self.high_label.setText(f"H: {high:.4f}" if high is not None else "H: N/A")
        self.low_label.setText(f"L: {low:.4f}" if low is not None else "L: N/A")
        self.vol_label.setText(f"Vol: {vol:.2f}" if vol is not None else "Vol: N/A")
        self.cnt_label.setText(f"Trd: {cnt}" if cnt is not None else "Trd: N/A")

    def update_fade(self, widget):
        eff = QGraphicsOpacityEffect(widget)
        widget.setGraphicsEffect(eff)
        anim = QPropertyAnimation(eff, b"opacity", self)
        anim.setDuration(300)
        anim.setStartValue(0.2)
        anim.setEndValue(1.0)
        anim.start(QPropertyAnimation.DeleteWhenStopped)

# Helper functions for fetching positions and orders
def fetch_open_positions(symbol=None, force_refresh=False):
    """Fetch open positions with error handling and caching"""
    try:
        global position_cache
        current_time = time.time()
        cache_age = current_time - position_cache['timestamp']

        # Check if we can use cached data
        if not force_refresh and cache_age < position_cache['ttl'] and position_cache['data']:
            # print(f"POSITION API: Using cached positions (age: {cache_age:.2f}s)")
            positions = position_cache['data']

            # Filter by symbol if provided
            if symbol and positions:
                filtered = []
                for pos in positions:
                    pos_symbol = pos.get('symbol', '')
                    # Match exact symbol or base symbol (before the colon)
                    if pos_symbol == symbol or (
                        ':' in pos_symbol and ':' in symbol and
                        pos_symbol.split(':')[0] == symbol.split(':')[0]
                    ):
                        filtered.append(pos)
                # print(f"POSITION API: Returning {len(filtered)} cached positions for {symbol}")
                return filtered

            return positions

        # print(f"POSITION API: Cache expired or force refresh requested, fetching fresh data")
        # print(f"POSITION API: fetch_open_positions called with symbol={symbol}, demo_mode={demo_mode}")

        if demo_mode:
            # Generate demo positions
            print("POSITION API: Using demo positions")
            demo_positions = [
                {
                    "symbol": "BTC/USDT",
                    "side": "long",
                    "contracts": 0.01,
                    "contractSize": 1,
                    "entryPrice": 50000,
                    "markPrice": 51000,
                    "unrealizedPnl": 10,
                    "leverage": 10,
                    "liquidationPrice": 45000,
                    "collateral": 500,
                    "notional": 50000 * 0.01,
                    "timestamp": datetime.now().timestamp() * 1000,
                    "datetime": datetime.now().isoformat()
                },
                {
                    "symbol": "ETH/USDT",
                    "side": "short",
                    "contracts": 0.1,
                    "contractSize": 1,
                    "entryPrice": 3000,
                    "markPrice": 2950,
                    "unrealizedPnl": 5,
                    "leverage": 10,
                    "liquidationPrice": 3300,
                    "collateral": 300,
                    "notional": 3000 * 0.1,
                    "timestamp": datetime.now().timestamp() * 1000,
                    "datetime": datetime.now().isoformat()
                }
            ]

            # Update cache
            position_cache['data'] = demo_positions
            position_cache['timestamp'] = current_time

            # Filter by symbol if provided
            if symbol:
                result = [pos for pos in demo_positions if (
                    pos['symbol'] == symbol or
                    pos['symbol'].split(':')[0] == symbol.split(':')[0] if ':' in symbol else symbol
                )]
                # print(f"POSITION API: Returning {len(result)} demo positions for {symbol}")
                return result

            # print(f"POSITION API: Returning all {len(demo_positions)} demo positions")
            return demo_positions
        else:
            # REAL EXCHANGE MODE
            print("POSITION API: Using REAL exchange")

            # Check if exchange is None
            if exchange is None:
                print("POSITION API: Exchange is None, returning empty positions list")
                return []

            # Check if exchange has fetch_positions method
            if not hasattr(exchange, 'fetch_positions'):
                print("POSITION API: Exchange does not support fetch_positions, using demo positions")
                # Fall back to demo positions
                if symbol:
                    # Create a demo position for the specified symbol
                    demo_positions = [
                        {
                            'symbol': symbol,
                            'side': 'long',
                            'contracts': 10.0,
                            'contractSize': 1.0,
                            'entryPrice': 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 100.0,
                            'markPrice': 51000.0 if 'BTC' in symbol else 3050.0 if 'ETH' in symbol else 102.0,
                            'notional': 500000.0 if 'BTC' in symbol else 30000.0 if 'ETH' in symbol else 1000.0,
                            'leverage': 10.0,
                            'unrealizedPnl': 1000.0,
                            'liquidationPrice': 45000.0 if 'BTC' in symbol else 2700.0 if 'ETH' in symbol else 90.0,
                            'marginMode': 'cross',
                            'collateral': 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 100.0,
                            'initialMargin': 5000.0 if 'BTC' in symbol else 300.0 if 'ETH' in symbol else 10.0,
                            'maintenanceMargin': 2500.0 if 'BTC' in symbol else 150.0 if 'ETH' in symbol else 5.0,
                            'marginRatio': 0.05,
                            'timestamp': int(time.time() * 1000),
                            'datetime': datetime.datetime.now().isoformat(),
                            'info': {}
                        }
                    ]
                else:
                    # Create demo positions for common symbols
                    demo_positions = [
                        {
                            'symbol': 'BTC/USDT:USDT',
                            'side': 'long',
                            'contracts': 10.0,
                            'contractSize': 1.0,
                            'entryPrice': 50000.0,
                            'markPrice': 51000.0,
                            'notional': 500000.0,
                            'leverage': 10.0,
                            'unrealizedPnl': 1000.0,
                            'liquidationPrice': 45000.0,
                            'marginMode': 'cross',
                            'collateral': 50000.0,
                            'initialMargin': 5000.0,
                            'maintenanceMargin': 2500.0,
                            'marginRatio': 0.05,
                            'timestamp': int(time.time() * 1000),
                            'datetime': datetime.datetime.now().isoformat(),
                            'info': {}
                        },
                        {
                            'symbol': 'ETH/USDT:USDT',
                            'side': 'short',
                            'contracts': 20.0,
                            'contractSize': 1.0,
                            'entryPrice': 3000.0,
                            'markPrice': 2950.0,
                            'notional': 60000.0,
                            'leverage': 10.0,
                            'unrealizedPnl': 1000.0,
                            'liquidationPrice': 3300.0,
                            'marginMode': 'cross',
                            'collateral': 6000.0,
                            'initialMargin': 600.0,
                            'maintenanceMargin': 300.0,
                            'marginRatio': 0.05,
                            'timestamp': int(time.time() * 1000),
                            'datetime': datetime.datetime.now().isoformat(),
                            'info': {}
                        }
                    ]

                position_cache['data'] = demo_positions
                position_cache['timestamp'] = current_time
                return demo_positions

            # IMPORTANT: For Huobi, we should ALWAYS fetch ALL positions first
            # and then filter manually, as Huobi doesn't support fetching by symbol

            # Try different approaches to get positions
            try:
                # First try: fetch all positions without symbol filter
                print("POSITION API: Fetching ALL positions from exchange")
                all_positions = exchange.fetch_positions()
                print(f"POSITION API: Got {len(all_positions)} positions from exchange")

                # Filter out positions with zero contracts
                non_zero_positions = []
                for pos in all_positions:
                    try:
                        contracts = float(pos.get('contracts', 0))
                        if abs(contracts) > 0:
                            non_zero_positions.append(pos)
                            # print(f"POSITION API: Found position: {pos.get('symbol')}, {pos.get('side')}, {contracts}, entry: {pos.get('entryPrice')}")
                    except Exception as e:
                        print(f"POSITION API: Error processing position: {e}")

                # Update cache with non-zero positions
                position_cache['data'] = non_zero_positions
                position_cache['timestamp'] = current_time

                # print(f"POSITION API: Found {len(non_zero_positions)} non-zero positions")

                # Filter by symbol if provided
                if symbol and non_zero_positions:
                    filtered = []
                    for pos in non_zero_positions:
                        pos_symbol = pos.get('symbol', '')
                        # Match exact symbol or base symbol (before the colon)
                        if pos_symbol == symbol or (
                            ':' in pos_symbol and ':' in symbol and
                            pos_symbol.split(':')[0] == symbol.split(':')[0]
                        ):
                            filtered.append(pos)
                    print(f"POSITION API: Returning {len(filtered)} positions for {symbol}")
                    return filtered

                return non_zero_positions

            except Exception as e:
                print(f"POSITION API: Error fetching positions: {e}")
                return []
    except Exception as e:
        print(f"POSITION API: Critical error in fetch_open_positions: {e}")
        return []

def fetch_open_orders(symbol=None, force_refresh=False):
    """Fetch open orders with error handling and caching"""
    try:
        global order_cache
        current_time = time.time()

        # Check if we can use cached data for this symbol
        if not force_refresh and symbol in order_cache['data']:
            cache_age = current_time - order_cache['timestamp']
            if cache_age < order_cache['ttl']:
                print(f"ORDERS API: Using cached orders for {symbol} (age: {cache_age:.2f}s)")
                return order_cache['data'][symbol]

        # print(f"ORDERS API: Cache expired or force refresh requested, fetching fresh data for {symbol}")

        if demo_mode:
            # Return demo orders
            print(f"ORDERS API: Using demo orders for {symbol}")
            if symbol:
                # Create empty demo orders list
                orders = []
                # Update cache
                order_cache['data'][symbol] = orders
                order_cache['timestamp'] = current_time
                return orders
            return []

        # Fetch real orders from exchange
        # print(f"ORDERS API: Fetching real orders for {symbol}")
        orders = exchange.fetch_open_orders(symbol)
        # print(f"ORDERS API: Got {len(orders)} orders for {symbol}")

        # Update cache
        if symbol:  # Only cache if we have a specific symbol
            order_cache['data'][symbol] = orders
            order_cache['timestamp'] = current_time

        return orders
    except Exception as e:
        print(f"Error fetching open orders: {e}")
        return []

# Core trading functions
def fetch_ohlcv(symbol, timeframe, limit=100):
    """Fetch OHLCV data for a symbol or generate demo data if in demo mode"""
    try:
        # print(f"OHLCV API: Fetching {limit} candles for {symbol} with timeframe {timeframe}")

        if demo_mode:
            # Generate demo OHLCV data
            now = datetime.now()

            # Adjust timestamp interval based on timeframe
            if timeframe == '1m':
                interval = timedelta(minutes=1)
            elif timeframe == '5m':
                interval = timedelta(minutes=5)
            elif timeframe == '15m':
                interval = timedelta(minutes=15)
            elif timeframe == '1h':
                interval = timedelta(hours=1)
            elif timeframe == '4h':
                interval = timedelta(hours=4)
            elif timeframe == '1d':
                interval = timedelta(days=1)
            else:
                interval = timedelta(minutes=1)  # Default to 1m

            # Generate timestamps in reverse order (newest last)
            timestamps = [(now - interval * i).timestamp() * 1000 for i in range(limit, 0, -1)]

            # Generate random but realistic price data based on symbol
            if symbol.startswith('BTC'):
                base_price = 50000
                volatility = 100
            elif symbol.startswith('ETH'):
                base_price = 3000
                volatility = 20
            elif symbol.startswith('SOL'):
                base_price = 120
                volatility = 2
            elif symbol.startswith('DOGE'):
                base_price = 0.18
                volatility = 0.005
            elif symbol.startswith('XRP'):
                base_price = 0.55
                volatility = 0.01
            else:
                base_price = 100
                volatility = 1

            # Generate prices with some trend and volatility
            closes = []
            for i in range(limit):
                if i == 0:
                    closes.append(base_price)
                else:
                    # Add some random walk with mean reversion
                    change = random.uniform(-1, 1) * volatility * 0.01
                    mean_reversion = (base_price - closes[-1]) * 0.01
                    closes.append(closes[-1] * (1 + change + mean_reversion))

            # Generate OHLC from close prices
            ohlcv_data = []
            for i, timestamp in enumerate(timestamps):
                close = closes[i]

                # Generate high, low, open around close with realistic relationships
                # High is always >= close and open
                # Low is always <= close and open
                if i == 0:
                    open_price = close * (1 + random.uniform(-0.003, 0.003))
                else:
                    # Open is usually close to previous close
                    open_price = closes[i-1] * (1 + random.uniform(-0.002, 0.002))

                # Ensure high is the highest value
                high = max(close, open_price) * (1 + random.uniform(0, 0.005))

                # Ensure low is the lowest value
                low = min(close, open_price) * (1 - random.uniform(0, 0.005))

                # Generate realistic volume based on price movement
                price_change_pct = abs(close - open_price) / open_price
                volume = random.uniform(0.5, 5) * base_price * 0.01 * (1 + price_change_pct * 10)

                ohlcv_data.append([timestamp, open_price, high, low, close, volume])

            # print(f"OHLCV API: Generated {len(ohlcv_data)} demo candles for {symbol}")

            # Convert to DataFrame for easier manipulation
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Verify data integrity
            if df.isna().any().any():
                # print("OHLCV API: Warning - Demo data contains NaN values, filling with appropriate values")
                df = df.fillna(method='ffill')

            # Ensure high >= low (critical for candlestick display)
            invalid_rows = df['high'] < df['low']
            if invalid_rows.any():
                # print(f"OHLCV API: Warning - {invalid_rows.sum()} rows have high < low, fixing...")
                # Swap high and low where needed
                temp = df.loc[invalid_rows, 'high'].copy()
                df.loc[invalid_rows, 'high'] = df.loc[invalid_rows, 'low']
                df.loc[invalid_rows, 'low'] = temp

            return df

        elif exchange is not None:
            # Fetch real data with retry mechanism
            max_retries = 3
            retry_delay = 1  # seconds

            for attempt in range(max_retries):
                try:
                    # print(f"OHLCV API: Fetching real OHLCV data for {symbol}, attempt {attempt+1}/{max_retries}")
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)

                    if not ohlcv or len(ohlcv) == 0:
                        # print(f"OHLCV API: No data returned for {symbol}, attempt {attempt+1}/{max_retries}")
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                        else:
                            # print(f"OHLCV API: Failed to fetch data after {max_retries} attempts")
                            return None

                    # print(f"OHLCV API: Successfully fetched {len(ohlcv)} candles for {symbol}")

                    # Convert to DataFrame for easier manipulation
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

                    # Verify data integrity
                    if df.isna().any().any():
                        # print("OHLCV API: Warning - Real data contains NaN values, filling with appropriate values")
                        df = df.fillna(method='ffill')

                    # Ensure high >= low (critical for candlestick display)
                    invalid_rows = df['high'] < df['low']
                    if invalid_rows.any():
                        # print(f"OHLCV API: Warning - {invalid_rows.sum()} rows have high < low, fixing...")
                        # Swap high and low where needed
                        temp = df.loc[invalid_rows, 'high'].copy()
                        df.loc[invalid_rows, 'high'] = df.loc[invalid_rows, 'low']
                        df.loc[invalid_rows, 'low'] = temp

                    # Print some stats for debugging
                    # print(f"OHLCV API: Data stats - {len(df)} rows, timeframe {timeframe}")
                    # print(f"OHLCV API: Timestamp range: {df['timestamp'].min()} to {df['timestamp'].max()}")
                    # print(f"OHLCV API: Price range: {df['low'].min():.4f} to {df['high'].max():.4f}")

                    return df

                except Exception as e:
                    # print(f"OHLCV API: Error on attempt {attempt+1}/{max_retries}: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        print(f"OHLCV API: Failed after {max_retries} attempts")
                        return None
        else:
            print("OHLCV API: No exchange available")
            return None

    except Exception as e:
        print(f"OHLCV API: Critical error fetching OHLCV data: {e}")
        import traceback
        traceback.print_exc()
        return None

def fetch_trades(symbol, limit=500):
    """Fetch recent trades for a symbol or generate demo data if in demo mode"""
    try:
        print(f"Fetching {limit} trades for {symbol}...")
        if demo_mode:
            # Generate demo trade data
            now = datetime.now()
            data = []

            # Determine base price based on symbol
            if symbol.startswith('BTC'):
                base_price = 50000
                amount_range = (0.001, 0.1)
            elif symbol.startswith('ETH'):
                base_price = 3000
                amount_range = (0.01, 1.0)
            elif symbol.startswith('DOGE'):
                base_price = 0.1774
                amount_range = (100, 10000)
            else:
                base_price = 100
                amount_range = (0.1, 10)

            # Generate trades with realistic price movement
            price = base_price
            for i in range(limit):
                # Add some random walk to price
                price_change = random.uniform(-0.0005, 0.0005) * base_price
                price += price_change

                # Ensure price stays within reasonable bounds
                if price < base_price * 0.95 or price > base_price * 1.05:
                    price = base_price * (1 + random.uniform(-0.01, 0.01))

                timestamp = now - timedelta(seconds=limit-i)
                amount = random.uniform(*amount_range)
                side = "buy" if random.random() > 0.5 else "sell"

                data.append({
                    'timestamp': timestamp.timestamp() * 1000,
                    'datetime': timestamp.isoformat(),
                    'symbol': symbol,
                    'id': str(i),
                    'order': None,
                    'type': None,
                    'side': side,
                    'takerOrMaker': 'taker',
                    'price': price,
                    'amount': amount,
                    'cost': price * amount,
                    'fee': None
                })

            print(f"Generated {len(data)} demo trades for {symbol}")
            return data
        elif exchange is not None:
            # Fetch real trades
            try:
                trades = exchange.fetch_trades(symbol, limit=limit)
                print(f"Fetched {len(trades)} real trades for {symbol}")
                return trades
            except Exception as e:
                print(f"Error fetching trades: {e}")
                # Try alternative method
                try:
                    print("Trying alternative method to fetch trades...")
                    # Some exchanges use different methods
                    if hasattr(exchange, 'fetch_my_trades'):
                        my_trades = exchange.fetch_my_trades(symbol, limit=limit)
                        print(f"Fetched {len(my_trades)} personal trades for {symbol}")
                        return my_trades
                except Exception as nested_e:
                    print(f"Alternative method also failed: {nested_e}")
                    return []
        else:
            return []
    except Exception as e:
        print(f"Error in fetch_trades: {e}")
        return []

def fetch_best_bid(symbol):
    """Fetch the best bid price for a symbol"""
    try:
        order_book = fetch_order_book(symbol)
        if order_book and 'bids' in order_book and len(order_book['bids']) > 0:
            return order_book['bids'][0][0]  # First bid price
        return None
    except Exception as e:
        print(f"Error fetching best bid: {e}")
        return None

def fetch_best_ask(symbol):
    """Fetch the best ask price for a symbol"""
    try:
        order_book = fetch_order_book(symbol)
        if order_book and 'asks' in order_book and len(order_book['asks']) > 0:
            return order_book['asks'][0][0]  # First ask price
        return None
    except Exception as e:
        print(f"Error fetching best ask: {e}")
        return None

def fetch_ticker(symbol):
    """Fetch ticker for a single symbol to get 24h price change

    Args:
        symbol (str): Symbol to fetch ticker for.

    Returns:
        dict: Ticker data with 24h price change
    """
    try:
        if demo_mode:
            # Generate demo ticker data
            # Generate a realistic price based on the symbol
            if symbol.startswith('BTC'):
                price = random.uniform(49000, 51000)
                change = random.uniform(-5, 5)
            elif symbol.startswith('ETH'):
                price = random.uniform(2900, 3100)
                change = random.uniform(-7, 7)
            elif symbol.startswith('SOL'):
                price = random.uniform(115, 125)
                change = random.uniform(-10, 10)
            elif symbol.startswith('DOGE'):
                price = random.uniform(0.17, 0.19)
                change = random.uniform(-8, 8)
            elif symbol.startswith('XRP'):
                price = random.uniform(0.75, 0.85)
                change = random.uniform(-6, 6)
            elif symbol.startswith('ADA'):
                price = random.uniform(0.45, 0.55)
                change = random.uniform(-9, 9)
            else:
                price = random.uniform(100, 1000)
                change = random.uniform(-10, 10)

            ticker = {
                'symbol': symbol,
                'last': price,
                'percentage': change,
                'high': price * 1.05,
                'low': price * 0.95,
                'volume': random.uniform(1000000, 10000000),
                'timestamp': int(time.time() * 1000)
            }
            return ticker
        elif exchange is not None:
            # Fetch real ticker data
            try:
                ticker = exchange.fetch_ticker(symbol)
                return ticker
            except Exception as e:
                print(f"Error fetching ticker for {symbol}: {e}")
                return None
        else:
            return None
    except Exception as e:
        print(f"Error in fetch_ticker: {e}")
        return None

def fetch_tickers(symbols=None):
    """Fetch tickers for multiple symbols to get 24h price changes

    Args:
        symbols (list): List of symbols to fetch tickers for. If None, fetches all tickers.

    Returns:
        dict: Dictionary of symbol -> ticker data with 24h price change
    """
    try:
        if demo_mode:
            # Generate demo ticker data
            tickers = {}
            for symbol in symbols or ["BTC/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "DOGE/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT"]:
                # Generate a realistic price based on the symbol
                if symbol.startswith('BTC'):
                    price = random.uniform(49000, 51000)
                    change = random.uniform(-5, 5)
                elif symbol.startswith('ETH'):
                    price = random.uniform(2900, 3100)
                    change = random.uniform(-7, 7)
                elif symbol.startswith('SOL'):
                    price = random.uniform(115, 125)
                    change = random.uniform(-10, 10)
                elif symbol.startswith('DOGE'):
                    price = random.uniform(0.17, 0.19)
                    change = random.uniform(-8, 8)
                elif symbol.startswith('XRP'):
                    price = random.uniform(0.50, 0.60)
                    change = random.uniform(-6, 6)
                elif symbol.startswith('ADA'):
                    price = random.uniform(0.40, 0.50)
                    change = random.uniform(-9, 9)
                else:
                    price = random.uniform(90, 110)
                    change = random.uniform(-5, 5)

                tickers[symbol] = {
                    'symbol': symbol,
                    'last': price,
                    'percentage': change,
                    'change': price * (change / 100),
                    'timestamp': datetime.now().timestamp() * 1000
                }
            return tickers
        elif exchange is not None:
            # Fetch real ticker data
            if symbols:
                # Fetch specific symbols
                tickers = {}
                for symbol in symbols:
                    try:
                        ticker = exchange.fetch_ticker(symbol)
                        tickers[symbol] = ticker
                    except Exception as e:
                        print(f"Error fetching ticker for {symbol}: {e}")
                return tickers
            else:
                # Fetch all tickers
                return exchange.fetch_tickers()
        else:
            return {}
    except Exception as e:
        print(f"Error fetching tickers: {e}")
        return {}

def fetch_order_book(symbol, force_refresh=False):
    """Fetch order book for a symbol with caching"""
    try:
        global orderbook_cache
        current_time = time.time()

        # Check if we can use cached data for this symbol
        if not force_refresh and symbol in orderbook_cache['data']:
            cache_age = current_time - orderbook_cache['timestamp']
            if cache_age < orderbook_cache['ttl']:
                # print(f"ORDERBOOK API: Using cached order book for {symbol} (age: {cache_age:.2f}s)")
                return orderbook_cache['data'][symbol]

        # print(f"ORDERBOOK API: Cache expired or force refresh requested, fetching fresh data for {symbol}")

        if demo_mode:
            # Generate demo order book data
            # Get a base price for the symbol
            if symbol.startswith('BTC'):
                base_price = 50000
            elif symbol.startswith('ETH'):
                base_price = 3000
            elif symbol.startswith('SOL'):
                base_price = 120
            else:
                base_price = 100

            # Generate asks (sell orders) slightly above base price
            asks = []
            for i in range(10):
                price = base_price * (1 + 0.0001 * (i + 1))
                size = random.uniform(0.01, 1.0) * (10 - i) / 10
                asks.append([price, size])

            # Generate bids (buy orders) slightly below base price
            bids = []
            for i in range(10):
                price = base_price * (1 - 0.0001 * (i + 1))
                size = random.uniform(0.01, 1.0) * (10 - i) / 10
                bids.append([price, size])

            order_book = {
                'symbol': symbol,
                'asks': asks,
                'bids': bids,
                'timestamp': datetime.now().timestamp() * 1000,
                'datetime': datetime.now().isoformat(),
                'nonce': int(datetime.now().timestamp() * 1000)
            }

            # Update cache
            orderbook_cache['data'][symbol] = order_book
            orderbook_cache['timestamp'] = current_time

            return order_book
        elif exchange is not None:
            # Fetch real order book
            # print(f"ORDERBOOK API: Fetching real order book for {symbol}")
            order_book = exchange.fetch_order_book(symbol)

            # Update cache
            orderbook_cache['data'][symbol] = order_book
            orderbook_cache['timestamp'] = current_time

            # print(f"ORDERBOOK API: Fetched order book with {len(order_book.get('bids', []))} bids and {len(order_book.get('asks', []))} asks")
            return order_book
        else:
            print("ORDERBOOK API: No exchange available")
            return None
    except Exception as e:
        print(f"Error fetching order book: {e}")
        return None

def fetch_best_ask(symbol):
    """Fetch best ask price for a symbol"""
    try:
        order_book = fetch_order_book(symbol)
        if order_book and 'asks' in order_book and len(order_book['asks']) > 0:
            return order_book['asks'][0][0]
        return None
    except Exception as e:
        print(f"Error fetching best ask price: {e}")
        return None

def fetch_best_bid(symbol):
    """Fetch best bid price for a symbol"""
    try:
        order_book = fetch_order_book(symbol)
        if order_book and 'bids' in order_book and len(order_book['bids']) > 0:
            return order_book['bids'][0][0]
        return None
    except Exception as e:
        print(f"Error fetching best bid price: {e}")
        return None

def fetch_account_info():
    """Fetch account balance and equity information"""
    try:
        if exchange is None:
            return {
                "total_equity": "0.00",
                "free_balance": "0.00",
                "used_balance": "0.00"
            }

        # Fetch balance information
        balance = exchange.fetch_balance()

        # Extract relevant information
        total_equity = balance.get('total', {}).get('USDT', 0)
        free_balance = balance.get('free', {}).get('USDT', 0)
        used_balance = balance.get('used', {}).get('USDT', 0)

        # Format the values
        total_equity_formatted = f"${float(total_equity):,.2f}" if total_equity else "$0.00"
        free_balance_formatted = f"${float(free_balance):,.2f}" if free_balance else "$0.00"
        used_balance_formatted = f"${float(used_balance):,.2f}" if used_balance else "$0.00"

        return {
            "total_equity": total_equity_formatted,
            "free_balance": free_balance_formatted,
            "used_balance": used_balance_formatted
        }
    except Exception as e:
        print(f"Error fetching account info: {e}")
        return {
            "total_equity": "$0.00",
            "free_balance": "$0.00",
            "used_balance": "$0.00"
        }

def fetch_account_info(force_refresh=False):
    """Fetch account information with caching"""
    global account_cache, exchange
    try:
        current_time = time.time()
        cache_age = current_time - account_cache['timestamp']

        # Check if we can use cached data
        if not force_refresh and cache_age < account_cache['ttl'] and account_cache['data']:
            return account_cache['data']

        if demo_mode:
            # Generate demo account info
            equity = 1000.0
            used = 250.0
            free = equity - used
            account_info = {
                'equity': f"${equity:.2f}",
                'used_balance': f"${used:.2f}",
                'free_balance': f"${free:.2f}",
                'margin_ratio': 0.25,
                'status': 'active'
            }

            # Update cache
            account_cache['data'] = account_info
            account_cache['timestamp'] = current_time

            return account_info
        elif exchange is not None:
            # Fetch real account info (only log this once per refresh)
            if force_refresh:
                print("ACCOUNT API: Fetching real account info from exchange")
            try:
                # Check if exchange has fetch_balance method
                if not hasattr(exchange, 'fetch_balance'):
                    print("ACCOUNT API: Exchange does not support fetch_balance, using demo account info")
                    # Use demo account info
                    equity = 10000.0
                    used = 2500.0
                    free = equity - used
                    account_info = {
                        'equity': f"${equity:.2f}",
                        'used_balance': f"${used:.2f}",
                        'free_balance': f"${free:.2f}",
                        'margin_ratio': 0.25,
                        'status': 'active'
                    }
                else:
                    # Fetch real balance
                    balance = exchange.fetch_balance()
                    total = float(balance.get('total', {}).get('USDT', 0))
                    used = float(balance.get('used', {}).get('USDT', 0))
                    free = float(balance.get('free', {}).get('USDT', 0))

                    # If we got zeros, try to get values from other currencies
                    if total == 0 and used == 0 and free == 0:
                        print("ACCOUNT API: No USDT balance found, checking other currencies")
                        # Try to find any non-zero balance
                        for currency, amount in balance.get('total', {}).items():
                            if float(amount) > 0:
                                print(f"ACCOUNT API: Found non-zero balance for {currency}: {amount}")
                                total += float(amount)

                        for currency, amount in balance.get('used', {}).items():
                            if float(amount) > 0:
                                print(f"ACCOUNT API: Found non-zero used balance for {currency}: {amount}")
                                used += float(amount)

                        for currency, amount in balance.get('free', {}).items():
                            if float(amount) > 0:
                                print(f"ACCOUNT API: Found non-zero free balance for {currency}: {amount}")
                                free += float(amount)

                    # If we still have zeros, use demo values
                    if total == 0 and used == 0 and free == 0:
                        print("ACCOUNT API: No balance found, using demo values")
                        total = 10000.0
                        used = 2500.0
                        free = total - used

                    # Store the full balance data for the balance dialog
                    account_info = {
                        'equity': f"${total:.2f}",
                        'used_balance': f"${used:.2f}",
                        'free_balance': f"${free:.2f}",
                        'margin_ratio': used/total if total > 0 else 0,
                        'status': 'active',
                        'full_balance': {
                            'free': balance.get('free', {}),
                            'total': balance.get('total', {})
                        }
                    }
            except Exception as e:
                print(f"ACCOUNT API: Error fetching balance: {e}")
                # Use demo account info as fallback
                equity = 10000.0
                used = 2500.0
                free = equity - used
                account_info = {
                    'equity': f"${equity:.2f}",
                    'used_balance': f"${used:.2f}",
                    'free_balance': f"${free:.2f}",
                    'margin_ratio': 0.25,
                    'status': 'active'
                }

            # Update cache
            account_cache['data'] = account_info
            account_cache['timestamp'] = current_time

            if force_refresh:
                print(f"ACCOUNT API: Fetched account info - Equity: {account_info['equity']}, Free: {account_info['free_balance']}")
            return account_info
        else:
            # Check if we've already tried to create a fallback exchange in this session
            if not hasattr(fetch_account_info, 'fallback_attempted'):
                fetch_account_info.fallback_attempted = False
                fetch_account_info.fallback_attempt_count = 0
                fetch_account_info.last_attempt_time = 0

            # Only try to create a fallback exchange after multiple attempts and with a delay
            current_time = time.time()
            time_since_last_attempt = current_time - getattr(fetch_account_info, 'last_attempt_time', 0)

            # If this is the first few attempts or we haven't waited long enough, return empty data
            # This gives the real exchange time to initialize properly
            if fetch_account_info.fallback_attempt_count < 3 and time_since_last_attempt < 10:
                fetch_account_info.fallback_attempt_count += 1
                fetch_account_info.last_attempt_time = current_time
                print(f"ACCOUNT API: Waiting for exchange to initialize (attempt {fetch_account_info.fallback_attempt_count}/3)")

                # Return empty data but don't create a fallback exchange yet
                account_info = {
                    'equity': "$0.00",
                    'used_balance': "$0.00",
                    'free_balance': "$0.00",
                    'margin_ratio': 0,
                    'status': 'initializing'
                }

                # Update cache with a short TTL to try again soon
                account_cache['data'] = account_info
                account_cache['timestamp'] = current_time
                account_cache['ttl'] = 5  # Short TTL to retry soon

                return account_info

            # After multiple attempts, try to create a fallback exchange
            if not fetch_account_info.fallback_attempted:
                print("ACCOUNT API: Exchange initialization timeout, creating fallback exchange")
                fetch_account_info.fallback_attempted = True

                # Try to create a fallback exchange
                try:
                    # We already declared exchange as global at the function start
                    exchange = ccxt.huobi({
                        'apiKey': 'demo_api_key',
                        'secret': 'demo_secret_key',
                        'enableRateLimit': True,
                        'options': {
                            'defaultType': 'swap',
                        }
                    })

                    # Try to load markets
                    try:
                        print("ACCOUNT API: Loading markets with fallback exchange...")
                        exchange.load_markets()
                        print("ACCOUNT API: Markets loaded successfully with fallback exchange")

                        # Now try to fetch balance with the fallback exchange
                        try:
                            print("ACCOUNT API: Fetching balance with fallback exchange...")
                            balance = exchange.fetch_balance()
                            total = float(balance.get('total', {}).get('USDT', 0))
                            used = float(balance.get('used', {}).get('USDT', 0))
                            free = float(balance.get('free', {}).get('USDT', 0))

                            # If we got zeros, use demo values
                            if total == 0 and used == 0 and free == 0:
                                print("ACCOUNT API: No balance found, using demo values")
                                total = 25000.0
                                used = 7500.0
                                free = total - used

                            account_info = {
                                'equity': f"${total:.2f}",
                                'used_balance': f"${used:.2f}",
                                'free_balance': f"${free:.2f}",
                                'margin_ratio': used/total if total > 0 else 0,
                                'status': 'active',
                                'full_balance': {
                                    'free': balance.get('free', {}),
                                    'total': balance.get('total', {})
                                }
                            }

                            # Update cache
                            account_cache['data'] = account_info
                            account_cache['timestamp'] = current_time

                            print("ACCOUNT API: Successfully fetched account info with fallback exchange")
                            return account_info
                        except Exception as e:
                            print(f"ACCOUNT API: Error fetching balance with fallback exchange: {e}")
                    except Exception as e:
                        print(f"ACCOUNT API: Error loading markets with fallback exchange: {e}")
                except Exception as e:
                    print(f"ACCOUNT API: Error creating fallback exchange: {e}")
            else:
                print("ACCOUNT API: Using cached demo values (fallback already attempted)")

            # If all fallback attempts fail, use demo values
            print("ACCOUNT API: Using demo account values")
            equity = 25000.0
            used = 7500.0
            free = equity - used
            account_info = {
                'equity': f"${equity:.2f}",
                'used_balance': f"${used:.2f}",
                'free_balance': f"${free:.2f}",
                'margin_ratio': used/equity,
                'status': 'active',
                'full_balance': {
                    'free': {'USDT': free},
                    'total': {'USDT': equity}
                }
            }

            # Update cache
            account_cache['data'] = account_info
            account_cache['timestamp'] = current_time

            return account_info
    except Exception as e:
        print(f"Error fetching account info: {e}")
        return {
            'equity': "$0.00",
            'used_balance': "$0.00",
            'free_balance': "$0.00",
            'margin_ratio': 0,
            'status': 'error'
        }

def fetch_open_orders(symbol=None):
    """Fetch open orders or generate demo orders if in demo mode"""
    try:
        # print(f"Fetching open orders for symbol: {symbol}, demo_mode: {demo_mode}")
        if demo_mode:
            # Generate demo orders
            if symbol is None:
                print("No symbol provided for demo orders")
                return []

            # Get current price from order book
            ob = fetch_order_book(symbol)
            if not ob:
                print("Could not fetch order book for demo orders")
                return []

            best_bid = ob['bids'][0][0] if ob['bids'] else 0
            best_ask = ob['asks'][0][0] if ob['asks'] else 0

            # Create demo orders - make them more visible with larger sizes
            demo_orders = [
                {"symbol": symbol, "side": "buy",  "amount": 5.0, "price": best_bid * 0.99, "status": "open"},
                {"symbol": symbol, "side": "sell", "amount": 3.0, "price": best_ask * 1.01, "status": "open"},
            ]
            # print(f"Created {len(demo_orders)} demo orders: {demo_orders}")
            return demo_orders
        elif exchange is not None:
            # Fetch real open orders
            orders = exchange.fetch_open_orders(symbol)
            # print(f"Fetched {len(orders)} real orders from exchange")
            return orders
        else:
            print("No exchange available")
            return []
    except Exception as e:
        print(f"Error fetching open orders: {e}")
        return []

def fetch_positions(symbol=None):
    """Fetch open positions or generate demo positions if in demo mode"""
    try:
        # Use position cache to reduce duplicate calls
        global position_cache
        current_time = time.time()
        cache_key = f"positions_{symbol if symbol else 'all'}"

        # Check if we have cached positions for this symbol
        if cache_key in position_cache and current_time - position_cache[cache_key]['timestamp'] < position_cache['ttl']:
            # Use cached positions
            return position_cache[cache_key]['data']

        # Only log this for specific symbol requests to reduce noise
        if symbol:
            print(f"POSITION API: fetch_positions called with symbol={symbol}")

        if demo_mode:
            # Generate demo positions
            print("POSITION API: Using demo positions")
            demo_positions = [
                {
                    "symbol": "BTC/USDT",
                    "side": "long",
                    "contracts": 0.01,
                    "contractSize": 1,
                    "entryPrice": 50000,
                    "markPrice": 51000,
                    "unrealizedPnl": 10,
                    "leverage": 10,
                    "liquidationPrice": 45000,
                    "collateral": 500,
                    "notional": 50000 * 0.01,
                    "timestamp": datetime.now().timestamp() * 1000,
                    "datetime": datetime.now().isoformat()
                },
                {
                    "symbol": "ETH/USDT",
                    "side": "short",
                    "contracts": 0.1,
                    "contractSize": 1,
                    "entryPrice": 3000,
                    "markPrice": 2950,
                    "unrealizedPnl": 5,
                    "leverage": 10,
                    "liquidationPrice": 3300,
                    "collateral": 300,
                    "notional": 3000 * 0.1,
                    "timestamp": datetime.now().timestamp() * 1000,
                    "datetime": datetime.now().isoformat()
                }
            ]

            # Filter by symbol if provided
            if symbol:
                result = [pos for pos in demo_positions if (
                    pos['symbol'] == symbol or
                    pos['symbol'].split(':')[0] == symbol.split(':')[0] if ':' in symbol else symbol
                )]

                # Cache the result
                position_cache[cache_key] = {
                    'data': result,
                    'timestamp': current_time
                }

                return result

            # Cache the result
            position_cache[cache_key] = {
                'data': demo_positions,
                'timestamp': current_time
            }

            return demo_positions

        elif exchange is not None:
            # REAL EXCHANGE MODE
            print("POSITION API: Using REAL exchange")

            # Validate symbol before fetching positions
            if symbol is not None:
                # Check if symbol is valid (not just 'B' or other invalid symbols)
                if len(symbol) < 3 or '/' not in symbol:
                    print(f"POSITION API: Skipping invalid symbol: {symbol}")
                    return []

            # Try different approaches to get positions
            try:
                # First try: fetch all positions
                all_positions = exchange.fetch_positions()
                print(f"POSITION API: Got {len(all_positions)} positions from exchange")

                # Filter out positions with zero contracts
                non_zero_positions = []
                for pos in all_positions:
                    try:
                        contracts = float(pos.get('contracts', 0))
                        if abs(contracts) > 0:
                            non_zero_positions.append(pos)
                    except Exception as e:
                        print(f"POSITION API: Error processing position: {e}")

                # If we have a symbol filter, apply it
                if symbol:
                    filtered_positions = []
                    for pos in non_zero_positions:
                        pos_symbol = pos.get('symbol', '')
                        # Try different matching approaches
                        if (pos_symbol == symbol or
                            pos_symbol.split(':')[0] == symbol.split(':')[0] if ':' in symbol and ':' in pos_symbol else False or
                            symbol in pos_symbol):
                            filtered_positions.append(pos)

                    if filtered_positions:
                        print(f"POSITION API: Returning {len(filtered_positions)} positions for {symbol}")

                    # Cache the result
                    position_cache[cache_key] = {
                        'data': filtered_positions,
                        'timestamp': current_time
                    }

                    return filtered_positions

                # Cache the result
                position_cache[cache_key] = {
                    'data': non_zero_positions,
                    'timestamp': current_time
                }

                return non_zero_positions

            except Exception as e:
                print(f"POSITION API: Error fetching positions: {e}")
                # Try a different approach if the first one fails
                try:
                    print("POSITION API: Trying alternative method to fetch positions")
                    # Some exchanges use different methods
                    if hasattr(exchange, 'fetch_my_trades'):
                        # Fetch recent trades but don't store if not used
                        _ = exchange.fetch_my_trades(symbol, limit=10)

                    # Try to get balance which might include position info
                    balance = exchange.fetch_balance()
                    if 'info' in balance and 'positions' in balance['info']:
                        positions = balance['info']['positions']
                        print(f"POSITION API: Found {len(positions)} positions in balance info")

                        # Cache the result
                        position_cache[cache_key] = {
                            'data': positions,
                            'timestamp': current_time
                        }

                        return positions
                except Exception as nested_e:
                    print(f"POSITION API: Alternative method also failed: {nested_e}")

            # If all else fails, return empty list
            empty_result = []

            # Cache the empty result
            position_cache[cache_key] = {
                'data': empty_result,
                'timestamp': current_time
            }

            return empty_result
        else:
            print("POSITION API: Exchange is None, returning empty positions list")
            empty_result = []

            # Cache the empty result
            position_cache[cache_key] = {
                'data': empty_result,
                'timestamp': current_time
            }

            return empty_result
    except Exception as e:
        print(f"POSITION API: Critical error in fetch_positions: {e}")
        return []

def fetch_trades(symbol, since=None, limit=100):
    """Fetch recent trades or generate demo trades if in demo mode"""
    try:
        if demo_mode:
            # Generate demo trades
            current_time = datetime.now()
            demo_trades = []

            # Get a base price for the symbol
            if symbol.startswith('BTC'):
                base_price = 50000
            elif symbol.startswith('ETH'):
                base_price = 3000
            elif symbol.startswith('SOL'):
                base_price = 120
            else:
                base_price = 100

            # Generate trades with some price variation
            for i in range(limit):
                # Alternate buy/sell trades
                side = 'buy' if i % 2 == 0 else 'sell'

                # Vary price slightly
                price_change = random.uniform(-0.002, 0.002)
                price = base_price * (1 + price_change)

                # Vary amount
                amount = random.uniform(0.001, 0.1) if base_price > 1000 else random.uniform(0.1, 10)

                # Create trade timestamp (older to newer)
                trade_time = current_time - timedelta(minutes=limit-i)

                demo_trades.append({
                    'id': f"demo-trade-{i}",
                    'symbol': symbol,
                    'side': side,
                    'amount': amount,
                    'price': price,
                    'timestamp': trade_time.timestamp() * 1000,
                    'datetime': trade_time.isoformat(),
                    'fee': {'cost': amount * price * 0.001, 'currency': 'USDT'},
                    'type': 'limit',
                    'cost': amount * price
                })

            return demo_trades
        elif exchange is not None:
            # Fetch real trades
            trades = exchange.fetch_trades(symbol, since=since, limit=limit)
            return trades
        else:
            return []
    except Exception as e:
        print(f"Error fetching trades: {e}")
        return []

def place_limit_order(symbol, side, amount, price, params={}):
    """Place a limit order or simulate it in demo mode"""
    try:
        if demo_mode:
            # Simulate a limit order in demo mode
            order_id = f"demo-{int(datetime.now().timestamp() * 1000)}"
            order = {
                'id': order_id,
                'symbol': symbol,
                'type': 'limit',
                'side': side,
                'amount': float(amount),
                'price': float(price),
                'status': 'open',
                'timestamp': datetime.now().timestamp() * 1000,
                'datetime': datetime.now().isoformat(),
                'filled': 0,
                'remaining': float(amount),
                'fee': {'cost': 0, 'currency': 'USDT'},
                'info': {'demo': True}
            }
            print(f"Demo mode: Placed limit {side} order for {amount} {symbol} at {price}")
            return order
        elif exchange is not None:
            # Place a real limit order using the appropriate method
            if side == 'buy':
                order = exchange.create_limit_buy_order(symbol, amount, price, params)
            else:  # side == 'sell'
                order = exchange.create_limit_sell_order(symbol, amount, price, params)
            return order
        else:
            return None
    except Exception as e:
        print(f"Error placing limit order: {e}")
        return None

def place_stop_order(symbol, side, amount, stop_price, params={}):
    """Place a stop-market (stopLoss) order or simulate in demo."""
    try:
        if demo_mode:
            print(f"Demo: Stop-market {side} {amount}@{stop_price}")
            order_id = f"demo-stop-{int(datetime.now().timestamp() * 1000)}"
            order = {
                'id': order_id,
                'symbol': symbol,
                'type': 'stop',
                'side': side,
                'amount': float(amount),
                'stopPrice': float(stop_price),
                'status': 'open',
                'timestamp': datetime.now().timestamp() * 1000,
                'datetime': datetime.now().isoformat(),
                'filled': 0,
                'remaining': float(amount),
                'fee': {'cost': 0, 'currency': 'USDT'},
                'info': {'demo': True}
            }
            return order
        else:
            # First, verify that the position exists and has sufficient size
            position_side = 'long' if side == 'sell' else 'short'
            positions = fetch_open_positions(symbol)

            # Filter positions by the correct side
            matching_positions = [p for p in positions if p.get('side') == position_side]

            if not matching_positions:
                print(f"Warning: No {position_side} position found for {symbol} to place stop order")
                return None

            # Get the actual position size
            position_size = abs(float(matching_positions[0].get('contracts', 0)))

            if position_size < amount:
                print(f"Warning: Position size ({position_size}) is less than requested stop order size ({amount})")
                # Adjust the amount to match the position size
                amount = position_size
                if amount <= 0:
                    print(f"Error: Cannot place stop order with zero size")
                    return None
                print(f"Adjusted stop order size to {amount}")

            # CCXT: create_order(symbol, type, side, amount, price, params)
            # price=None for market, params must include stopPrice
            p = {'stopPrice': stop_price, 'reduceOnly': True, 'offset': 'close'}
            p.update(params)

            # For Huobi/HTX, we need to use a limit order with stop price instead of a stop market order
            # This helps avoid the "Insufficient open positions" error
            try:
                print(f"Placing stop order: {symbol}, {side}, {amount}, {stop_price}")
                return exchange.create_order(symbol, 'stop', side, amount, None, p)
            except Exception as e:
                if "Insufficient open positions" in str(e):
                    print(f"Error: Insufficient open positions. Trying with limit order...")
                    # Try with a limit order at the stop price instead
                    p['offset'] = 'close'
                    return place_limit_order(symbol, side, amount, stop_price, p)
                else:
                    raise  # Re-raise if it's a different error
    except Exception as e:
        print(f"Error placing stop order: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return None

def place_market_order(symbol, side, amount, params={}):
    """Place a market order or simulate it in demo mode"""
    try:
        if demo_mode:
            # Get current price from order book
            price = fetch_best_ask(symbol) if side == 'buy' else fetch_best_bid(symbol)
            if price is None:
                price = 50000 if symbol.startswith('BTC') else 3000 if symbol.startswith('ETH') else 100

            # Simulate a market order in demo mode
            order_id = f"demo-{int(datetime.now().timestamp() * 1000)}"
            order = {
                'id': order_id,
                'symbol': symbol,
                'type': 'market',
                'side': side,
                'amount': float(amount),
                'price': float(price),  # Estimated execution price
                'status': 'closed',  # Market orders are filled immediately
                'timestamp': datetime.now().timestamp() * 1000,
                'datetime': datetime.now().isoformat(),
                'filled': float(amount),
                'remaining': 0,
                'fee': {'cost': float(amount) * float(price) * 0.001, 'currency': 'USDT'},
                'info': {'demo': True}
            }
            print(f"Demo mode: Executed market {side} order for {amount} {symbol} at ~{price}")
            return order
        elif exchange is not None:
            # Place a real market order using the appropriate method
            if side == 'buy':
                order = exchange.create_market_buy_order(symbol, amount, params)
            else:  # side == 'sell'
                order = exchange.create_market_sell_order(symbol, amount, params)
            return order
        else:
            return None
    except Exception as e:
        print(f"Error placing market order: {e}")
        return None

def cancel_order(order_id, symbol=None):
    """Cancel a specific order by ID"""
    try:
        if demo_mode:
            # Simulate cancelling an order in demo mode
            print(f"Demo mode: Cancelled order {order_id} for {symbol if symbol else 'unknown symbol'}")
            return True
        elif exchange is not None:
            # Cancel a real order
            if symbol:
                exchange.cancel_order(order_id, symbol)
            else:
                # Try to find the symbol for this order
                orders = []
                positions = fetch_open_positions()
                symbols = {p['symbol'] for p in positions}
                for sym in symbols:
                    sym_orders = fetch_open_orders(sym)
                    orders.extend(sym_orders)

                # Find the order with matching ID
                matching_order = next((o for o in orders if o.get('id', '') == order_id), None)
                if matching_order:
                    exchange.cancel_order(order_id, matching_order.get('symbol'))
                else:
                    print(f"Could not find order with ID {order_id}")
                    return False
            return True
        else:
            return False
    except Exception as e:
        print(f"Error cancelling order {order_id}: {e}")
        return False

def cancel_all_orders(symbol=None):
    """Cancel all open orders regardless of symbol"""
    try:
        if demo_mode:
            # Simulate cancelling orders in demo mode
            print(f"Demo mode: Cancelled all orders{' for ' + symbol if symbol else ''}")
            return True
        elif exchange is not None:
            # Cancel real orders
            if symbol:
                # Try to use the exchange's cancelAllOrders method if available
                try:
                    print(f"Attempting to cancel all orders for {symbol} using exchange method")
                    exchange.cancel_all_orders(symbol)
                    print(f"Successfully cancelled all orders for {symbol}")
                except Exception as e:
                    # Fallback to manual cancellation if the method is not supported
                    print(f"Direct cancel_all_orders failed: {e}, falling back to manual cancellation")
                    orders = fetch_open_orders(symbol)
                    for o in orders:
                        exchange.cancel_order(o['id'], symbol)
            else:
                # Try to get all symbols with open orders
                try:
                    # Try to use cancelAllOrders for all symbols at once if supported
                    try:
                        print("Attempting to cancel all orders across all symbols")
                        exchange.cancel_all_orders()
                        print("Successfully cancelled all orders across all symbols")
                    except Exception as e:
                        print(f"Global cancel_all_orders failed: {e}, cancelling by symbol")

                        # Get all markets (for debugging purposes)
                        # markets = list(exchange.markets.keys())

                        # Fetch all open orders across all symbols
                        all_orders = []
                        try:
                            # Try to fetch all open orders at once if supported
                            all_orders = exchange.fetch_open_orders()
                            symbols_with_orders = {order['symbol'] for order in all_orders}
                        except Exception:
                            # If not supported, we'll need to check common symbols
                            # Start with current symbol
                            current_symbol = symbol or default_symbol
                            symbols_to_check = [current_symbol]

                            # Add symbols from positions as they likely have orders
                            positions = fetch_open_positions(force_refresh=True)
                            symbols_to_check.extend([p['symbol'] for p in positions])

                            # Make the list unique
                            symbols_to_check = list(set(symbols_to_check))

                            # Check each symbol for open orders
                            symbols_with_orders = set()
                            for sym in symbols_to_check:
                                orders = fetch_open_orders(sym)
                                if orders:
                                    symbols_with_orders.add(sym)
                                    all_orders.extend(orders)

                        # Cancel orders for each symbol with open orders
                        for sym in symbols_with_orders:
                            try:
                                exchange.cancel_all_orders(sym)
                                print(f"Cancelled all orders for {sym}")
                            except Exception as e2:
                                # Fallback to manual cancellation
                                print(f"Cancel all for {sym} failed: {e2}, using manual cancellation")
                                # Get orders for this symbol from our all_orders list
                                symbol_orders = [o for o in all_orders if o.get('symbol') == sym]
                                for o in symbol_orders:
                                    try:
                                        exchange.cancel_order(o['id'], sym)
                                        print(f"Cancelled order {o['id']} for {sym}")
                                    except Exception as e3:
                                        print(f"Failed to cancel order {o['id']}: {e3}")
                except Exception as e:
                    print(f"Error in cancel_all_orders: {e}, falling back to basic implementation")
                    # Fallback to the original implementation - check current symbol
                    current_symbol = symbol or default_symbol
                    orders = fetch_open_orders(current_symbol)
                    for o in orders:
                        try:
                            exchange.cancel_order(o['id'], current_symbol)
                        except Exception as e2:
                            print(f"Failed to cancel order {o['id']}: {e2}")
            return True
        else:
            return False
    except Exception as e:
        print(f"Error cancelling orders: {e}")
        return False

# Function to set leverage
def set_leverage(symbol, leverage, margin_mode='cross'):
    """Set leverage for the given symbol"""
    if demo_mode:
        print(f"DEMO: Setting leverage to {leverage}x for {symbol}")
        return True

    try:
        if not exchange:
            print("Exchange not initialized")
            return False

        # Set leverage
        result = exchange.set_leverage(leverage, symbol, params={'marginMode': margin_mode})
        print(f"Set leverage to {leverage}x for {symbol}: {result}")
        return True

    except Exception as e:
        print(f"Error setting leverage: {e}")
        return False

def close_position(symbol, position_side=None):
    """Close an open position or simulate it in demo mode"""
    try:
        if demo_mode:
            # Simulate closing a position in demo mode
            print(f"Demo mode: Closed {position_side if position_side else 'all'} position(s) for {symbol}")
            return True
        elif exchange is not None:
            # Close real position
            positions = fetch_open_positions(symbol, force_refresh=True)

            if not positions:
                print(f"No open positions found for {symbol}")
                return False

            success = False
            for p in positions:
                if p['symbol'] == symbol and (position_side is None or p['side'] == position_side):
                    side = 'sell' if p['side'] == 'long' else 'buy'
                    amount = abs(float(p.get('contracts', 0)))
                    if amount > 0:
                        print(f"Closing position: {symbol} {p['side']} {amount}")
                        try:
                            # First try with market order
                            params = {'offset': 'close', 'reduceOnly': True}
                            order = place_market_order(symbol, side, amount, params)
                            if order:
                                print(f"Successfully closed position with market order")
                                success = True
                            else:
                                # If market order fails, try with limit order at current price
                                price = fetch_best_bid(symbol) if side == 'buy' else fetch_best_ask(symbol)
                                if price:
                                    print(f"Market order failed, trying limit order at {price}")
                                    order = place_limit_order(symbol, side, amount, price, params)
                                    if order:
                                        print(f"Successfully placed limit order to close position")
                                        success = True
                        except Exception as e:
                            print(f"Error closing position with market order: {e}")
                            print(f"Traceback: {traceback.format_exc()}")
                            # Try with limit order as fallback
                            try:
                                price = fetch_best_bid(symbol) if side == 'buy' else fetch_best_ask(symbol)
                                if price:
                                    print(f"Trying limit order at {price} as fallback")
                                    params = {'offset': 'close', 'reduceOnly': True}
                                    order = place_limit_order(symbol, side, amount, price, params)
                                    if order:
                                        print(f"Successfully placed limit order to close position")
                                        success = True
                            except Exception as nested_e:
                                print(f"Error placing fallback limit order: {nested_e}")

            return success
        else:
            return False
    except Exception as e:
        print(f"Error closing position: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def close_all_positions():
    """Close all open positions or simulate it in demo mode"""
    try:
        if demo_mode:
            # Simulate closing all positions in demo mode
            print("Demo mode: Closed all positions")
            return True
        elif exchange is not None:
            # Close real positions
            positions = fetch_open_positions(force_refresh=True)

            if not positions:
                print("No open positions found")
                return True

            success = True
            # Group positions by symbol
            symbols = set(p['symbol'] for p in positions)

            for symbol in symbols:
                # Use our improved close_position function for each symbol
                result = close_position(symbol)
                if not result:
                    print(f"Warning: Failed to close positions for {symbol}")
                    success = False

            return success
        else:
            return False
    except Exception as e:
        print(f"Error closing all positions: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def set_leverage(symbol, leverage):
    """Set leverage for a symbol or simulate it in demo mode"""
    try:
        if demo_mode:
            # Simulate setting leverage in demo mode
            print(f"Demo mode: Set leverage to {leverage}x for {symbol}")
            return True
        elif exchange is not None:
            # Set real leverage
            exchange.set_leverage(leverage, symbol)
            return True
        else:
            return False
    except Exception as e:
        print(f"Error setting leverage: {e}")
        return False

# Technical analysis functions
def calculate_ema(df, column='close', span=20):
    """Calculate Exponential Moving Average"""
    return df[column].ewm(span=span, adjust=False).mean()

def calculate_sma(df, column='close', window=20):
    """Calculate Simple Moving Average"""
    return df[column].rolling(window=window).mean()

def calculate_rsi(df, column='close', window=14):
    """Calculate Relative Strength Index"""
    delta = df[column].diff()
    gain = delta.where(delta > 0, 0).rolling(window=window).mean()
    loss = -delta.where(delta < 0, 0).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def calculate_macd(df, column='close', fast=12, slow=26, signal=9):
    """Calculate MACD"""
    ema_fast = df[column].ewm(span=fast, adjust=False).mean()
    ema_slow = df[column].ewm(span=slow, adjust=False).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal, adjust=False).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def calculate_bollinger_bands(df, column='close', window=20, num_std=2):
    """Calculate Bollinger Bands"""
    sma = calculate_sma(df, column, window)
    std = df[column].rolling(window=window).std()
    upper_band = sma + (std * num_std)
    lower_band = sma - (std * num_std)
    return sma, upper_band, lower_band

def calculate_atr(df, period=14):
    """Calculate Average True Range (ATR)"""
    high = df['high']
    low = df['low']
    close = df['close'].shift(1)

    # Calculate True Range
    tr1 = high - low
    tr2 = abs(high - close)
    tr3 = abs(low - close)
    tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)

    # Calculate ATR
    atr = tr.rolling(window=period).mean()
    return atr

def calculate_atr_ema_bands(df, ema_period=20, atr_period=14, multipliers=[1, 2, 3]):
    """Calculate ATR-EMA Bands

    Args:
        df (pd.DataFrame): DataFrame with OHLCV data
        ema_period (int): EMA period
        atr_period (int): ATR period
        multipliers (list): List of multipliers for ATR bands

    Returns:
        dict: Dictionary containing EMA and bands
    """
    # Calculate EMA
    ema = df['close'].ewm(span=ema_period, adjust=False).mean()

    # Calculate ATR
    atr = calculate_atr(df, atr_period)

    # Initialize bands dictionary
    bands = {
        'ema': ema,
        'atr': atr
    }

    # Calculate upper and lower bands for each multiplier
    for mult in multipliers:
        bands[f'ema_plus_{mult}_atr'] = ema + (atr * mult)
        bands[f'ema_minus_{mult}_atr'] = ema - (atr * mult)

    return bands

# Thread management functions
def start_thread(task, target_func, *args, **kwargs):
    """Start a background thread for a specific task"""
    global epinnox_thread_flag, pnl_perc_thread_flag, auto_transfer_thread_flag, auto_audit_thread_flag, auto_advice_thread_flag

    if task == "epinnox" and not epinnox_thread_flag:
        epinnox_thread_flag = True
        thread = Thread(target=target_func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    elif task == "pnl" and not pnl_perc_thread_flag:
        pnl_perc_thread_flag = True
        thread = Thread(target=target_func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    elif task == "auto_transfer" and not auto_transfer_thread_flag:
        auto_transfer_thread_flag = True
        thread = Thread(target=target_func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    elif task == "auto_audit" and not auto_audit_thread_flag:
        auto_audit_thread_flag = True
        thread = Thread(target=target_func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    elif task == "auto_advice" and not auto_advice_thread_flag:
        auto_advice_thread_flag = True
        thread = Thread(target=target_func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
        return thread
    return None

class MiniWidgetsPanel(QGroupBox):
    def __init__(self, parent=None):
        super().__init__("Quick Metrics", parent)
        self.setStyleSheet("QGroupBox{color:#ccc;}")
        self.parent = parent

        # Widget titles for the subtitle
        self.widget_titles = ["Depth", "Margin", "PnL", "Spread"]
        self.title_label = QLabel(self.widget_titles[0])
        self.title_label.setStyleSheet("color: #00ff44; font-size: 10px; font-style: italic;")
        self.title_label.setAlignment(Qt.AlignCenter)

        # 1) Prev/Next buttons with chevron icons
        btn_prev = QPushButton("❮")  # Unicode chevron left
        btn_next = QPushButton("❯")  # Unicode chevron right

        # Style the buttons to look more like app-native controls
        button_style = """
            QPushButton {
                background-color: #222;
                color: #00ff44;
                border-radius: 10px;
                font-weight: bold;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #333;
                color: #00ff88;
            }
            QPushButton:pressed {
                background-color: #444;
            }
        """

        btn_prev.setStyleSheet(button_style)
        btn_next.setStyleSheet(button_style)
        btn_prev.setFixedSize(20,20)
        btn_next.setFixedSize(20,20)
        btn_prev.clicked.connect(self.prev)
        btn_next.clicked.connect(self.next)

        nav = QHBoxLayout()
        nav.addWidget(btn_prev)
        nav.addWidget(self.title_label)
        nav.addWidget(btn_next)

        # 2) The stacked widget itself
        self.stack = QStackedWidget()

        # Simplified widgets that don't rely on pyqtgraph
        self.stack.addWidget(self._make_depth_widget())
        self.stack.addWidget(self._make_margin_gauge())
        self.stack.addWidget(self._make_pnl_widget())
        self.stack.addWidget(self._make_spread_label())

        # Layout
        layout = QVBoxLayout(self)
        layout.addLayout(nav)
        layout.addWidget(self.stack)

    def prev(self):
        idx = (self.stack.currentIndex() - 1) % self.stack.count()
        self.stack.setCurrentIndex(idx)
        # Update the title label
        self.title_label.setText(self.widget_titles[idx])
        # Apply a subtle fade-in effect
        self._apply_fade_effect()

    def next(self):
        idx = (self.stack.currentIndex() + 1) % self.stack.count()
        self.stack.setCurrentIndex(idx)
        # Update the title label
        self.title_label.setText(self.widget_titles[idx])
        # Apply a subtle fade-in effect
        self._apply_fade_effect()

    def _apply_fade_effect(self):
        """Apply a fade-in effect to the current widget"""
        # Only apply fade effect once every 5 seconds to reduce overhead
        current_time = datetime.now()
        if hasattr(self, 'last_fade_time') and (current_time - self.last_fade_time).total_seconds() < 5:
            return

        self.last_fade_time = current_time

        try:
            # Create a fade-in effect for the current widget
            current_widget = self.stack.currentWidget()

            # Use QGraphicsOpacityEffect for the fade
            opacity_effect = QGraphicsOpacityEffect(current_widget)
            current_widget.setGraphicsEffect(opacity_effect)

            # Create animation with shorter duration
            self.fade_animation = QPropertyAnimation(opacity_effect, b"opacity")
            self.fade_animation.setDuration(200)  # 200ms duration (reduced from 300ms)
            self.fade_animation.setStartValue(0.5)  # Start from higher opacity (less dramatic fade)
            self.fade_animation.setEndValue(1.0)
            self.fade_animation.setEasingCurve(QEasingCurve.InOutQuad)
            self.fade_animation.start(QPropertyAnimation.DeleteWhenStopped)
        except Exception as e:
            print(f"Error applying fade effect: {e}")

    def _make_depth_widget(self):
        # Simple widget to show depth info
        w = QWidget()
        w.setStyleSheet("background:#111;")
        layout = QVBoxLayout(w)

        self.depth_label = QLabel("Order Book Depth")
        self.depth_label.setStyleSheet("color:#00ff44; font-weight:bold;")
        self.depth_label.setAlignment(Qt.AlignCenter)

        self.bid_label = QLabel("Bids: 0.00")
        self.bid_label.setStyleSheet("color:#00ff44;")

        self.ask_label = QLabel("Asks: 0.00")
        self.ask_label.setStyleSheet("color:#ff0000;")

        layout.addWidget(self.depth_label)
        layout.addWidget(self.bid_label)
        layout.addWidget(self.ask_label)

        return w

    def _make_margin_gauge(self):
        # A horizontal progress bar: used margin vs free
        w = QWidget()
        l = QVBoxLayout(w)

        # Create a styled margin gauge
        self.margin_bar = QProgressBar()
        self.margin_bar.setTextVisible(True)
        self.margin_bar.setMaximum(100)

        # Style the progress bar for a more modern look
        self.margin_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #444;
                border-radius: 5px;
                background-color: #111;
                text-align: center;
                color: white;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #005500, stop:0.5 #00aa00, stop:1 #00ff00);
                border-radius: 3px;
            }
        """)

        # Create a label with more detailed info
        self.margin_details = QLabel("Used: $0 | Free: $0")
        self.margin_details.setStyleSheet("color: #ccc; font-size: 9px;")
        self.margin_details.setAlignment(Qt.AlignCenter)

        # Add a title with icon
        margin_title = QLabel("Margin Usage")
        margin_title.setStyleSheet("color: #00ff44; font-weight: bold;")
        margin_title.setAlignment(Qt.AlignCenter)

        l.addWidget(margin_title)
        l.addWidget(self.margin_bar)
        l.addWidget(self.margin_details)
        return w

    def _make_pnl_widget(self):
        # Simple widget to show PnL info
        w = QWidget()
        w.setStyleSheet("background:#111;")
        layout = QVBoxLayout(w)

        self.pnl_label = QLabel("Profit & Loss")
        self.pnl_label.setStyleSheet("color:#00ff44; font-weight:bold;")
        self.pnl_label.setAlignment(Qt.AlignCenter)

        self.current_pnl = QLabel("Current: $0.00")
        self.current_pnl.setStyleSheet("color:#00ff44;")

        self.daily_pnl = QLabel("Daily: $0.00")
        self.daily_pnl.setStyleSheet("color:#00ff44;")

        layout.addWidget(self.pnl_label)
        layout.addWidget(self.current_pnl)
        layout.addWidget(self.daily_pnl)

        return w

    def _make_spread_label(self):
        lbl = QLabel("Spread: 0.0000 (0.00%)")
        lbl.setStyleSheet("color:#00ff44; background:rgba(0,0,0,150); padding:4px;")
        lbl.setAlignment(Qt.AlignCenter)
        return lbl

    def update_all(self, symbol):
        """Call this every tick/update cycle using a worker thread"""
        # Check if a worker is already running
        if hasattr(self, '_mini_widgets_worker') and self._mini_widgets_worker.isRunning():
            return  # Don't start a new worker if one is already running

        # Create and start the worker thread
        self._mini_widgets_worker = workers.OrderBookWorker(symbol)
        self._mini_widgets_worker.fetched.connect(self._update_with_orderbook)
        self._mini_widgets_worker.error.connect(self._handle_mini_widgets_error)
        self._mini_widgets_worker.start()

    def _handle_mini_widgets_error(self, error_msg):
        """Handle errors from the mini widgets worker thread"""
        if DEBUG:
            print(f"Mini widgets worker error: {error_msg}")

    def _update_with_orderbook(self, ob):
        """Update the mini widgets with the fetched order book data"""
        try:
            # 1) Update depth widget
            if ob and ob.get('bids') and ob.get('asks'):
                total_bid_vol = sum(b[1] for b in ob['bids'][:5])
                total_ask_vol = sum(a[1] for a in ob['asks'][:5])
                self.bid_label.setText(f"Bids: {total_bid_vol:.4f}")
                self.ask_label.setText(f"Asks: {total_ask_vol:.4f}")
                self.depth_label.setText(f"Order Book Depth: {ob.get('symbol', '')}")

            # 2) Update margin gauge with smooth animation
            info = fetch_account_info()
            used = float(info['used_balance'].strip('$'))
            free = float(info['free_balance'].strip('$'))
            total = used + free
            pct = int((used/total)*100) if total>0 else 0

            # Animate the progress bar change
            try:
                current_value = self.margin_bar.value()
                if current_value != pct:
                    # Create animation for smooth transition
                    self.margin_animation = QPropertyAnimation(self.margin_bar, b"value")
                    self.margin_animation.setDuration(500)  # 500ms duration
                    self.margin_animation.setStartValue(current_value)
                    self.margin_animation.setEndValue(pct)
                    self.margin_animation.setEasingCurve(QEasingCurve.OutCubic)
                    self.margin_animation.start(QPropertyAnimation.DeleteWhenStopped)

                # Update the format text
                self.margin_bar.setFormat(f"{pct}% used")

                # Update the detailed info
                self.margin_details.setText(f"Used: ${used:.2f} | Free: ${free:.2f}")

                # Change color based on usage percentage
                if pct > 80:
                    # Red for high usage
                    self.margin_bar.setStyleSheet("""
                        QProgressBar { border: 1px solid #444; border-radius: 5px; background-color: #111; color: white; }
                        QProgressBar::chunk { background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #550000, stop:0.5 #aa0000, stop:1 #ff0000); border-radius: 3px; }
                    """)
                elif pct > 50:
                    # Yellow for medium usage
                    self.margin_bar.setStyleSheet("""
                        QProgressBar { border: 1px solid #444; border-radius: 5px; background-color: #111; color: white; }
                        QProgressBar::chunk { background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #555500, stop:0.5 #aaaa00, stop:1 #ffff00); border-radius: 3px; }
                    """)
                else:
                    # Green for low usage
                    self.margin_bar.setStyleSheet("""
                        QProgressBar { border: 1px solid #444; border-radius: 5px; background-color: #111; color: white; }
                        QProgressBar::chunk { background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #005500, stop:0.5 #00aa00, stop:1 #00ff00); border-radius: 3px; }
                    """)
            except Exception as e:
                print(f"Error animating margin gauge: {e}")
                # Fallback to direct update if animation fails
                self.margin_bar.setValue(pct)
                self.margin_bar.setFormat(f"{pct}% used")

            # 3) Update PnL widget
            pnl = 0.0
            for pos in fetch_open_positions():
                pnl += float(pos.get('unrealizedPnl',0))
            self.current_pnl.setText(f"Current: ${pnl:.2f}")
            self.current_pnl.setStyleSheet(f"color:{'#00ff44' if pnl >= 0 else '#ff0000'};")

            # Use a fixed value for daily PnL in demo mode
            daily_pnl = pnl * 1.5  # Just a demo value
            self.daily_pnl.setText(f"Daily: ${daily_pnl:.2f}")
            self.daily_pnl.setStyleSheet(f"color:{'#00ff44' if daily_pnl >= 0 else '#ff0000'};")

            # 4) Update spread label
            if ob and ob.get('bids') and ob.get('asks') and len(ob['bids']) > 0 and len(ob['asks']) > 0:
                spread = ob['asks'][0][0] - ob['bids'][0][0]
                pct_spread = spread / ((ob['asks'][0][0]+ob['bids'][0][0])/2) * 100
                self.stack.widget(3).setText(f"Spread: {spread:.4f} ({pct_spread:.2f}%)")
        except Exception as e:
            print(f"Error updating mini widgets: {e}")

class TradesWidget(QWidget):
    """Widget to display recent trades"""
    MAX_TRADES = 3  # Show last 3 trades

    # Use real trades data from the chart
    DEMO_MODE = False

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent  # so we can call back to main window
        self.current_symbol = "DOGE/USDT:USDT"  # Set a default symbol
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # Remove margins for a cleaner look

        print("TradesWidget initialized with DEMO_MODE =", self.DEMO_MODE)
        print(f"Default symbol set to {self.current_symbol}")

        # We don't need a timer as we'll get updates from the chart
        print("TradesWidget initialized - will receive updates from chart data")

        # Create the trades table with exactly the number of rows we need
        self.trades_table = QTableWidget(0, 3)  # 0 rows initially, 3 columns: Side, Price, Amount
        self.trades_table.setHorizontalHeaderLabels(["Side", "Price", "Amount"])
        self.trades_table.verticalHeader().setVisible(False)
        self.trades_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.trades_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.trades_table.setShowGrid(False)  # Hide grid lines for a cleaner look

        # Set custom font for the header
        header_font = QFont("Arial", 10, QFont.Bold)
        self.trades_table.horizontalHeader().setFont(header_font)

        # Set column widths
        self.trades_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Side
        self.trades_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)  # Price
        self.trades_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)  # Amount

        # Set row height for better readability
        self.trades_table.verticalHeader().setDefaultSectionSize(40)  # Taller rows

        layout.addWidget(self.trades_table)

        # Style the table with a more modern and readable look
        self.trades_table.setStyleSheet("""
          QTableWidget {
            background-color: #0a0a0a;
            color: #ffffff;
            border: none;
            font-size: 12pt;
            font-family: 'Arial';
          }
          QHeaderView::section {
            background-color: #1a1a1a;
            color: #ffffff;
            padding: 8px;
            border: none;
            font-weight: bold;
          }
          QTableWidget::item {
            padding: 5px;
            border-bottom: 1px solid #222;
          }
          QTableWidget::item:selected {
            background-color: #2a2a2a;
          }
        """)

    # We don't need the _update_trades method anymore as we get updates from the chart

    def _generate_demo_trades(self, symbol):
        """Generate demo trades for testing"""
        current_time = datetime.now()
        demo_trades = []

        # Get a base price for the symbol
        if symbol.startswith('BTC'):
            base_price = 50000
        elif symbol.startswith('ETH'):
            base_price = 3000
        elif symbol.startswith('DOGE'):
            base_price = 0.18
        else:
            base_price = 100

        # Generate trades with some price variation
        for i in range(10):  # Generate 10 trades
            # Alternate buy/sell trades
            side = 'buy' if i % 2 == 0 else 'sell'

            # Vary price slightly
            price_change = random.uniform(-0.002, 0.002)
            price = base_price * (1 + price_change)

            # Vary amount
            amount = random.uniform(0.001, 0.1) if base_price > 1000 else random.uniform(10, 100)

            # Create trade timestamp (older to newer)
            trade_time = current_time - timedelta(seconds=10-i)

            demo_trades.append({
                'id': f"demo-trade-{i}",
                'symbol': symbol,
                'side': side,
                'amount': amount,
                'price': price,
                'timestamp': int(trade_time.timestamp() * 1000),
                'datetime': trade_time.isoformat(),
                'fee': {'cost': amount * price * 0.001, 'currency': 'USDT'},
                'type': 'limit',
                'cost': amount * price
            })

        print(f"Generated {len(demo_trades)} demo trades for {symbol}")
        return demo_trades

    def update(self, symbol):
        """Update the trades table with recent trades"""
        # Skip if symbol is empty
        if not symbol:
            print("Skipping trades update - empty symbol")
            return

        # Throttle updates to reduce lag, but keep trades updates frequent
        current_time = datetime.now()
        if hasattr(self, 'last_update_time') and (current_time - self.last_update_time).total_seconds() < 1:
            return  # Skip updates if less than 1 second has passed

        self.last_update_time = current_time

        # Store the current symbol
        self.current_symbol = symbol
        print(f"Updating trades for {symbol}")

        # Check if the main window has trades data for this symbol
        if hasattr(self.parent, 'latest_trades_data') and self.parent.latest_trades_data:
            print(f"Using existing trades data from chart")
            self._populate_trades(self.parent.latest_trades_data)
        elif self.DEMO_MODE:
            # Fallback to demo mode if no data is available
            trades = self._generate_demo_trades(symbol)
            self._populate_trades(trades)
        else:
            # If no data is available and not in demo mode, fetch data
            print(f"No existing trades data, fetching from exchange")
            limit = self.MAX_TRADES * 5  # Fetch 5x more trades than we display

            # Create and start the worker thread
            self._trades_worker = workers.TradesWorker(symbol, limit=limit)
            self._trades_worker.fetched.connect(self._populate_trades)
            self._trades_worker.error.connect(self._handle_error)
            self._trades_worker.start()

    def _handle_error(self, error_msg):
        """Handle errors from the trades worker thread"""
        if DEBUG:
            print(f"Trades worker error: {error_msg}")

    def _populate_trades(self, trades):
        """Populate the trades table with the fetched data"""
        try:
            if not trades:
                print("No trades received")
                return

            # print(f"Received {len(trades)} trades for {self.current_symbol}")

            # Get the most recent trades (limited to MAX_TRADES)
            recent_trades = trades[-self.MAX_TRADES:] if len(trades) > self.MAX_TRADES else trades
            # print(f"Displaying {len(recent_trades)} most recent trades")

            # Prepare all items before touching the table to minimize UI updates
            items_to_set = []

            # Process trades in reverse order (newest at the top)
            for i, trade in enumerate(reversed(recent_trades)):
                # Get trade data
                side = trade.get('side', '')
                price = trade.get('price', 0)
                amount = trade.get('amount', 0)

                # Side cell
                side_item = QTableWidgetItem(side.upper())
                side_item.setFont(QFont("Arial", 10, QFont.Bold))

                # Set color based on side (buy=green, sell=red)
                if side.lower() == 'buy':
                    side_item.setForeground(QColor("#4dff4d"))  # Green for buys
                    side_item.setBackground(QColor(30, 60, 30))  # Dark green background
                else:
                    side_item.setForeground(QColor("#ff4d4d"))  # Red for sells
                    side_item.setBackground(QColor(60, 30, 30))  # Dark red background

                side_item.setTextAlignment(Qt.AlignCenter)
                items_to_set.append((i, 0, side_item))

                # Price cell
                price_item = QTableWidgetItem(f"{price:.4f}")
                price_item.setFont(QFont("Arial", 11))
                price_item.setForeground(QColor("#ffffff"))  # White text
                price_item.setTextAlignment(Qt.AlignCenter)
                items_to_set.append((i, 1, price_item))

                # Amount cell
                amount_item = QTableWidgetItem(f"{amount:.2f}")
                amount_item.setFont(QFont("Arial", 10))
                amount_item.setForeground(QColor("#aaaaaa"))  # Light gray text
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                items_to_set.append((i, 2, amount_item))

            # Now update the table all at once
            # First, block signals to prevent multiple updates
            self.trades_table.blockSignals(True)

            # Clear the table and set the exact number of rows we need
            self.trades_table.setRowCount(0)
            self.trades_table.setRowCount(len(recent_trades))

            # Set all items at once
            for row, col, item in items_to_set:
                self.trades_table.setItem(row, col, item)

            # Unblock signals
            self.trades_table.blockSignals(False)
        except Exception as e:
            print(f"Error populating trades: {e}")


class OrderBookWidget(QWidget):
    DEPTH = 1    # show exactly 1 ask, 1 mid row, 1 bid

    # Set a minimum height to ensure at least 3 rows are visible
    MIN_VISIBLE_ROWS = 3

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent  # so we can call back to main window
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)  # Remove margins for a cleaner look

        #  ─── 3 cols: Asks | Price | Bids ──────────────────────────
        # Create table with 0 rows initially, we'll set the exact number we need when populating
        self.book_table = QTableWidget(0, 3)
        self.book_table.setHorizontalHeaderLabels(["Asks", "Price", "Bids"])
        self.book_table.verticalHeader().setVisible(False)
        self.book_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.book_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.book_table.setShowGrid(False)  # Hide grid lines for a cleaner look

        # Set custom font for the header
        header_font = QFont("Arial", 10, QFont.Bold)
        self.book_table.horizontalHeader().setFont(header_font)

        # Set column widths - stretch asks/bids, fixed width for price
        self.book_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Asks
        self.book_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Price
        self.book_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)  # Bids

        # Set row height for better readability
        self.book_table.verticalHeader().setDefaultSectionSize(40)  # Taller rows

        layout.addWidget(self.book_table)

        # Style the table with a more modern and readable look
        self.book_table.setStyleSheet("""
          QTableWidget {
            background-color: #0a0a0a;
            color: #ffffff;
            border: none;
            font-size: 12pt;
            font-family: 'Arial';
          }
          QHeaderView::section {
            background-color: #1a1a1a;
            color: #00b4d8;
            padding: 8px;
            border: none;
            font-weight: bold;
          }
          QTableWidget::item {
            padding: 5px;
            border-bottom: 1px solid #222;
          }
          QTableWidget::item:selected {
            background-color: #2a2a2a;
          }
        """)

        # Calculate minimum height to show at least MIN_VISIBLE_ROWS rows plus header
        header_height = self.book_table.horizontalHeader().height()
        row_height = 25  # Approximate row height in pixels
        min_height = header_height + (self.MIN_VISIBLE_ROWS * row_height)
        self.book_table.setMinimumHeight(min_height)

        # Connect click events
        self.book_table.cellClicked.connect(self.on_book_click)

        # Connect right-click context menu
        self.book_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.book_table.customContextMenuRequested.connect(self.on_book_context_menu)

    def update(self, symbol):
        # Skip if symbol is empty
        if not symbol:
            return

        # Store the symbol for later use
        self.current_symbol = symbol

        # Throttle updates to reduce lag, but keep orderbook updates frequent
        current_time = datetime.now()
        if hasattr(self, 'last_update_time') and (current_time - self.last_update_time).total_seconds() < 1:
            return  # Skip updates if less than 1 second has passed

        self.last_update_time = current_time

        # Check if a worker is already running
        if hasattr(self, '_orderbook_worker') and self._orderbook_worker.isRunning():
            return  # Don't start a new worker if one is already running

        # Create and start the worker thread
        self._orderbook_worker = workers.OrderBookWorker(symbol)
        self._orderbook_worker.fetched.connect(self._populate_orderbook)
        self._orderbook_worker.error.connect(self._handle_orderbook_error)
        self._orderbook_worker.start()

    def _handle_orderbook_error(self, error_msg):
        """Handle errors from the order book worker thread"""
        if DEBUG:
            print(f"Order book worker error: {error_msg}")

    def _populate_orderbook_old(self, ob):
        """Populate the order book with the fetched data"""
        if not ob:
            return

        bids = ob['bids'][:self.DEPTH]                         # best bids first
        asks = ob['asks'][:self.DEPTH]                         # best (lowest) asks first

        max_size = max(
            max((size for _, size in bids), default=0),
            max((size for _, size in asks), default=0)
        ) or 1

        # clear all cells but keep structure
        self.book_table.clearContents()

        mid = self.DEPTH

        # ——— fill asks above the mid-row ——————————————————————
        for i, (price, size) in enumerate(asks):
            row = i
            # price
            itm_p = QTableWidgetItem(f"{price:.4f}")
            itm_p.setTextAlignment(Qt.AlignCenter)
            self.book_table.setItem(row, 1, itm_p)
            # ask size
            itm_s = QTableWidgetItem(str(size))
            itm_s.setTextAlignment(Qt.AlignRight|Qt.AlignVCenter)
            color_int = int(200 * (size/max_size)) + 30
            itm_s.setBackground(QColor(255, color_int, color_int))
            self.book_table.setItem(row, 2, itm_s)

        # ——— mid-row: current mark price ———————————————————
        best_bid = bids[0][0] if bids else 0
        best_ask = asks[-1][0] if asks else 0  # Last ask is the best (lowest) ask

        # Better mid-price fallback logic
        if best_bid and best_ask:
            mid_price = (best_bid + best_ask) / 2
        elif best_bid:
            mid_price = best_bid
        elif best_ask:
            mid_price = best_ask
        else:
            mid_price = None

        # Only draw the mid price if we have a valid value
        if mid_price is not None:
            itm_mid = QTableWidgetItem(f"{mid_price:.4f}")
            itm_mid.setFont(QFont("", weight=QFont.Bold))
            itm_mid.setForeground(QColor("#00ff44"))
            itm_mid.setTextAlignment(Qt.AlignCenter)
            self.book_table.setItem(mid, 1, itm_mid)

        # ——— fill bids below the mid-row ———————————————————
        # Calculate how many bids to display to ensure we have at least 3 visible
        display_count = min(len(bids), self.MIN_VISIBLE_ROWS)

        # Start from mid-row and go down
        for i in range(display_count):
            row = mid + 1 + i  # Start from just below mid-row and go down
            if i < len(bids):
                price, size = bids[i]
                # bid size
                itm_s = QTableWidgetItem(str(size))
                itm_s.setTextAlignment(Qt.AlignLeft|Qt.AlignVCenter)
                color_int = int(200 * (size/max_size)) + 30
                itm_s.setBackground(QColor(color_int, 255, color_int))
                self.book_table.setItem(row, 0, itm_s)
                # price
                itm_p = QTableWidgetItem(f"{price:.4f}")
                itm_p.setTextAlignment(Qt.AlignCenter)
                self.book_table.setItem(row, 1, itm_p)

        # Make sure the middle-row (the mid price) is visible and centered
        mid_row = self.DEPTH
        mid_item = self.book_table.item(mid_row, 1)  # column 1 is the Price column
        if mid_item:
            # Scroll so that the mid_row is centered vertically in the viewport
            self.book_table.scrollToItem(
                mid_item,
                QAbstractItemView.PositionAtCenter
            )

    def _populate_orderbook(self, ob):
        """Populate the order book with the fetched data"""
        try:
            if not ob:
                return

            # Skip if the symbol has changed since we started fetching
            if hasattr(self, 'current_symbol') and self.parent and hasattr(self.parent, 'get_symbol'):
                current_symbol = self.parent.get_symbol()
                if current_symbol != self.current_symbol:
                    print(f"Skipping orderbook update - symbol changed from {self.current_symbol} to {current_symbol}")
                    return

            bids = ob['bids'][:self.DEPTH]                         # best bids first
            asks = ob['asks'][:self.DEPTH]                         # best (lowest) asks first

            max_size = max(
                max((size for _, size in bids), default=0),
                max((size for _, size in asks), default=0)
            ) or 1

            # Determine the appropriate decimal precision based on price
            # For assets like DOGE with small prices, use more decimal places
            decimal_precision = 6 if (asks and asks[0][0] < 1) or (bids and bids[0][0] < 1) else 4

            # Calculate the total number of rows we need
            total_rows = 2 * self.DEPTH + 1  # asks + mid + bids

            # Prepare all items before touching the table to minimize UI updates
            items_to_set = []
            mid = self.DEPTH

            # ——— 1) Asks above the middle row (LEFT SIDE) —————————————
            for i, (price, size) in enumerate(asks):
                row = i
                # Ask price (left side)
                ask_price = QTableWidgetItem(f"{price:.{decimal_precision}f}")
                ask_price.setFont(QFont("Arial", 11, QFont.Bold))
                ask_price.setForeground(QColor("#ff4d4d"))  # Red text for asks
                ask_price.setTextAlignment(Qt.AlignRight)  # Right-aligned as requested

                # Calculate intensity based on size relative to max_size
                # Use a logarithmic scale to better visualize differences
                intensity = min(1.0, 0.2 + 0.8 * (size / max_size))

                # Create a gradient background from dark to bright red
                r = int(60 + 195 * intensity)  # 60 to 255
                g = int(30 * (1 - intensity))  # 30 to 0
                b = int(30 * (1 - intensity))  # 30 to 0

                # Apply the gradient background
                ask_price.setBackground(QColor(r, g, b, 100 + int(155 * intensity)))  # Semi-transparent
                ask_price.setToolTip(f"Size: {size:.1f} ({intensity*100:.0f}% of max)")
                items_to_set.append((row, 0, ask_price))  # Column 0 (left)

            # ——— 2) Mid‐price (CENTER) ————————————————————
            # Calculate mid price from best bid and best ask
            mid_price = 0
            if bids and asks:
                mid_price = (bids[0][0] + asks[0][0]) / 2
            elif bids:
                mid_price = bids[0][0]
            elif asks:
                mid_price = asks[0][0]

            # Create mid-price item with enhanced styling
            mid_item = QTableWidgetItem(f"{mid_price:.{decimal_precision}f}")
            mid_item.setFont(QFont("Arial", 12, QFont.Bold))
            mid_item.setForeground(QColor("#00ff88"))  # Bright green text
            mid_item.setTextAlignment(Qt.AlignCenter)
            mid_item.setBackground(QColor(30, 50, 30))  # Dark green background
            items_to_set.append((mid, 1, mid_item))  # Column 1 (center)

            # ——— 3) Bids below the middle row (RIGHT SIDE) ——————————
            for i, (price, size) in enumerate(bids):
                row = mid + 1 + i
                # Bid price (right side)
                bid_price = QTableWidgetItem(f"{price:.{decimal_precision}f}")
                bid_price.setFont(QFont("Arial", 11, QFont.Bold))
                bid_price.setForeground(QColor("#4dff4d"))  # Green text for bids
                bid_price.setTextAlignment(Qt.AlignLeft)  # Left-aligned as requested

                # Calculate intensity based on size relative to max_size
                # Use a logarithmic scale to better visualize differences
                intensity = min(1.0, 0.2 + 0.8 * (size / max_size))

                # Create a gradient background from dark to bright green
                r = int(30 * (1 - intensity))  # 30 to 0
                g = int(60 + 195 * intensity)  # 60 to 255
                b = int(30 * (1 - intensity))  # 30 to 0

                # Apply the gradient background
                bid_price.setBackground(QColor(r, g, b, 100 + int(155 * intensity)))  # Semi-transparent
                bid_price.setToolTip(f"Size: {size:.1f} ({intensity*100:.0f}% of max)")
                items_to_set.append((row, 2, bid_price))  # Column 2 (right)

            # Now update the table all at once
            # First, block signals to prevent multiple updates
            self.book_table.blockSignals(True)

            # Set the exact number of rows we need and clear all cells
            self.book_table.setRowCount(total_rows)
            self.book_table.clearContents()

            # Set all items at once
            for row, col, item in items_to_set:
                self.book_table.setItem(row, col, item)

            # Add a subtle highlight to the entire mid-price row
            for col in range(3):
                item = self.book_table.item(mid, col)
                if item:
                    item.setBackground(QColor(30, 50, 30))  # Dark green background

            # Make sure the middle-row (the mid price) is visible and centered
            mid_item = self.book_table.item(mid, 1)  # column 1 is the Price column
            if mid_item:
                # Scroll so that the mid_row is centered vertically in the viewport
                self.book_table.scrollToItem(
                    mid_item,
                    QAbstractItemView.PositionAtCenter
                )

            # Unblock signals
            self.book_table.blockSignals(False)
        except Exception as e:
            print(f"Error populating orderbook: {e}")

    def on_book_click(self, row, _):
        # We ignore the column parameter as we always use the price from column 1
        # Get the price from the middle column
        price_item = self.book_table.item(row, 1)
        if not price_item or not price_item.text():
            return

        # Clearer side detection based on row position relative to mid
        mid = self.DEPTH
        if row < mid:  # Above mid row = ask/sell
            side = 'sell'
        elif row > mid:  # Below mid row = bid/buy
            side = 'buy'
        else:  # Mid row - skip
            return

        price = float(price_item.text())
        qty = self.parent.get_quantity()

        # place a limit order at that price
        place_limit_order(
            self.parent.get_symbol(),
            side,
            qty,
            price,
            params={'offset':'open', 'lever_rate':self.parent.get_leverage()}
        )
        self.parent._update_positions()
        print(f"Order Book Click: {side} {qty} {self.parent.get_symbol()} @ {price}")

    def on_book_context_menu(self, point):
        """Handle right-click context menu in the order book"""
        # Get the row that was clicked
        row = self.book_table.rowAt(point.y())

        if row < 0:
            return

        # Get the price from the middle column
        price_item = self.book_table.item(row, 1)
        if not price_item or not price_item.text():
            return

        price = float(price_item.text())
        symbol = self.parent.get_symbol()

        # Determine if this is a bid or ask based on row position
        mid = self.DEPTH
        if row < mid:  # Above mid row = ask/sell
            side = 'sell'
            opposite_side = 'buy'
        elif row > mid:  # Below mid row = bid/buy
            side = 'buy'
            opposite_side = 'sell'
        else:  # Mid row - use a default
            return

        # Create the context menu
        menu = QMenu(self.book_table)

        # Check if there's an existing order at this price
        orders = fetch_open_orders(symbol)
        matching_orders = [o for o in orders if float(o.get('price', 0)) == price and o.get('side', '').lower() == side]

        if matching_orders:
            # Actions for existing orders
            for order in matching_orders:
                order_id = order.get('id', '')
                order_size = float(order.get('amount', 0))

                # Add a header for this order
                header_action = QAction(f"Order: {side.upper()} {order_size} @ {price}", menu)
                header_action.setEnabled(False)
                menu.addAction(header_action)

                # Cancel order action
                cancel_action = menu.addAction(f"Cancel Order")
                cancel_action.setData(("cancel", order_id))

                # Modify order actions
                modify_price_action = menu.addAction(f"Modify Price")
                modify_price_action.setData(("modify_price", order_id, order_size))

                modify_size_action = menu.addAction(f"Modify Size")
                modify_size_action.setData(("modify_size", order_id, price))

                # Market close action
                market_close_action = menu.addAction(f"Market Close")
                market_close_action.setData(("market_close", order_id, order_size))

                menu.addSeparator()

        # Always add actions for new orders
        new_order_action = menu.addAction(f"Place {side.upper()} Order")
        new_order_action.setData(("new_order", side, price))

        opposite_order_action = menu.addAction(f"Place {opposite_side.upper()} Order")
        opposite_order_action.setData(("new_order", opposite_side, price))

        # Execute the menu and get the selected action
        action = menu.exec_(self.book_table.mapToGlobal(point))
        if not action:
            return

        # Process the selected action
        action_data = action.data()
        if not action_data:
            return

        action_type = action_data[0]

        if action_type == "cancel":
            # Cancel the order
            order_id = action_data[1]
            self.cancel_order(order_id)

        elif action_type == "modify_price":
            # Modify the price of an order
            order_id = action_data[1]
            order_size = action_data[2]
            self.modify_order_price(order_id, order_size)

        elif action_type == "modify_size":
            # Modify the size of an order
            order_id = action_data[1]
            order_price = action_data[2]
            self.modify_order_size(order_id, order_price)

        elif action_type == "market_close":
            # Close the order at market price
            order_id = action_data[1]
            order_size = action_data[2]
            self.market_close_order(order_id, order_size, side)

        elif action_type == "new_order":
            # Place a new order
            order_side = action_data[1]
            order_price = action_data[2]
            self.place_new_order(order_side, order_price)

    def cancel_order(self, order_id):
        """Cancel an order by ID"""
        try:
            result = cancel_order(order_id)
            if result:
                QMessageBox.information(self.parent, "Order Canceled", f"Successfully canceled order {order_id}")
            else:
                QMessageBox.warning(self.parent, "Cancel Failed", f"Failed to cancel order {order_id}")
        except Exception as e:
            QMessageBox.critical(self.parent, "Error", f"Error canceling order: {str(e)}")
        finally:
            # Update the UI
            self.parent._update_positions()
            self.parent._update_orders()

    def modify_order_price(self, order_id, order_size):
        """Modify the price of an order"""
        try:
            # Ask for the new price
            new_price, ok = QInputDialog.getDouble(
                self.parent, "Modify Order Price", "Enter new price:",
                0.0, 0.0, 1000000.0, 4
            )

            if ok and new_price > 0:
                # Cancel the old order
                cancel_result = cancel_order(order_id)
                if not cancel_result:
                    QMessageBox.warning(self.parent, "Modify Failed", "Failed to cancel the original order")
                    return

                # Get the order details to recreate it
                orders = fetch_open_orders(self.parent.get_symbol())
                matching_order = next((o for o in orders if o.get('id', '') == order_id), None)

                if matching_order:
                    side = matching_order.get('side', '').lower()
                    symbol = matching_order.get('symbol', self.parent.get_symbol())

                    # Place a new order with the new price
                    place_limit_order(
                        symbol,
                        side,
                        order_size,
                        new_price,
                        params={'offset':'open', 'lever_rate':self.parent.get_leverage()}
                    )

                    QMessageBox.information(self.parent, "Order Modified",
                                          f"Modified order price from {matching_order.get('price')} to {new_price}")
                else:
                    # If we can't find the original order details, just place a new one
                    QMessageBox.warning(self.parent, "Order Not Found",
                                      "Original order not found, please place a new order manually")
        except Exception as e:
            QMessageBox.critical(self.parent, "Error", f"Error modifying order: {str(e)}")
        finally:
            # Update the UI
            self.parent._update_positions()
            self.parent._update_orders()

    def modify_order_size(self, order_id, order_price):
        """Modify the size of an order"""
        try:
            # Ask for the new size
            new_size, ok = QInputDialog.getDouble(
                self.parent, "Modify Order Size", "Enter new size:",
                0.0, 0.0, 1000000.0, 4
            )

            if ok and new_size > 0:
                # Cancel the old order
                cancel_result = cancel_order(order_id)
                if not cancel_result:
                    QMessageBox.warning(self.parent, "Modify Failed", "Failed to cancel the original order")
                    return

                # Get the order details to recreate it
                orders = fetch_open_orders(self.parent.get_symbol())
                matching_order = next((o for o in orders if o.get('id', '') == order_id), None)

                if matching_order:
                    side = matching_order.get('side', '').lower()
                    symbol = matching_order.get('symbol', self.parent.get_symbol())

                    # Place a new order with the new size
                    place_limit_order(
                        symbol,
                        side,
                        new_size,
                        order_price,
                        params={'offset':'open', 'lever_rate':self.parent.get_leverage()}
                    )

                    QMessageBox.information(self.parent, "Order Modified",
                                          f"Modified order size from {matching_order.get('amount')} to {new_size}")
                else:
                    # If we can't find the original order details, just place a new one
                    QMessageBox.warning(self.parent, "Order Not Found",
                                      "Original order not found, please place a new order manually")
        except Exception as e:
            QMessageBox.critical(self.parent, "Error", f"Error modifying order: {str(e)}")
        finally:
            # Update the UI
            self.parent._update_positions()
            self.parent._update_orders()

    def market_close_order(self, order_id, order_size, side):
        """Close an order at market price"""
        try:
            # Cancel the existing order
            cancel_result = cancel_order(order_id)
            if not cancel_result:
                QMessageBox.warning(self.parent, "Market Close Failed", "Failed to cancel the original order")
                return

            # Place a market order on the opposite side
            opposite_side = 'sell' if side == 'buy' else 'buy'

            place_market_order(
                self.parent.get_symbol(),
                opposite_side,
                order_size,
                params={'offset':'close', 'lever_rate':self.parent.get_leverage()}
            )

            QMessageBox.information(self.parent, "Order Closed",
                                  f"Closed order with market {opposite_side.upper()} of {order_size}")
        except Exception as e:
            QMessageBox.critical(self.parent, "Error", f"Error closing order: {str(e)}")
        finally:
            # Update the UI
            self.parent._update_positions()
            self.parent._update_orders()

    def place_new_order(self, side, price):
        """Place a new order"""
        try:
            # Use the current quantity directly without asking
            size = self.parent.get_quantity()

            if size > 0:
                # Place the order
                place_limit_order(
                    self.parent.get_symbol(),
                    side,
                    size,
                    price,
                    params={'offset':'open', 'lever_rate':self.parent.get_leverage()}
                )

                print(f"Placed {side.upper()} order for {size} @ {price}")
        except Exception as e:
            print(f"Error placing order: {str(e)}")
        finally:
            # Update the UI
            self.parent._update_positions()
            self.parent._update_orders()

class EpinnoxTraderInterface(QMainWindow):
    # Define custom signals for thread-safe UI updates
    status_update_signal = Signal(str)

    def __init__(self):
        super().__init__()

        # Connect the status update signal to the update_status_message slot
        self.status_update_signal.connect(self.update_status_message)

        # Main window
        self.setWindowTitle("EpiNn0x Trader's Interface")
        self.resize(1280, 820)
        self.setStyleSheet("background-color: #0d0d0d; color: #e6e6e6;")

        # Central layout
        central = QWidget(self)
        self.setCentralWidget(central)
        main_layout = QVBoxLayout(central)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(6)

        # ─── Top Bar: Title, Account Info, Theme Selector ──────────────────────
        top_bar = QWidget()
        top_h = QHBoxLayout(top_bar)
        top_h.setContentsMargins(0, 0, 0, 0)

        title = QLabel("EPINNOX")
        title.setFont(QFont("Consolas", 18, QFont.Bold))
        title.setStyleSheet("color: #00ff44;")
        top_h.addWidget(title, alignment=Qt.AlignLeft)

        top_h.addStretch()

        # Account info frame removed - now displayed in the top navigation bar

        main_layout.addWidget(top_bar)

        # ─── Main Tabs: Dashboard, Trading ──────────────────────────────────
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabBar::tab { background: #222; padding:8px; color:#ddd; font-size: 14px; }
            QTabBar::tab:selected { background: #444; }
        """)

        # Create Dashboard Tab with exchange instance
        from dashboard_tab import DashboardTab
        self.dashboard_tab = DashboardTab(self)
        
        # Initialize dashboard with exchange and demo mode settings
        try:
            self.dashboard_tab.initialize(exchange, demo_mode)
        except Exception as e:
            print(f"Warning: Could not fully initialize dashboard: {e}")
            # Continue anyway - dashboard will update when data is available
            
        self.main_tabs.addTab(self.dashboard_tab, "Dashboard")

        # Create Trading Tab (will contain the original UI)
        self.trading_tab = QWidget()
        trading_layout = QGridLayout(self.trading_tab)
        trading_layout.setContentsMargins(8, 8, 8, 8)
        trading_layout.setSpacing(6)

        # Add the analysis panel to the trading tab
        self.analysis_panel = AnalysisPanel(self)
        trading_layout.addWidget(self.analysis_panel, 0, 0, 1, 2)

        # Add the trading tab to main tabs
        self.main_tabs.addTab(self.trading_tab, "Trading")

        # Create and add Chart Tab
        try:
            from chart_tab import ChartTab
            self.chart_tab = ChartTab()
            self.main_tabs.addTab(self.chart_tab, "Charts")
        except Exception as e:
            print(f"Error loading Chart Tab: {e}")

        # Add main tabs to main layout
        main_layout.addWidget(self.main_tabs)

        # Set the dashboard tab as the default tab
        self.main_tabs.setCurrentIndex(0)

        # ─── Menu ───────────────────────────────────────────────────────────────
        mb = self.menuBar()

        # ─── Toolbar for quick actions ─────────────────────────────────
        self.toolbar = self.addToolBar("Quick Actions")
        self.toolbar.setMovable(False)
        self.toolbar.setStyleSheet("""
            QToolBar { background: #111; spacing: 6px; }
            QToolButton { background: #222; color: #0f0; padding: 6px; border: none; }
            QToolButton:hover { background: #333; }
        """)

        # Create styled buttons for the toolbar with colors
        # Trend line button
        btn_trend_line = QPushButton("Trend Line (T)")
        btn_trend_line.setStyleSheet("background-color: #666600; color: white; padding: 6px;")
        btn_trend_line.setCheckable(True)
        btn_trend_line.clicked.connect(self.toggle_trend_line_mode)
        self.toolbar.addWidget(btn_trend_line)
        self.trend_line_btn = btn_trend_line

        # Indicators button
        btn_indicators = QPushButton("Indicators")
        btn_indicators.setStyleSheet("background-color: #006666; color: white; padding: 6px;")
        btn_indicators.clicked.connect(self.show_indicators_dialog)
        self.toolbar.addWidget(btn_indicators)

        # Add a separator
        self.toolbar.addSeparator()

        # Long buttons - green
        btn_limit_long = QPushButton("Limit Long")
        btn_limit_long.setStyleSheet("background-color: #006600; color: white; padding: 6px;")
        btn_limit_long.clicked.connect(self.place_limit_long)

        btn_mkt_long = QPushButton("Market Long")
        btn_mkt_long.setStyleSheet("background-color: #009900; color: white; padding: 6px;")
        btn_mkt_long.clicked.connect(self.place_market_long)

        # Short buttons - red
        btn_limit_short = QPushButton("Limit Short")
        btn_limit_short.setStyleSheet("background-color: #660000; color: white; padding: 6px;")
        btn_limit_short.clicked.connect(self.place_limit_short)

        btn_mkt_short = QPushButton("Market Short")
        btn_mkt_short.setStyleSheet("background-color: #990000; color: white; padding: 6px;")
        btn_mkt_short.clicked.connect(self.place_market_short)

        # Close All - orange
        btn_close_all = QPushButton("Close All")
        btn_close_all.setStyleSheet("background-color: #ff6600; color: white; padding: 6px;")
        btn_close_all.setToolTip("Close all open positions across all symbols")
        btn_close_all.clicked.connect(self.close_all_positions)

        # Cancel All - gray
        btn_cancel_all = QPushButton("Cancel All")
        btn_cancel_all.setStyleSheet("background-color: #666666; color: white; padding: 6px;")
        btn_cancel_all.setToolTip("Cancel all open orders across all symbols")
        btn_cancel_all.clicked.connect(self.cancel_all_orders)

        # Add buttons to toolbar
        for btn in (btn_limit_long, btn_limit_short, btn_mkt_long, btn_mkt_short, btn_close_all, btn_cancel_all):
            self.toolbar.addWidget(btn)

        # Keep the QActions for menu and shortcuts
        act_limit_long  = QAction("Limit Long",  self, triggered=self.place_limit_long,  shortcut="Ctrl+L")
        act_limit_short = QAction("Limit Short", self, triggered=self.place_limit_short, shortcut="Ctrl+K")
        act_mkt_long    = QAction("Market Long", self, triggered=self.place_market_long, shortcut="Ctrl+B")
        act_mkt_short   = QAction("Market Short",self, triggered=self.place_market_short,shortcut="Ctrl+S")
        act_close_all   = QAction("Close All",   self, triggered=self.close_all_positions,shortcut="Ctrl+E")
        act_cancel_all  = QAction("Cancel All",  self, triggered=self.cancel_all_orders,shortcut="Ctrl+W")

        # Add separator
        self.toolbar.addSeparator()

        # Add performance settings button
        speed_btn = QPushButton("Performance Settings")
        speed_btn.setStyleSheet("background-color: #222; color: #00ff44; padding: 6px;")
        speed_btn.clicked.connect(self.show_performance_settings)
        self.toolbar.addWidget(speed_btn)

        # Create the always on top action
        self.always_on_top_action = QAction("Always on Top", self, checkable=True)
        self.always_on_top_action.triggered.connect(self.toggle_always_on_top)

        # Trade menu holds margin & bracket settings
        trade_menu = mb.addMenu("Trade")
        trade_menu.addAction(act_limit_long)
        trade_menu.addAction(act_limit_short)
        trade_menu.addSeparator()
        trade_menu.addAction(act_mkt_long)
        trade_menu.addAction(act_mkt_short)
        trade_menu.addSeparator()
        trade_menu.addAction(QAction("Bracket Order…", self, triggered=self.configure_bracket_dialog))

        # Orders menu for cancel/close
        orders_menu = mb.addMenu("Orders")
        orders_menu.addAction(act_close_all)
        orders_menu.addAction(act_cancel_all)
        orders_menu.addSeparator()
        orders_menu.addAction(QAction("View Open Orders", self, triggered=lambda: self.tabs.setCurrentIndex(1)))

        # View menu for panels & theme
        view_menu = mb.addMenu("View")
        view_menu.addAction(QAction("Toggle Orderbook", self, checkable=True, checked=True, triggered=lambda v: self.orderbook.setVisible(v)))
        view_menu.addAction(QAction("Toggle Chart", self, checkable=True, checked=True, triggered=lambda v: self.chart.setVisible(v)))
        view_menu.addSeparator()
        view_menu.addAction(self.always_on_top_action)

        # Add Indicators menu
        indicators_menu = mb.addMenu("Indicators")
        indicators_menu.addAction(QAction("Manage Indicators...", self, triggered=self.show_indicators_dialog))
        indicators_menu.addSeparator()

        # Create indicator toggle actions
        self.indicator_actions = {}
        for indicator_name in ["Moving Average", "Bollinger Bands", "RSI", "MACD", "Stochastic"]:
            action = QAction(indicator_name, self, checkable=True)
            action.triggered.connect(lambda checked, name=indicator_name: self.toggle_indicator(name, checked))
            indicators_menu.addAction(action)
            self.indicator_actions[indicator_name] = action

        # Set initial states
        self.indicator_actions["Moving Average"].setChecked(True)  # MA is on by default

        # Settings menu
        settings_menu = mb.addMenu("Settings")

        # Performance Settings - Add as first item for prominence
        performance_action = QAction("Performance Settings...", self)
        performance_action.setShortcut("Ctrl+P")
        performance_action.setStatusTip("Adjust update speeds and performance settings")
        performance_action.triggered.connect(self.show_performance_settings)
        settings_menu.addAction(performance_action)

        # Auto Trade Settings
        auto_trade_settings_action = QAction("Auto Trade Settings...", self)
        auto_trade_settings_action.setStatusTip("Configure auto trade strategy parameters")
        auto_trade_settings_action.triggered.connect(self.show_auto_trade_settings)
        settings_menu.addAction(auto_trade_settings_action)

        settings_menu.addSeparator()

        # Account management options
        settings_menu.addAction(QAction("Manage API Accounts", self, triggered=self.show_api_credentials_dialog))
        settings_menu.addAction(QAction("Toggle Demo Mode", self, triggered=self.toggle_demo_mode))

        settings_menu.addSeparator()

        # Theme submenu
        theme_menu = settings_menu.addMenu("Theme")
        theme_menu.setStyleSheet("QMenu { background: #1a1a1a; color: #ddd; }")

        # Create theme actions
        matrix_action = QAction("Matrix (Black/Green)", self, checkable=True)
        matrix_action.triggered.connect(lambda: self.apply_theme(0))

        tradingview_action = QAction("TradingView (Blue/Gray)", self, checkable=True)
        tradingview_action.triggered.connect(lambda: self.apply_theme(1))

        # Add to theme menu
        theme_menu.addAction(matrix_action)
        theme_menu.addAction(tradingview_action)

        # Set initial checked state
        matrix_action.setChecked(True)

        # Store actions for later reference
        self.theme_actions = [matrix_action, tradingview_action]

        # Account status label in menu bar
        self.account_status_label = QLabel("DEMO MODE")
        self.account_status_label.setStyleSheet("color: #ff9900; padding: 0 10px;")
        mb.setCornerWidget(self.account_status_label)

        # Help menu
        help_menu = mb.addMenu("Help")
        help_menu.addAction(QAction("About Epinnox", self, triggered=self.show_help))
        help_menu.addAction(QAction("Documentation", self, triggered=lambda: QDesktopServices.openUrl(QUrl("https://github.com/Geo222222/gettwistedlocs"))))

        # ─── Left Panel: Controls & Inputs ────────────────────────────────────
        left = QWidget()
        left.setFixedWidth(280)  # Reduced from 300 to give more space to right panel
        left.setStyleSheet("background-color: #141414; border-right:1px solid #222;")
        left_layout = QVBoxLayout(left)
        left_layout.setContentsMargins(6, 6, 6, 6)
        left_layout.setSpacing(10)

        # Order buttons grouped - COMMENTED OUT
        # orders_group = QGroupBox("Orders")
        # orders_group.setStyleSheet("QGroupBox { color: #ccc; }")
        # og_l = QGridLayout(orders_group)
        # btn_specs = [
        #     ("Limit Long", "#006600", self.place_limit_long, 0, 0),
        #     ("Limit Short", "#660000", self.place_limit_short, 0, 1),
        #     ("Market Long", "#009900", self.place_market_long, 1, 0),
        #     ("Market Short", "#990000", self.place_market_short, 1, 1),
        #     ("Close All", "#ff6600", self.close_all_positions, 2, 0),
        #     ("Cancel All", "#666666", self.cancel_all_orders, 2, 1),
        # ]
        # for text, color, slot, r, c in btn_specs:
        #     b = QPushButton(text)
        #     b.setStyleSheet(f"background-color: {color}; color: white; padding:6px;")
        #     b.clicked.connect(slot)
        #     og_l.addWidget(b, r, c)
        # left_layout.addWidget(orders_group)

        # Add the MiniWidgetsPanel at the top of the left panel
        self.mini_widgets = MiniWidgetsPanel(self)
        left_layout.addWidget(self.mini_widgets)

        # Inputs group
        inputs_group = QGroupBox("Trade Settings")
        inputs_group.setStyleSheet("QGroupBox { color: #ccc; }")
        ig_l = QVBoxLayout(inputs_group)
        # Symbol selector with price changes
        sym_h = QHBoxLayout()
        sym_h.addWidget(QLabel("Pair:"))
        self.symbol_cb = QComboBox()
        self.symbol_cb.setEditable(True)
        self.symbol_cb.setStyleSheet("""
            QComboBox { min-width: 200px; }
            QComboBox::item { padding: 5px; }
        """)
        # We'll populate the items with price changes later
        self.symbol_cb.setToolTip("Choose trading pair")
        self.symbol_cb.currentTextChanged.connect(self.on_symbol_changed)
        sym_h.addWidget(self.symbol_cb)

        # Add refresh button for tickers
        self.refresh_tickers_btn = QPushButton("⟳")
        self.refresh_tickers_btn.setFixedSize(24, 24)
        self.refresh_tickers_btn.setToolTip("Refresh price data")
        self.refresh_tickers_btn.clicked.connect(self.refresh_tickers)
        sym_h.addWidget(self.refresh_tickers_btn)

        ig_l.addLayout(sym_h)

        # Initialize with default pairs
        self.default_pairs = ["MOODENG/USDT:USDT", "ETH/USDT:USDT", "SOL/USDT:USDT", "BTC/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT"]
        self.symbol_cb.addItems(self.default_pairs)

        # Fetch tickers for the pairs
        self.tickers_cache = {}
        self.last_tickers_update = datetime.now() - timedelta(minutes=10)  # Force initial update
        self.refresh_tickers()
        # Quantity
        qty_h = QHBoxLayout()
        qty_h.addWidget(QLabel("Qty:"))
        self.qty_sb = QDoubleSpinBox()
        self.qty_sb.setRange(0.0001, 100000)
        self.qty_sb.setDecimals(4)
        self.qty_sb.setValue(20)
        qty_h.addWidget(self.qty_sb)
        ig_l.addLayout(qty_h)
        # Leverage
        lev_h = QHBoxLayout()
        lev_h.addWidget(QLabel("Lev:"))
        self.lev_sb = QSpinBox()
        self.lev_sb.setRange(1, 125)
        self.lev_sb.setValue(20)
        lev_h.addWidget(self.lev_sb)
        ig_l.addLayout(lev_h)

        # Max Margin
        max_margin_h = QHBoxLayout()
        max_margin_h.addWidget(QLabel("Max Margin:"))
        self.max_margin_sb = QDoubleSpinBox()
        self.max_margin_sb.setRange(1, 1000)
        self.max_margin_sb.setDecimals(2)
        self.max_margin_sb.setValue(10.0)
        self.max_margin_sb.setToolTip("Maximum margin to use for auto trading")
        max_margin_h.addWidget(self.max_margin_sb)
        ig_l.addLayout(max_margin_h)

        # Margin mode
        mode_h = QHBoxLayout()
        mode_h.addWidget(QLabel("Margin:"))
        self.margin_cb = QComboBox()
        self.margin_cb.addItems(["Cross", "Isolated"])
        mode_h.addWidget(self.margin_cb)
        ig_l.addLayout(mode_h)

        # ─── NEW: Quick‐Qty Presets & Slider ────────────────────────────────
        h = QHBoxLayout()
        for label, factor in [("½", 0.5), ("×1", 1), ("×2", 2), ("Max", None)]:
            b = QPushButton(label)
            b.setFixedWidth(40)
            b.setStyleSheet("background-color: #333; color: #00ff00;")
            b.clicked.connect(lambda _, f=factor: self.apply_qty_preset(f))
            h.addWidget(b)
        ig_l.addLayout(h)

        self.qty_slider = QSlider(Qt.Horizontal)
        self.qty_slider.setRange(1, 100)
        self.qty_slider.setValue(10)  # Default to 10%
        self.qty_slider.setToolTip("Pct of free balance")
        self.qty_slider.setStyleSheet("background-color: #222;")
        self.qty_slider.valueChanged.connect(self.on_qty_slider)
        ig_l.addWidget(self.qty_slider)

        # ─── Stop-Loss & Take-Profit ──────────────────────────────
        sl_tp_layout = QGridLayout()
        sl_tp_layout.addWidget(QLabel("Stop-Loss:"), 0, 0)
        self.sl_sb = QDoubleSpinBox()
        self.sl_sb.setDecimals(4)
        self.sl_sb.setRange(0, 1e8)
        self.sl_sb.setValue(2.0)
        self.sl_sb.setStyleSheet("background-color: #222; color: #ff4444;")
        sl_tp_layout.addWidget(self.sl_sb, 0, 1)

        sl_tp_layout.addWidget(QLabel("Take-Profit:"), 1, 0)
        self.tp_sb = QDoubleSpinBox()
        self.tp_sb.setDecimals(4)
        self.tp_sb.setRange(0, 1e8)
        self.tp_sb.setValue(0.3)
        self.tp_sb.setStyleSheet("background-color: #222; color: #44ff44;")
        sl_tp_layout.addWidget(self.tp_sb, 1, 1)

        ig_l.addLayout(sl_tp_layout)

        left_layout.addWidget(inputs_group)

        # Toggles group
        toggles_group = QGroupBox("Features")
        toggles_group.setStyleSheet("QGroupBox { color: #ccc; }")
        tg_l = QGridLayout(toggles_group)
        opts = ["Auto Trade", "Audit Pos", "Trend", "Advice"]
        self.feature_checkboxes = {}
        for idx, name in enumerate(opts):
            cb = QCheckBox(name)
            cb.setStyleSheet("color: #eee;")
            cb.stateChanged.connect(lambda state, n=name: self.toggle_feature(n, state))
            tg_l.addWidget(cb, idx // 2, idx % 2)
            self.feature_checkboxes[name] = cb

            # Settings button removed - now in Settings menu

        left_layout.addWidget(toggles_group)

        # MiniWidgetsPanel is now at the top of the left panel

        left_layout.addStretch()
        trading_layout.addWidget(left, 1, 0)

        # ─── Right Panel: Chart & Controls ───────────────────────────────────
        right = QWidget()
        right_layout = QVBoxLayout(right)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(4)

        # Chart controls
        chart_ctrl = QHBoxLayout()
        self.tf_selector = QComboBox()
        self.tf_selector.setObjectName("timeframe_combo")
        self.tf_selector.addItems(["1m","5m","15m","1h","4h"])
        self.tf_selector.setFixedWidth(80)
        self.tf_selector.setToolTip("Timeframe")
        self.tf_selector.currentTextChanged.connect(self.on_timeframe_changed)
        chart_ctrl.addWidget(self.tf_selector)
        self.chart_type_selector = QComboBox()
        self.chart_type_selector.setObjectName("chart_type_combo")
        self.chart_type_selector.addItems(["Tick", "Candlestick", "Line Chart"])
        self.chart_type_selector.setFixedWidth(120)
        self.chart_type_selector.setToolTip("Chart Type")
        self.chart_type_selector.currentTextChanged.connect(self.switch_chart_type)
        chart_ctrl.addWidget(self.chart_type_selector)

        # ─── Tick‐count selector ───────────────────────────────
        self.tick_count_sb = QSpinBox()
        self.tick_count_sb.setRange(10, 2000)
        self.tick_count_sb.setValue(1500)  # Changed from 500 to 1500
        self.tick_count_sb.setSuffix(" ticks")
        self.tick_count_sb.setToolTip("Number of ticks to display")
        self.tick_count_sb.setFixedWidth(100)
        self.tick_count_sb.setEnabled(False)       # only enabled in Tick mode
        chart_ctrl.addWidget(self.tick_count_sb)

        # Add checkbox to toggle volume display
        self.show_volume_cb = QCheckBox("Show Volume")
        self.show_volume_cb.setChecked(True)  # Volume is shown by default
        self.show_volume_cb.setToolTip("Toggle volume display on/off")
        self.show_volume_cb.stateChanged.connect(self.toggle_volume_display)
        chart_ctrl.addWidget(self.show_volume_cb)

        # Initialize volume display setting
        self.show_volume = True
        self.volume_bars = []

        chart_ctrl.addStretch()
        right_layout.addLayout(chart_ctrl)

        # Create a vertical splitter for the top section (order book + trades) and chart
        from PySide6.QtWidgets import QSplitter
        self.right_splitter = QSplitter(Qt.Vertical)

        # Create a horizontal splitter for order book and trades
        self.top_splitter = QSplitter(Qt.Horizontal)

        # Order Book Widget (left side)
        self.orderbook = OrderBookWidget(self)
        self.top_splitter.addWidget(self.orderbook)

        # Trades Widget (right side)
        self.trades_widget = TradesWidget(self)
        self.top_splitter.addWidget(self.trades_widget)

        # Set initial sizes to make them equal width
        self.top_splitter.setSizes([1, 1])  # Equal width for order book and trades

        # Add the top splitter to the right splitter
        self.right_splitter.addWidget(self.top_splitter)

        # Plot widget with DateAxisItem for proper time display and GPU optimization
        from pyqtgraph import DateAxisItem

        # Create chart with GPU-optimized settings
        self.chart = pg.PlotWidget(
            axisItems={'bottom': DateAxisItem(orientation='bottom')},
            useOpenGL=True,  # Explicitly enable OpenGL for this widget
            background='#0d0d0d',
            enableMenu=False # Disable context menu for better performance
        )

        # Configure grid with reduced alpha for better performance
        self.chart.showGrid(x=True, y=True, alpha=0.15)

        # Set rendering hints for better GPU utilization
        self.chart.setAntialiasing(True)
        self.chart.setDownsampling(mode='peak', auto=True, ds=2)  # Enable downsampling for large datasets

        # Disable the default context menu on the chart's ViewBox
        self.chart.plotItem.vb.menu = None  # Remove the ViewBox menu
        self.chart.plotItem.ctrlMenu = None  # Remove the plot control menu

        # Set viewport update mode to minimize redraws
        # Use the enum value instead of an integer
        from PySide6.QtWidgets import QGraphicsView
        self.chart.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.SmartViewportUpdate)

        # Connect chart click event for order placement
        self.chart.scene().sigMouseClicked.connect(self.on_chart_click)

        # Create a layout for the chart and indicators
        self.chart_layout = QVBoxLayout()
        self.chart_layout.setContentsMargins(0, 0, 0, 0)
        self.chart_layout.setSpacing(2)
        self.chart_layout.addWidget(self.chart)

        # Create a widget to hold the chart layout
        chart_container = QWidget()
        chart_container.setLayout(self.chart_layout)
        self.right_splitter.addWidget(chart_container)

        # Create GPU-accelerated plot item with optimized settings
        pen = pg.mkPen('#00ff44', width=1)
        self.plot = self.chart.plot(
            [], [],
            pen=pen,
            antialias=True
        )

        # Set initial sizes to show more of the order book and trades area
        self.right_splitter.setSizes([200, 400])  # Adjusted to give more space to the chart section

        right_layout.addWidget(self.right_splitter)
        trading_layout.addWidget(right, 1, 1)

        # Add the trading tab to the main tabs
        self.main_tabs.addTab(self.trading_tab, "Trading")

        # ─── Bottom Tabs: Positions, Trades, Performance ──────────────────────────────────
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabBar::tab { background: #222; padding:6px; color:#ddd; }
            QTabBar::tab:selected { background: #444; }
        """)
        self.tabs.addTab(self._make_table(["Symbol","Side","Size","Entry","Price","PnL","PnL %","Leverage","Margin","PnL Ratio"]), "Positions")
        self.tabs.addTab(self._make_table(["Symbol","Side","Type","Size","Price","Status"]), "Orders")

        # Add Performance Dashboard tab
        from performance_dashboard import PerformanceDashboard
        self.performance_dashboard = PerformanceDashboard()
        self.tabs.addTab(self.performance_dashboard, "Performance")

        # Give more space to the positions/orders panel
        trading_layout.addWidget(self.tabs, 2, 0, 1, 2)

        # Set row stretch factors to give more space to the positions/orders panel
        trading_layout.setRowStretch(0, 0)   # Analytics banner (fixed height)
        trading_layout.setRowStretch(1, 5)   # Chart + orderbook
        trading_layout.setRowStretch(2, 3)   # Positions/orders - 3 parts

        # Set column widths - left panel fixed, right panel expandable
        trading_layout.setColumnStretch(0, 1)  # Left panel - 1 part
        trading_layout.setColumnStretch(1, 4)  # Right panel - 4 parts (increased from default to give more width)

        # ─── Positions Table Context Menu ────────────────────────────────
        self.positions_table = self.tabs.widget(0).findChild(QTableWidget)
        self.positions_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.positions_table.customContextMenuRequested.connect(self.on_position_menu)

        # ─── Status Bar ──────────────────────────────────────────────────────
        sb = QStatusBar()
        self.setStatusBar(sb)
        sb.showMessage("Ready")

        # Timers for demo updates
        self.x = list(range(200))
        self.y = [random.gauss(0, 1) for _ in self.x]

        # Set up timers for updates with optimized intervals
        # Use single shot timers for better performance - they'll be restarted after each update completes
        self.plot_timer = QTimer()
        self.plot_timer.setSingleShot(True)
        self.plot_timer.timeout.connect(self._scheduled_chart_update)

        # Order book update timer
        self.ob_timer = QTimer()
        self.ob_timer.setSingleShot(True)
        self.ob_timer.timeout.connect(self._scheduled_orderbook_update)

        # Account update timer
        self.acct_timer = QTimer()
        self.acct_timer.setSingleShot(True)
        self.acct_timer.timeout.connect(self._scheduled_account_update)

        # Positions update timer
        self.positions_timer = QTimer()
        self.positions_timer.setSingleShot(True)
        self.positions_timer.timeout.connect(self._scheduled_positions_update)

        # Orders update timer
        self.orders_timer = QTimer()
        self.orders_timer.setSingleShot(True)
        self.orders_timer.timeout.connect(self._scheduled_orders_update)

        # Mini widgets update timer
        self.mini_widgets_timer = QTimer()
        self.mini_widgets_timer.setSingleShot(True)
        self.mini_widgets_timer.timeout.connect(self._scheduled_mini_widgets_update)

        # Start all timers with intervals from global settings
        self.plot_timer.start(CHART_UPDATE_INTERVAL)       # Chart updates
        self.ob_timer.start(ORDERBOOK_UPDATE_INTERVAL)     # Order book updates
        self.acct_timer.start(ACCOUNT_UPDATE_INTERVAL)     # Account updates
        self.positions_timer.start(POSITIONS_UPDATE_INTERVAL) # Positions updates
        self.orders_timer.start(ORDERS_UPDATE_INTERVAL)    # Orders updates
        self.mini_widgets_timer.start(MINI_WIDGETS_UPDATE_INTERVAL)  # Mini widgets updates

        # Add timer for the AnalysisPanel
        self.analysis_timer = QTimer()
        self.analysis_timer.timeout.connect(lambda: self.analysis_panel.update_metrics())
        self.analysis_timer.start(ANALYSIS_UPDATE_INTERVAL)  # Use global setting

        # Connect tick count spinbox value changed signal
        self.tick_count_sb.valueChanged.connect(self.on_tick_count_changed)

        # Enable tick count spinbox if chart type is Tick
        self.tick_count_sb.setEnabled(self.chart_type_selector.currentText() == "Tick")

        # Apply initial theme
        self.apply_theme(0)

        # Apply slow preset for performance by default without showing dialog
        # Note: Global settings are already set to slow preset values
        print("\n===== USING SLOW PERFORMANCE PRESET BY DEFAULT =====")
        print(f"Chart updates: {CHART_UPDATE_INTERVAL}ms")
        print(f"Orderbook updates: {ORDERBOOK_UPDATE_INTERVAL}ms")
        print(f"Account updates: {ACCOUNT_UPDATE_INTERVAL}ms")
        print(f"Positions updates: {POSITIONS_UPDATE_INTERVAL}ms")
        print(f"Orders updates: {ORDERS_UPDATE_INTERVAL}ms")
        print(f"Mini widgets updates: {MINI_WIDGETS_UPDATE_INTERVAL}ms")
        print(f"Analysis updates: {ANALYSIS_UPDATE_INTERVAL}ms")
        print(f"Scanner updates: {SCANNER_UPDATE_INTERVAL}ms")
        print(f"Performance tab updates: {PERFORMANCE_TAB_UPDATE_INTERVAL}ms")
        print(f"Analysis metrics updates: {ANALYSIS_METRICS_UPDATE_INTERVAL}ms")
        print(f"GPU Acceleration: {USE_GPU}")
        print(f"Position cache TTL: {position_cache['ttl']}s")
        print(f"Orderbook cache TTL: {orderbook_cache['ttl']}s")
        print(f"Account cache TTL: {account_cache['ttl']}s")
        print("===================================================\n")

        # Add keyboard shortcuts
        QShortcut(QKeySequence("Ctrl+B"), self, activated=self.place_market_long)
        QShortcut(QKeySequence("Ctrl+S"), self, activated=self.place_market_short)
        QShortcut(QKeySequence("Ctrl+L"), self, activated=self.place_limit_long)
        QShortcut(QKeySequence("Ctrl+K"), self, activated=self.place_limit_short)
        QShortcut(QKeySequence("Ctrl+W"), self, activated=self.cancel_all_orders)
        QShortcut(QKeySequence("Ctrl+E"), self, activated=self.close_all_positions)

        # Add shortcut for trend line drawing mode (T key)
        QShortcut(QKeySequence("T"), self, activated=self.toggle_trend_line_mode)

        # Style the menus to match the dark/green look
        self.menuBar().setStyleSheet("""
          QMenuBar { background: #111; color: #0f0; }
          QMenuBar::item:selected { background: #222; }
          QMenu { background: #1a1a1a; color: #ddd; }
          QMenu::item:selected { background: #333; color: #0f0; }
        """)

    def _make_table(self, headers):
        w = QWidget()
        l = QVBoxLayout(w)
        l.setContentsMargins(4, 4, 4, 4)

        # Create table with 10 empty rows by default to make it taller
        tbl = QTableWidget(10, len(headers))
        tbl.setHorizontalHeaderLabels(headers)
        tbl.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Center text in header and cells
        tbl.horizontalHeader().setDefaultAlignment(Qt.AlignCenter)

        # Set row height to be compact
        tbl.verticalHeader().setDefaultSectionSize(24)  # Smaller row height
        tbl.verticalHeader().setVisible(False)  # Hide row numbers

        # Make the table take up all available space
        tbl.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Style with centered text
        tbl.setStyleSheet("""
            QTableWidget {
                background:#1e1e1e;
                color:#eef;
            }
            QTableWidget::item {
                text-align: center;
                alignment: center;
            }
            QHeaderView::section {
                background-color: #222;
                color: #ddd;
                font-weight: bold;
                text-align: center;
                padding: 4px;
            }
        """)

        l.addWidget(tbl)
        return w

    def apply_theme(self, idx):
        # Update the theme actions checked state
        if hasattr(self, 'theme_actions'):
            for i, action in enumerate(self.theme_actions):
                action.setChecked(i == idx)

        if idx == 0:  # Matrix
            pg.setConfigOption('background', '#000000')
            pg.setConfigOption('foreground', '#00ff00')
            self.setStyleSheet("background-color:#000; color:#00ff00;")
        else:  # TradingView
            pg.setConfigOption('background', '#1f1f2e')
            pg.setConfigOption('foreground', '#49a3f1')
            self.setStyleSheet("background-color:#1f1f2e; color:#cfd8dc;")

    def switch_chart_type(self, t):
        print(f"Switching chart type to {t}")
        # Enable/disable tick count spinbox based on chart type
        self.tick_count_sb.setEnabled(t == "Tick")

        # Clear any existing candlestick item if switching away from candlestick mode
        if hasattr(self, 'candlestick_item') and self.candlestick_item is not None and t != "Candlestick":
            self.chart.removeItem(self.candlestick_item)
            self.candlestick_item = None

        # Store the current chart type
        self.current_chart_type = t

        # Force a chart update immediately
        self._update_chart()
        # Reset the last update time to ensure next regular update happens
        self.last_chart_update = datetime.now() - timedelta(seconds=10)

        # Update status bar
        self.statusBar().showMessage(f"Chart type switched to {t}")

    def on_timeframe_changed(self, tf):
        print(f"Timeframe changed to {tf}")
        # Store the current timeframe
        self.current_timeframe = tf

        # Force a chart update immediately
        self._update_chart()
        # Reset the last update time to ensure next regular update happens
        self.last_chart_update = datetime.now() - timedelta(seconds=10)

    def refresh_tickers(self):
        """Fetch and update ticker data for all pairs in the dropdown"""
        try:
            # Check if we've updated recently (within 30 seconds)
            current_time = datetime.now()
            if hasattr(self, 'last_tickers_update') and (current_time - self.last_tickers_update).total_seconds() < 30:
                print("Skipping ticker update - last update was less than 30 seconds ago")
                return

            # Update the timestamp
            self.last_tickers_update = current_time

            # Disable the refresh button while updating
            self.refresh_tickers_btn.setEnabled(False)
            self.refresh_tickers_btn.setText("⌛")

            # Create and start the worker thread
            self._tickers_worker = workers.TickersWorker(self.default_pairs)
            self._tickers_worker.fetched.connect(self._populate_tickers)
            self._tickers_worker.error.connect(self._handle_tickers_error)
            self._tickers_worker.finished.connect(lambda: self._on_tickers_worker_finished())
            self._tickers_worker.start()
        except Exception as e:
            print(f"Error refreshing tickers: {e}")
            self.refresh_tickers_btn.setEnabled(True)
            self.refresh_tickers_btn.setText("⟳")

    def _on_tickers_worker_finished(self):
        """Re-enable the refresh button when the worker is done"""
        self.refresh_tickers_btn.setEnabled(True)
        self.refresh_tickers_btn.setText("⟳")

    def _handle_tickers_error(self, error_msg):
        """Handle errors from the tickers worker thread"""
        print(f"Tickers worker error: {error_msg}")

    def _populate_tickers(self, tickers):
        """Update the symbol dropdown with ticker data"""
        try:
            # Store the tickers in the cache
            self.tickers_cache = tickers

            # Remember the current selection
            current_symbol = self.symbol_cb.currentText()

            # Clear the dropdown
            self.symbol_cb.clear()

            # Add items with price change information
            for symbol in self.default_pairs:
                if symbol in tickers:
                    ticker = tickers[symbol]
                    # Get the price change percentage
                    change_pct = ticker.get('percentage', 0)
                    # Format the display text with price change
                    if change_pct > 0:
                        display_text = f"{symbol} (+{change_pct:.2f}%)"
                        self.symbol_cb.addItem(display_text)
                        # Set green color for positive change
                        index = self.symbol_cb.count() - 1
                        self.symbol_cb.setItemData(index, QColor("#00ff44"), Qt.ForegroundRole)
                    elif change_pct < 0:
                        display_text = f"{symbol} ({change_pct:.2f}%)"
                        self.symbol_cb.addItem(display_text)
                        # Set red color for negative change
                        index = self.symbol_cb.count() - 1
                        self.symbol_cb.setItemData(index, QColor("#ff4444"), Qt.ForegroundRole)
                    else:
                        display_text = f"{symbol} (0.00%)"
                        self.symbol_cb.addItem(display_text)
                else:
                    # If no ticker data, just add the symbol
                    self.symbol_cb.addItem(symbol)

            # Restore the previous selection if it exists
            index = self.symbol_cb.findText(current_symbol, Qt.MatchStartsWith)
            if index >= 0:
                self.symbol_cb.setCurrentIndex(index)
            elif self.symbol_cb.count() > 0:
                # Otherwise select the first item
                self.symbol_cb.setCurrentIndex(0)
        except Exception as e:
            print(f"Error populating tickers: {e}")

    def on_symbol_changed(self, symbol):
        print(f"Symbol changed to {symbol}")
        # Extract the base symbol without the price change info
        base_symbol = symbol.split(' ')[0] if ' ' in symbol else symbol

        # Force a chart update immediately
        self._update_chart()
        # Reset the last update time to ensure next regular update happens
        self.last_chart_update = datetime.now() - timedelta(seconds=10)
        # Also update the order book and trades widget if they exist
        if hasattr(self, 'orderbook'):
            self.orderbook.update(base_symbol)
        if hasattr(self, 'trades_widget'):
            self.trades_widget.update(base_symbol)

    def on_tick_count_changed(self, ticks: int):
        # only refresh if we're in Tick mode
        if self.chart_type_selector.currentText() == "Tick":
            # Store the tick count
            self.tick_count = ticks
            print(f"Tick count changed to {ticks}")
            # drop the last-update throttle
            self.last_chart_update = datetime.now() - timedelta(seconds=10)
            self._update_chart()

    def toggle_volume_display(self, state):
        """Toggle the display of volume bars on the chart"""
        # Store the volume display state
        self.show_volume = bool(state)
        print(f"Volume display {'enabled' if self.show_volume else 'disabled'}")

        # If disabling volume, remove all volume bars from the chart
        if not self.show_volume:
            # Store references to all volume bars for easy removal
            if hasattr(self, 'volume_bars') and self.volume_bars:
                for bar in self.volume_bars:
                    self.chart.removeItem(bar)
                self.volume_bars = []

            # Also clear any volume bars that might have been added directly to the chart
            # This is a more aggressive approach to ensure all volume bars are removed
            items_to_remove = []
            for item in self.chart.items():
                # Check if the item is a BarGraphItem (volume bar)
                if isinstance(item, pg.BarGraphItem):
                    items_to_remove.append(item)

            # Remove all identified volume bars
            for item in items_to_remove:
                self.chart.removeItem(item)

        # Force a chart update immediately
        self.last_chart_update = datetime.now() - timedelta(seconds=10)
        self._update_chart()

    def _scheduled_chart_update(self):
        """Scheduled chart update that restarts the timer when done"""
        # Only update if not already updating
        if not hasattr(self, '_chart_update_in_progress') or not self._chart_update_in_progress:
            self._chart_update_in_progress = True
            try:
                self._update_chart()
            finally:
                self._chart_update_in_progress = False
                # Restart the timer for the next update using global setting
                # print(f"Restarting chart timer with interval: {CHART_UPDATE_INTERVAL}ms")
                self.plot_timer.start(CHART_UPDATE_INTERVAL)

    def _scheduled_orderbook_update(self):
        """Scheduled orderbook update that restarts the timer when done"""
        # Only update if not already updating
        if not hasattr(self, '_orderbook_update_in_progress') or not self._orderbook_update_in_progress:
            self._orderbook_update_in_progress = True
            try:
                if hasattr(self, 'orderbook'):
                    self.orderbook.update(self.get_symbol())
            finally:
                self._orderbook_update_in_progress = False
                # Restart the timer for the next update using global setting
                self.ob_timer.start(ORDERBOOK_UPDATE_INTERVAL)

    def _scheduled_account_update(self):
        """Scheduled account update that restarts the timer when done"""
        # Only update if not already updating
        if not hasattr(self, '_account_update_in_progress') or not self._account_update_in_progress:
            self._account_update_in_progress = True
            try:
                self._update_account()
            finally:
                self._account_update_in_progress = False
                # Restart the timer for the next update using global setting
                self.acct_timer.start(ACCOUNT_UPDATE_INTERVAL)

    def _scheduled_positions_update(self):
        """Scheduled positions update that restarts the timer when done"""
        # Only update if not already updating
        if not hasattr(self, '_positions_update_in_progress') or not self._positions_update_in_progress:
            self._positions_update_in_progress = True
            try:
                self._update_positions()
            finally:
                self._positions_update_in_progress = False
                # Restart the timer for the next update using global setting
                # print(f"Restarting positions timer with interval: {POSITIONS_UPDATE_INTERVAL}ms")
                self.positions_timer.start(POSITIONS_UPDATE_INTERVAL)

    def _scheduled_orders_update(self):
        """Scheduled orders update that restarts the timer when done"""
        # Only update if not already updating
        if not hasattr(self, '_orders_update_in_progress') or not self._orders_update_in_progress:
            self._orders_update_in_progress = True
            try:
                self._update_orders()
            finally:
                self._orders_update_in_progress = False
                # Restart the timer for the next update using global setting
                self.orders_timer.start(ORDERS_UPDATE_INTERVAL)

    def _scheduled_mini_widgets_update(self):
        """Scheduled mini widgets update that restarts the timer when done"""
        # Only update if not already updating
        if not hasattr(self, '_mini_widgets_update_in_progress') or not self._mini_widgets_update_in_progress:
            self._mini_widgets_update_in_progress = True
            try:
                if hasattr(self, 'mini_widgets'):
                    self.mini_widgets.update_all(self.get_symbol())
            finally:
                self._mini_widgets_update_in_progress = False
                # Restart the timer for the next update using global setting
                self.mini_widgets_timer.start(MINI_WIDGETS_UPDATE_INTERVAL)

    def _update_chart(self):
        try:
            # Get the chart's view box (commented out to avoid unused variable warning)
            # view_box = self.chart.getViewBox()
            # Store view range for later use if needed
            # current_range = view_box.viewRange() if view_box else None

            # Use a class variable to track last update time to prevent excessive updates
            current_time = datetime.now()
            if hasattr(self, 'last_chart_update') and (current_time - self.last_chart_update).total_seconds() < 1.0:
                return  # Skip update if less than 1 second has passed

            self.last_chart_update = current_time
            sym = self.get_symbol()

            # Store current symbol, timeframe and chart type as instance variables
            # so they're accessible in other methods to fix undefined variable errors
            self.current_symbol = sym

            # Check if chart controls exist
            if not hasattr(self, 'chart_type_selector'):
                print("Chart controls not initialized yet")
                return

            # Get the chart type
            chart_type = self.chart_type_selector.currentText()
            self.current_chart_type = chart_type

            # Check if timeframe selector exists
            if hasattr(self, 'tf_selector'):
                self.current_timeframe = self.tf_selector.currentText()
            else:
                self.current_timeframe = "1h"  # Default timeframe

            # Skip if no symbol selected
            if not sym:
                return

            # Get the selected timeframe from the UI
            tf = self.tf_selector.currentText()
            self.current_timeframe = tf

            # Get the chart type (tick or candle)
            chart_type = self.chart_type_selector.currentText()
            self.current_chart_type = chart_type

            # No special handling needed for Candlestick mode anymore
            # We'll use the worker thread for both chart types

            # Check if a worker is already running
            if hasattr(self, '_chart_worker') and self._chart_worker.isRunning():
                return  # Don't start a new worker if one is already running

            # Read desired tick-count (only used for Tick charts)
            tick_limit = self.tick_count_sb.value() if chart_type == "Tick" else None

            # Create and start the worker thread
            self._chart_worker = workers.ChartDataWorker(
                symbol=sym,
                timeframe=tf,
                chart_type=chart_type,
                tick_limit=tick_limit
            )
            self._chart_worker.fetched_ohlcv.connect(self._populate_ohlcv_chart)
            self._chart_worker.fetched_trades.connect(self._populate_tick_chart)
            self._chart_worker.error.connect(self._handle_chart_error)
            self._chart_worker.start()
        except Exception as e:
            print(f"Error in _update_chart: {e}")
            import traceback
            traceback.print_exc()

    def _handle_chart_error(self, error_msg):
        """Handle errors from the chart worker thread"""
        if DEBUG:
            print(f"Chart worker error: {error_msg}")

        # Fallback to random data
        if not hasattr(self, 'x') or not hasattr(self, 'y'):
            self.x = list(range(100))
            self.y = [random.gauss(0, 1) for _ in range(100)]
        else:
            self.y = self.y[1:] + [random.gauss(0, 1)]

        self.chart.clear()
        self.chart.plot(self.x, self.y, pen=pg.mkPen('#00ff44'))

    def _populate_ohlcv_chart(self, ohlcv_data):
        """
        Enhanced implementation for OHLCV line chart using PyQtGraph.
        Shows close price line, high/low range, and volume.

        Args:
            ohlcv_data: OHLCV data from the exchange in format [[timestamp, open, high, low, close, volume], ...]
        """
        # Save current view range to restore after update
        view_box = self.chart.getViewBox()
        current_range = view_box.viewRange() if view_box else None

        # Preserve trend lines when updating the chart
        trend_lines_to_restore = []
        if hasattr(self, 'trend_line_items') and self.trend_line_items:
            trend_lines_to_restore = self.trend_line_items.copy()
            self.trend_line_items = []

        try:
            if not ohlcv_data or len(ohlcv_data) == 0:
                print("No OHLCV data received")
                return

            sym = self.get_symbol()
            tf = self.current_timeframe if hasattr(self, 'current_timeframe') else "1m"

            # Store the current chart type and symbol to track changes
            current_chart_type = getattr(self, 'current_chart_type', None)
            current_symbol = getattr(self, 'current_symbol', None)

            # For candlestick charts, we'll handle clearing differently to prevent flickering
            is_candlestick = current_chart_type == "Candlestick"
            symbol_changed = current_symbol != sym

            # Only clear the chart if:
            # 1. It's not a candlestick chart, or
            # 2. The symbol has changed (requiring a full redraw)
            if not is_candlestick or symbol_changed:
                # Clear the chart completely
                self.chart.clear()

                # If we're switching away from candlestick mode, clear the reference
                if hasattr(self, 'candlestick_item') and not is_candlestick:
                    self.candlestick_item = None
            else:
                # For candlestick charts with the same symbol, we'll selectively clear items
                # but keep the candlestick item to prevent flickering

                # Get all items in the chart
                items_to_remove = []
                for item in self.chart.items():
                    # Skip the candlestick item if we have one
                    if hasattr(self, 'candlestick_item') and item == self.candlestick_item:
                        continue
                    items_to_remove.append(item)

                # Remove all items except the candlestick
                for item in items_to_remove:
                    self.chart.removeItem(item)

            # Remove any existing indicator plots
            for i in range(self.chart_layout.count()):
                if i > 0:  # Skip the main chart (index 0)
                    widget = self.chart_layout.itemAt(i).widget()
                    if widget:
                        widget.setParent(None)
                        widget.deleteLater()

            # Extract data from candles
            timestamps = [candle[0] / 1000.0 for candle in ohlcv_data]  # Convert ms to seconds
            # opens = [candle[1] for candle in ohlcv_data]  # Not used in this implementation
            highs = [candle[2] for candle in ohlcv_data]
            lows = [candle[3] for candle in ohlcv_data]
            closes = [candle[4] for candle in ohlcv_data]
            volumes = [candle[5] for candle in ohlcv_data]

            # Calculate price change percentage for the period
            if len(closes) > 1:
                first_price = closes[0]
                last_price = closes[-1]
                price_change = last_price - first_price
                price_change_pct = (price_change / first_price) * 100

                # Determine color based on price change
                color = '#00ff44' if price_change >= 0 else '#ff4444'  # Green for up, red for down
            else:
                price_change_pct = 0
                color = '#00ff44'

            # Create a line plot for closing prices
            self.chart.plot(
                timestamps,
                closes,
                pen=pg.mkPen(color, width=2),
                name=f"{sym} ({tf}): {price_change_pct:.2f}%"
            )

            # Add indicators based on settings
            if hasattr(self, 'indicator_settings'):
                # Moving Average
                if self.indicator_settings["Moving Average"]["enabled"] and len(closes) >= self.indicator_settings["Moving Average"]["period"]:
                    ma_type = self.indicator_settings["Moving Average"]["type"]
                    ma_period = self.indicator_settings["Moving Average"]["period"]
                    ma_color = self.indicator_settings["Moving Average"]["color"]
                    ma_style = self.indicator_settings["Moving Average"]["style"]

                    # Convert style string to Qt line style
                    line_style = Qt.SolidLine
                    if ma_style == "Dash":
                        line_style = Qt.DashLine
                    elif ma_style == "Dot":
                        line_style = Qt.DotLine

                    # Calculate MA based on type
                    ma_values = []
                    if ma_type == "SMA":
                        # Simple Moving Average
                        for i in range(len(closes)):
                            if i < ma_period - 1:
                                ma_values.append(None)  # Not enough data for MA yet
                            else:
                                # Calculate MA for this point
                                ma_values.append(sum(closes[i-(ma_period-1):i+1]) / ma_period)
                    elif ma_type == "EMA":
                        # Exponential Moving Average
                        k = 2 / (ma_period + 1)
                        ema = closes[0]  # Start with first price
                        ma_values.append(ema)

                        for i in range(1, len(closes)):
                            ema = closes[i] * k + ema * (1 - k)
                            ma_values.append(ema)
                    elif ma_type == "WMA":
                        # Weighted Moving Average
                        for i in range(len(closes)):
                            if i < ma_period - 1:
                                ma_values.append(None)
                            else:
                                # Calculate weighted sum
                                weights_sum = sum(range(1, ma_period + 1))
                                weighted_sum = 0
                                for j in range(ma_period):
                                    weighted_sum += closes[i - j] * (ma_period - j)
                                ma_values.append(weighted_sum / weights_sum)

                    # Remove None values for plotting
                    ma_timestamps = []
                    ma_values_filtered = []
                    for i, ma in enumerate(ma_values):
                        if ma is not None:
                            ma_timestamps.append(timestamps[i])
                            ma_values_filtered.append(ma)

                    # Plot the MA line
                    self.chart.plot(
                        ma_timestamps,
                        ma_values_filtered,
                        pen=pg.mkPen(ma_color, width=1, style=line_style),
                        name=f"{ma_type}({ma_period})"
                    )

                # Bollinger Bands
                if self.indicator_settings["Bollinger Bands"]["enabled"] and len(closes) >= self.indicator_settings["Bollinger Bands"]["period"]:
                    bb_period = self.indicator_settings["Bollinger Bands"]["period"]
                    bb_std = self.indicator_settings["Bollinger Bands"]["std_dev"]
                    bb_color = self.indicator_settings["Bollinger Bands"]["color"]
                    bb_fill = self.indicator_settings["Bollinger Bands"]["fill"]
                    bb_fill_alpha = self.indicator_settings["Bollinger Bands"]["fill_alpha"]

                    # Calculate SMA
                    sma_values = []
                    for i in range(len(closes)):
                        if i < bb_period - 1:
                            sma_values.append(None)
                        else:
                            sma_values.append(sum(closes[i-(bb_period-1):i+1]) / bb_period)

                    # Calculate standard deviation
                    std_values = []
                    for i in range(len(closes)):
                        if i < bb_period - 1:
                            std_values.append(None)
                        else:
                            segment = closes[i-(bb_period-1):i+1]
                            mean = sum(segment) / bb_period
                            variance = sum([(x - mean) ** 2 for x in segment]) / bb_period
                            std_values.append(variance ** 0.5)

                    # Calculate upper and lower bands
                    upper_band = []
                    lower_band = []
                    for i in range(len(sma_values)):
                        if sma_values[i] is None:
                            upper_band.append(None)
                            lower_band.append(None)
                        else:
                            upper_band.append(sma_values[i] + bb_std * std_values[i])
                            lower_band.append(sma_values[i] - bb_std * std_values[i])

                    # Remove None values for plotting
                    bb_timestamps = []
                    sma_filtered = []
                    upper_filtered = []
                    lower_filtered = []
                    for i in range(len(sma_values)):
                        if sma_values[i] is not None:
                            bb_timestamps.append(timestamps[i])
                            sma_filtered.append(sma_values[i])
                            upper_filtered.append(upper_band[i])
                            lower_filtered.append(lower_band[i])

                    # Plot the bands
                    self.chart.plot(
                        bb_timestamps,
                        sma_filtered,
                        pen=pg.mkPen(bb_color, width=1),
                        name=f"BB SMA({bb_period})"
                    )

                    upper_curve = self.chart.plot(
                        bb_timestamps,
                        upper_filtered,
                        pen=pg.mkPen(bb_color, width=1, style=Qt.DashLine),
                        name=f"BB Upper({bb_std}σ)"
                    )

                    lower_curve = self.chart.plot(
                        bb_timestamps,
                        lower_filtered,
                        pen=pg.mkPen(bb_color, width=1, style=Qt.DashLine),
                        name=f"BB Lower({bb_std}σ)"
                    )

                    # Fill between bands if enabled
                    if bb_fill and len(bb_timestamps) > 0:
                        fill = pg.FillBetweenItem(upper_curve, lower_curve, brush=pg.mkBrush(bb_color + f"{int(bb_fill_alpha * 255):02x}"))
                        self.chart.addItem(fill)

                # ATR-EMA Bands
                if self.indicator_settings["ATR-EMA Bands"]["enabled"] and len(closes) >= max(self.indicator_settings["ATR-EMA Bands"]["ema_period"], self.indicator_settings["ATR-EMA Bands"]["atr_period"]):
                    ema_period = self.indicator_settings["ATR-EMA Bands"]["ema_period"]
                    atr_period = self.indicator_settings["ATR-EMA Bands"]["atr_period"]
                    multipliers = self.indicator_settings["ATR-EMA Bands"]["multipliers"]
                    ema_color = self.indicator_settings["ATR-EMA Bands"]["ema_color"]
                    upper_color = self.indicator_settings["ATR-EMA Bands"]["upper_band_color"]
                    lower_color = self.indicator_settings["ATR-EMA Bands"]["lower_band_color"]
                    fill_enabled = self.indicator_settings["ATR-EMA Bands"]["fill"]
                    fill_alpha = self.indicator_settings["ATR-EMA Bands"]["fill_alpha"]

                    # Create DataFrame from OHLCV data
                    df = pd.DataFrame({
                        'open': [candle[1] for candle in ohlcv_data],
                        'high': [candle[2] for candle in ohlcv_data],
                        'low': [candle[3] for candle in ohlcv_data],
                        'close': [candle[4] for candle in ohlcv_data],
                        'volume': [candle[5] for candle in ohlcv_data]
                    })

                    # Calculate ATR-EMA Bands
                    bands = calculate_atr_ema_bands(df, ema_period, atr_period, multipliers)

                    # Plot EMA line
                    ema_values = bands['ema'].values
                    self.chart.plot(
                        timestamps,
                        ema_values,
                        pen=pg.mkPen(ema_color, width=1),
                        name=f"EMA({ema_period})"
                    )

                    # Plot upper bands
                    upper_curves = []
                    for mult in multipliers:
                        band_key = f'ema_plus_{mult}_atr'
                        if band_key in bands:
                            upper_values = bands[band_key].values
                            curve = self.chart.plot(
                                timestamps,
                                upper_values,
                                pen=pg.mkPen(upper_color, width=1, style=Qt.DashLine),
                                name=f"EMA+{mult}ATR"
                            )
                            upper_curves.append((mult, curve))

                    # Plot lower bands
                    lower_curves = []
                    for mult in multipliers:
                        band_key = f'ema_minus_{mult}_atr'
                        if band_key in bands:
                            lower_values = bands[band_key].values
                            curve = self.chart.plot(
                                timestamps,
                                lower_values,
                                pen=pg.mkPen(lower_color, width=1, style=Qt.DashLine),
                                name=f"EMA-{mult}ATR"
                            )
                            lower_curves.append((mult, curve))

                    # Fill between bands if enabled
                    if fill_enabled and upper_curves and lower_curves:
                        # Sort by multiplier
                        upper_curves.sort(key=lambda x: x[0], reverse=True)
                        lower_curves.sort(key=lambda x: x[0])

                        # Get the outermost bands
                        outermost_upper = upper_curves[0][1]
                        outermost_lower = lower_curves[0][1]

                        # Create fill between the outermost bands
                        fill = pg.FillBetweenItem(
                            outermost_upper,
                            outermost_lower,
                            brush=pg.mkBrush(ema_color + f"{int(fill_alpha * 255):02x}")
                        )
                        self.chart.addItem(fill)

                # RSI (Relative Strength Index)
                if self.indicator_settings["RSI"]["enabled"] and len(closes) >= self.indicator_settings["RSI"]["period"]:
                    rsi_period = self.indicator_settings["RSI"]["period"]
                    rsi_color = self.indicator_settings["RSI"]["color"]
                    rsi_overbought = self.indicator_settings["RSI"]["overbought"]
                    rsi_oversold = self.indicator_settings["RSI"]["oversold"]

                    # Calculate RSI
                    rsi_values = []

                    # First calculate price changes
                    changes = [0]
                    for i in range(1, len(closes)):
                        changes.append(closes[i] - closes[i-1])

                    # Calculate initial average gain and loss
                    avg_gain = sum([max(0, change) for change in changes[1:rsi_period+1]]) / rsi_period
                    avg_loss = sum([abs(min(0, change)) for change in changes[1:rsi_period+1]]) / rsi_period

                    # Add initial RSI values (None for the first period-1 points)
                    for i in range(rsi_period):
                        rsi_values.append(None)

                    # Calculate first RSI value
                    if avg_loss == 0:
                        rsi = 100
                    else:
                        rs = avg_gain / avg_loss
                        rsi = 100 - (100 / (1 + rs))
                    rsi_values.append(rsi)

                    # Calculate remaining RSI values using smoothed method
                    for i in range(rsi_period + 1, len(closes)):
                        change = closes[i] - closes[i-1]
                        gain = max(0, change)
                        loss = abs(min(0, change))

                        avg_gain = (avg_gain * (rsi_period - 1) + gain) / rsi_period
                        avg_loss = (avg_loss * (rsi_period - 1) + loss) / rsi_period

                        if avg_loss == 0:
                            rsi = 100
                        else:
                            rs = avg_gain / avg_loss
                            rsi = 100 - (100 / (1 + rs))
                        rsi_values.append(rsi)

                    # Create a separate plot for RSI
                    rsi_plot = pg.PlotWidget(
                        axisItems={'bottom': pg.DateAxisItem(orientation='bottom')},
                        useOpenGL=True,
                        background='#0d0d0d'
                    )
                    rsi_plot.setFixedHeight(100)
                    rsi_plot.showGrid(x=True, y=True, alpha=0.15)

                    # Remove None values for plotting
                    rsi_timestamps = []
                    rsi_filtered = []
                    for i, rsi in enumerate(rsi_values):
                        if rsi is not None:
                            rsi_timestamps.append(timestamps[i])
                            rsi_filtered.append(rsi)

                    # Plot RSI line
                    rsi_plot.plot(
                        rsi_timestamps,
                        rsi_filtered,
                        pen=pg.mkPen(rsi_color, width=1),
                        name=f"RSI({rsi_period})"
                    )

                    # Add overbought and oversold lines
                    rsi_plot.addLine(y=rsi_overbought, pen=pg.mkPen('r', width=1, style=Qt.DashLine))
                    rsi_plot.addLine(y=rsi_oversold, pen=pg.mkPen('g', width=1, style=Qt.DashLine))

                    # Set Y range to 0-100
                    rsi_plot.setYRange(0, 100)

                    # Add RSI plot below the main chart
                    self.chart_layout.addWidget(rsi_plot)

                # MACD (Moving Average Convergence Divergence)
                if self.indicator_settings["MACD"]["enabled"] and len(closes) >= self.indicator_settings["MACD"]["slow_period"]:
                    macd_fast = self.indicator_settings["MACD"]["fast_period"]
                    macd_slow = self.indicator_settings["MACD"]["slow_period"]
                    macd_signal = self.indicator_settings["MACD"]["signal_period"]
                    macd_color = self.indicator_settings["MACD"]["macd_color"]
                    signal_color = self.indicator_settings["MACD"]["signal_color"]

                    # Calculate fast EMA
                    fast_ema = []
                    k_fast = 2 / (macd_fast + 1)
                    ema = closes[0]
                    fast_ema.append(ema)

                    for i in range(1, len(closes)):
                        ema = closes[i] * k_fast + ema * (1 - k_fast)
                        fast_ema.append(ema)

                    # Calculate slow EMA
                    slow_ema = []
                    k_slow = 2 / (macd_slow + 1)
                    ema = closes[0]
                    slow_ema.append(ema)

                    for i in range(1, len(closes)):
                        ema = closes[i] * k_slow + ema * (1 - k_slow)
                        slow_ema.append(ema)

                    # Calculate MACD line
                    macd_line = []
                    for i in range(len(closes)):
                        macd_line.append(fast_ema[i] - slow_ema[i])

                    # Calculate signal line (EMA of MACD)
                    signal_line = []
                    k_signal = 2 / (macd_signal + 1)
                    ema = macd_line[0]
                    signal_line.append(ema)

                    for i in range(1, len(macd_line)):
                        ema = macd_line[i] * k_signal + ema * (1 - k_signal)
                        signal_line.append(ema)

                    # Calculate histogram
                    histogram = []
                    for i in range(len(macd_line)):
                        histogram.append(macd_line[i] - signal_line[i])

                    # Create a separate plot for MACD
                    macd_plot = pg.PlotWidget(
                        axisItems={'bottom': pg.DateAxisItem(orientation='bottom')},
                        useOpenGL=True,
                        background='#0d0d0d'
                    )
                    macd_plot.setFixedHeight(100)
                    macd_plot.showGrid(x=True, y=True, alpha=0.15)

                    # Plot MACD line
                    macd_plot.plot(
                        timestamps,
                        macd_line,
                        pen=pg.mkPen(macd_color, width=1),
                        name=f"MACD({macd_fast},{macd_slow})"
                    )

                    # Plot signal line
                    macd_plot.plot(
                        timestamps,
                        signal_line,
                        pen=pg.mkPen(signal_color, width=1),
                        name=f"Signal({macd_signal})"
                    )

                    # Plot histogram as bar chart
                    for i in range(len(timestamps)):
                        if histogram[i] >= 0:
                            color = '#00ff00'  # Green for positive
                        else:
                            color = '#ff0000'  # Red for negative

                        bar = pg.BarGraphItem(
                            x=[timestamps[i]],
                            height=[histogram[i]],
                            width=0.7 * (timestamps[1] - timestamps[0]) if i < len(timestamps) - 1 else 0.7,
                            brush=color
                        )
                        macd_plot.addItem(bar)

                    # Add MACD plot below the main chart
                    self.chart_layout.addWidget(macd_plot)

                # Stochastic Oscillator
                if self.indicator_settings["Stochastic"]["enabled"] and len(closes) >= self.indicator_settings["Stochastic"]["k_period"]:
                    k_period = self.indicator_settings["Stochastic"]["k_period"]
                    d_period = self.indicator_settings["Stochastic"]["d_period"]
                    slowing = self.indicator_settings["Stochastic"]["slowing"]
                    stoch_overbought = self.indicator_settings["Stochastic"]["overbought"]
                    stoch_oversold = self.indicator_settings["Stochastic"]["oversold"]
                    k_color = self.indicator_settings["Stochastic"]["k_color"]
                    d_color = self.indicator_settings["Stochastic"]["d_color"]

                    # Calculate %K
                    k_values = []
                    for i in range(len(closes)):
                        if i < k_period - 1:
                            k_values.append(None)
                        else:
                            highest_high = max(highs[i-(k_period-1):i+1])
                            lowest_low = min(lows[i-(k_period-1):i+1])

                            if highest_high == lowest_low:
                                k_values.append(50)  # Default to middle if no range
                            else:
                                k = 100 * (closes[i] - lowest_low) / (highest_high - lowest_low)
                                k_values.append(k)

                    # Apply slowing to %K if needed
                    if slowing > 1:
                        slowed_k = []
                        for i in range(len(k_values)):
                            if k_values[i] is None or i < slowing - 1:
                                slowed_k.append(None)
                            else:
                                # Average of last 'slowing' %K values
                                valid_values = [k for k in k_values[i-(slowing-1):i+1] if k is not None]
                                if valid_values:
                                    slowed_k.append(sum(valid_values) / len(valid_values))
                                else:
                                    slowed_k.append(None)
                        k_values = slowed_k

                    # Calculate %D (SMA of %K)
                    d_values = []
                    for i in range(len(k_values)):
                        if k_values[i] is None or i < d_period - 1:
                            d_values.append(None)
                        else:
                            # Average of last 'd_period' %K values
                            valid_values = [k for k in k_values[i-(d_period-1):i+1] if k is not None]
                            if valid_values:
                                d_values.append(sum(valid_values) / len(valid_values))
                            else:
                                d_values.append(None)

                    # Create a separate plot for Stochastic
                    stoch_plot = pg.PlotWidget(
                        axisItems={'bottom': pg.DateAxisItem(orientation='bottom')},
                        useOpenGL=True,
                        background='#0d0d0d'
                    )
                    stoch_plot.setFixedHeight(100)
                    stoch_plot.showGrid(x=True, y=True, alpha=0.15)

                    # Remove None values for plotting
                    k_timestamps = []
                    k_filtered = []
                    for i, k in enumerate(k_values):
                        if k is not None:
                            k_timestamps.append(timestamps[i])
                            k_filtered.append(k)

                    d_timestamps = []
                    d_filtered = []
                    for i, d in enumerate(d_values):
                        if d is not None:
                            d_timestamps.append(timestamps[i])
                            d_filtered.append(d)

                    # Plot %K line
                    stoch_plot.plot(
                        k_timestamps,
                        k_filtered,
                        pen=pg.mkPen(k_color, width=1),
                        name=f"%K({k_period})"
                    )

                    # Plot %D line
                    stoch_plot.plot(
                        d_timestamps,
                        d_filtered,
                        pen=pg.mkPen(d_color, width=1),
                        name=f"%D({d_period})"
                    )

                    # Add overbought and oversold lines
                    stoch_plot.addLine(y=stoch_overbought, pen=pg.mkPen('r', width=1, style=Qt.DashLine))
                    stoch_plot.addLine(y=stoch_oversold, pen=pg.mkPen('g', width=1, style=Qt.DashLine))

                    # Set Y range to 0-100
                    stoch_plot.setYRange(0, 100)

                    # Add Stochastic plot below the main chart
                    self.chart_layout.addWidget(stoch_plot)

            # Add high/low range as a shaded area
            high_low_fill = pg.FillBetweenItem(
                pg.PlotDataItem(timestamps, highs),
                pg.PlotDataItem(timestamps, lows),
                brush=pg.mkBrush(color + '20')  # Add transparency
            )
            self.chart.addItem(high_low_fill)

            # Add a legend
            self.chart.addLegend()

            # Set axis labels
            self.chart.setLabel('left', 'Price')
            self.chart.setLabel('bottom', 'Time')

            # Format the x-axis to show dates with better formatting
            axis = self.chart.getAxis('bottom')
            axis.setStyle(showValues=True)
            axis.setPen(pg.mkPen('#49a3f1'))

            # Add a time formatter for the x-axis
            def time_formatter(timestamp, _):
                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime('%H:%M')

            axis.setTickFont(QFont('Arial', 8))
            axis.setTickSpacing(major=3600, minor=1800)  # Major tick every hour, minor every 30 min
            axis.tickStrFormat = time_formatter

            # Format the y-axis with better precision and grid
            y_axis = self.chart.getAxis('left')
            y_axis.setPen(pg.mkPen('#49a3f1'))
            y_axis.setTickFont(QFont('Arial', 8))

            # Add grid lines
            self.chart.showGrid(x=True, y=True, alpha=0.3)

            # Add padding around the chart
            self.chart.setContentsMargins(10, 10, 10, 10)
            self.chart.getViewBox().setRange(xRange=(min(timestamps), max(timestamps)),
                                            yRange=(min(lows)*0.998, max(highs)*1.002),
                                            padding=0.05)

            # Restore trend lines if we had any
            if trend_lines_to_restore:
                for item in trend_lines_to_restore:
                    self.chart.addItem(item)
                self.trend_line_items = trend_lines_to_restore

            # Add volume bars at the bottom (scaled down) if volume display is enabled
            if hasattr(self, 'show_volume') and self.show_volume and max(volumes) > 0:  # Only if volume display is enabled and we have volume data
                # Create a secondary y-axis for volume on the right
                volume_axis = pg.ViewBox()
                self.chart.scene().addItem(volume_axis)
                self.chart.getAxis('right').linkToView(volume_axis)
                volume_axis.setXLink(self.chart)

                # Scale the volume to fit in the lower 20% of the chart
                max_price_range = max(highs) - min(lows)
                volume_scale = max_price_range * 0.2 / max(volumes)
                scaled_volumes = [v * volume_scale + min(lows) for v in volumes]

                # Create volume bars
                volume_bars = pg.BarGraphItem(
                    x=timestamps,
                    height=scaled_volumes,
                    width=(timestamps[1] - timestamps[0]) * 0.8 if len(timestamps) > 1 else 60,
                    brush=pg.mkBrush(color + '40')  # Add transparency
                )
                self.chart.addItem(volume_bars)

                # Store reference to the volume bars for later removal
                if not hasattr(self, 'volume_bars'):
                    self.volume_bars = []
                self.volume_bars.append(volume_bars)

            # Draw position lines
            self._draw_position_lines(sym, (timestamps, closes))

            # Draw bid/ask lines
            self._draw_bid_ask_lines(sym, (timestamps, closes))

            # Restore the previous view range if we had one
            if current_range:
                view_box = self.chart.getViewBox()
                if view_box:
                    view_box.setRange(xRange=current_range[0], yRange=current_range[1], padding=0)
                    # print(f"Restored view range: x={current_range[0]}, y={current_range[1]}")

            # Update status bar
            self.statusBar().showMessage(f"Updated OHLCV chart for {sym} with {len(ohlcv_data)} candles ({price_change_pct:.2f}%)")

        except Exception as e:
            print("Error in OHLCV chart update:", e)
            import traceback
            traceback.print_exc()

    def _draw_position_lines(self, symbol, price_data):
        """Draw horizontal lines for open positions on the chart

        Args:
            symbol (str): The current symbol being displayed
            price_data (tuple): Tuple of (timestamps, prices) for positioning labels
        """
        try:
            # Store symbol as instance variable to fix undefined variable errors
            self.current_symbol = symbol

            # Get positions for the current symbol only
            positions = fetch_open_positions(symbol)

            # Count positions for current symbol and other symbols
            current_symbol_positions = [p for p in positions if p.get('symbol') == symbol]
            other_positions = [p for p in positions if p.get('symbol') != symbol]

            if DEBUG:
                print(f"Found {len(current_symbol_positions)} positions for current symbol {symbol} and {len(other_positions)} for other symbols (only showing current symbol positions)")

            # Get the current/latest price for the symbol
            current_price = None
            if price_data and len(price_data[1]) > 0:
                # Use the last price from the price_data
                current_price = price_data[1][-1]
            else:
                # Try to get the current price from order book
                try:
                    bid = fetch_best_bid(symbol)
                    ask = fetch_best_ask(symbol)
                    if bid and ask:
                        current_price = (bid + ask) / 2
                    elif bid:
                        current_price = bid
                    elif ask:
                        current_price = ask
                except Exception as e:
                    if DEBUG:
                        print(f"Error fetching current price: {e}")

            # Only display positions for the current symbol
            for position in current_symbol_positions:
                # Get entry price with safety checks
                entry_price_raw = position.get("entryPrice")
                if entry_price_raw is None:
                    # Try alternative fields
                    entry_price_raw = position.get('price') or position.get('avgPrice') or 0
                    if DEBUG:
                        print(f"Using alternative entry price {entry_price_raw} for {position.get('symbol')}")

                # Convert to float safely
                try:
                    entry_price = float(entry_price_raw)
                    if DEBUG:
                        print(f"Processing position with entry price: {entry_price}")
                except (ValueError, TypeError):
                    if DEBUG:
                        print(f"Invalid entry price: {entry_price_raw}, skipping line")
                    continue

                if entry_price > 0:
                    # Determine line color based on position side
                    side = position.get("side", "")
                    line_color = "#00ff00" if side.lower() == "long" else "#ff0000"  # Green for long, red for short
                    pos_symbol = position.get("symbol", "Unknown")

                    # Create horizontal line for position
                    try:
                        # Create a basic horizontal line
                        line = pg.InfiniteLine(
                            pos=entry_price,
                            angle=0,
                            pen=pg.mkPen(line_color, width=2)  # Make it thicker and solid
                        )
                        self.chart.addItem(line)

                        # Get the left side of the chart for text positioning
                        x_pos = price_data[0][0] if price_data and len(price_data[0]) > 0 else 0

                        # Determine the appropriate decimal precision based on price
                        # For assets like DOGE with small prices, use more decimal places
                        decimal_precision = 6 if entry_price < 1 else 4

                        # Add a simple text label with symbol info
                        label_text = f"{side.upper()} @ {entry_price:.{decimal_precision}f}"
                        text_item = pg.TextItem(
                            html=f'<div style="color:{line_color};background-color:rgba(0,0,0,150);padding:2px">{label_text}</div>',
                            anchor=(0, 0.5)  # Center vertically, left-aligned
                        )

                        # Position at left side of chart
                        text_item.setPos(x_pos, entry_price)
                        self.chart.addItem(text_item)

                        if DEBUG:
                            print(f"Added position line and label for {pos_symbol} {side} at {entry_price}")

                        # Add horizontal dashed line at current price with percentage and price difference
                        if current_price:
                            # Calculate percentage and price difference
                            price_diff = current_price - entry_price
                            pct_diff = (price_diff / entry_price) * 100

                            # Adjust sign based on position side (long/short)
                            if side.lower() == "short":
                                price_diff = -price_diff
                                pct_diff = -pct_diff

                            # Determine color based on whether the position is profitable
                            diff_color = "#00ff44" if pct_diff > 0 else "#ff4444"  # Green for profit, red for loss

                            # Create dashed horizontal line at current price
                            current_line = pg.InfiniteLine(
                                pos=current_price,
                                angle=0,
                                pen=pg.mkPen(diff_color, width=2, style=Qt.DashLine)  # Dashed line
                            )
                            self.chart.addItem(current_line)

                            # Use the same decimal precision for current price
                            diff_label = f"Current: {current_price:.{decimal_precision}f} | Diff: {price_diff:+.{decimal_precision}f} ({pct_diff:+.2f}%)"
                            diff_text_item = pg.TextItem(
                                html=f'<div style="color:{diff_color};background-color:rgba(0,0,0,150);padding:2px">{diff_label}</div>',
                                anchor=(1, 0.5)  # Center vertically, right-aligned
                            )

                            # Position at right side of chart (like open order text)
                            x_ref = price_data[0][-1] if price_data and len(price_data[0]) > 0 else datetime.now().timestamp()
                            diff_text_item.setPos(x_ref, current_price)
                            self.chart.addItem(diff_text_item)

                            if DEBUG:
                                print(f"Added current price line with diff: {diff_label}")
                    except Exception as e:
                        if DEBUG:
                            print(f"Error adding position line: {e}")
        except Exception as e:
            if DEBUG:
                print(f"Error drawing position lines: {e}")
                import traceback
                traceback.print_exc()

    def _draw_bid_ask_lines(self, symbol, price_data):
        """Draw horizontal lines with small dots for best bid and best ask prices

        Args:
            symbol (str): The current symbol being displayed
            price_data (tuple): Tuple of (timestamps, prices) for positioning labels
        """
        try:
            # Remove any existing bid/ask lines (for memory management)
            if hasattr(self, '_bid_ask_lines'):
                for item in self._bid_ask_lines:
                    self.chart.removeItem(item)

            # Initialize list to track bid/ask lines and dots
            self._bid_ask_lines = []

            # Fetch best bid and ask prices
            best_bid = fetch_best_bid(symbol)
            best_ask = fetch_best_ask(symbol)

            if DEBUG:
                print(f"Best bid: {best_bid}, Best ask: {best_ask}")

            # Get the x-range for positioning dots
            x_range = None
            if price_data and len(price_data[0]) > 0:
                x_min = price_data[0][0]
                x_max = price_data[0][-1]
                x_range = (x_min, x_max)
            else:
                # Fallback if no price data
                current_time = datetime.now().timestamp()
                x_range = (current_time - 3600, current_time)

            # Create dots for best bid (gold color)
            if best_bid:
                # Create horizontal line for best bid
                bid_line = pg.InfiniteLine(
                    pos=best_bid,
                    angle=0,
                    pen=pg.mkPen('#FFD700', width=1, style=Qt.DotLine)  # Gold color, dotted line
                )
                self.chart.addItem(bid_line)
                self._bid_ask_lines.append(bid_line)

                # Create small dots along the bid line
                num_dots = 20  # Number of dots to display
                x_step = (x_range[1] - x_range[0]) / (num_dots - 1)
                bid_x = [x_range[0] + i * x_step for i in range(num_dots)]
                bid_y = [best_bid] * num_dots

                # Plot bid dots
                bid_dots = self.chart.plot(
                    bid_x, bid_y,
                    pen=None,
                    symbol='o',
                    symbolSize=3,  # Small dots
                    symbolBrush=pg.mkBrush('#FFD700'),  # Gold color
                    antialias=True
                )
                self._bid_ask_lines.append(bid_dots)

            # Create dots for best ask (silver color)
            if best_ask:
                # Create horizontal line for best ask
                ask_line = pg.InfiniteLine(
                    pos=best_ask,
                    angle=0,
                    pen=pg.mkPen('#C0C0C0', width=1, style=Qt.DotLine)  # Silver color, dotted line
                )
                self.chart.addItem(ask_line)
                self._bid_ask_lines.append(ask_line)

                # Create small dots along the ask line
                num_dots = 20  # Number of dots to display
                x_step = (x_range[1] - x_range[0]) / (num_dots - 1)
                ask_x = [x_range[0] + i * x_step for i in range(num_dots)]
                ask_y = [best_ask] * num_dots

                # Plot ask dots
                ask_dots = self.chart.plot(
                    ask_x, ask_y,
                    pen=None,
                    symbol='o',
                    symbolSize=3,  # Small dots
                    symbolBrush=pg.mkBrush('#C0C0C0'),  # Silver color
                    antialias=True
                )
                self._bid_ask_lines.append(ask_dots)

        except Exception as e:
            if DEBUG:
                print(f"Error drawing bid/ask lines: {e}")
                import traceback
                traceback.print_exc()

    def _populate_tick_chart(self, trades_data):
        """Populate the chart with tick data"""
        # Save current view range to restore after update
        view_box = self.chart.getViewBox()
        current_range = view_box.viewRange() if view_box else None

        # Preserve trend lines when updating the chart
        trend_lines_to_restore = []
        if hasattr(self, 'trend_line_items') and self.trend_line_items:
            trend_lines_to_restore = self.trend_line_items.copy()
            self.trend_line_items = []
        try:
            sym = self.get_symbol()
            # Store as instance variables to fix undefined variable errors
            self.current_symbol = sym
            # Store chart type and timeframe as instance variables (used by other methods)
            self.current_chart_type
            self.current_timeframe

            if not trades_data or len(trades_data) == 0:
                return

            # Store the trades data for other components to use
            self.latest_trades_data = trades_data

            # Update the trades widget with the latest trades data
            if hasattr(self, 'trades_widget'):
                self.trades_widget._populate_trades(trades_data)

            # Convert timestamps → seconds
            ts = [t['timestamp']/1000 for t in trades_data]
            pr = [t['price'] for t in trades_data]

            # Separate buys/sells
            buy_ts = [t['timestamp']/1000 for t in trades_data if t['side']=='buy']
            buy_pr = [t['price'] for t in trades_data if t['side']=='buy']
            sell_ts = [t['timestamp']/1000 for t in trades_data if t['side']=='sell']
            sell_pr = [t['price'] for t in trades_data if t['side']=='sell']

            # Clear previous items
            self.chart.clear()

            # We're not plotting the connecting line anymore, only the dots

            # Plot buy markers (green dots)
            self.chart.plot(
                buy_ts, buy_pr,
                pen=None,
                symbol='o',
                symbolSize=8,  # Slightly larger dots since they're the main focus now
                symbolBrush=pg.mkBrush('#00ff44'),
                antialias=True
            )

            # Plot sell markers (red dots)
            self.chart.plot(
                sell_ts, sell_pr,
                pen=None,
                symbol='o',
                symbolSize=8,  # Slightly larger dots since they're the main focus now
                symbolBrush=pg.mkBrush('#ff4444'),
                antialias=True
            )

            # Add ATR-EMA Bands if enabled
            if hasattr(self, 'indicator_settings') and self.indicator_settings.get("ATR-EMA Bands", {}).get("enabled", False):
                # We need to convert the tick data to OHLCV format for the indicators
                # First, create a DataFrame from the tick data
                if len(ts) > 0:
                    # Create a DataFrame with the tick data
                    tick_df = pd.DataFrame({
                        'timestamp': ts,
                        'price': pr
                    })

                    # Resample to 1-minute OHLCV data
                    tick_df['timestamp'] = pd.to_datetime(tick_df['timestamp'], unit='s')
                    tick_df.set_index('timestamp', inplace=True)

                    # Resample to 1-minute bars
                    ohlc = tick_df['price'].resample('1min').ohlc()
                    volume = tick_df['price'].resample('1min').count()

                    # Create OHLCV DataFrame
                    df = pd.DataFrame({
                        'open': ohlc['open'],
                        'high': ohlc['high'],
                        'low': ohlc['low'],
                        'close': ohlc['close'],
                        'volume': volume
                    })

                    # Drop NaN values
                    df.dropna(inplace=True)

                    if len(df) > 0:
                        # Get ATR-EMA Bands settings
                        ema_period = self.indicator_settings["ATR-EMA Bands"]["ema_period"]
                        atr_period = self.indicator_settings["ATR-EMA Bands"]["atr_period"]
                        multipliers = self.indicator_settings["ATR-EMA Bands"]["multipliers"]
                        ema_color = self.indicator_settings["ATR-EMA Bands"]["ema_color"]
                        upper_color = self.indicator_settings["ATR-EMA Bands"]["upper_band_color"]
                        lower_color = self.indicator_settings["ATR-EMA Bands"]["lower_band_color"]

                        # Calculate ATR-EMA Bands
                        if len(df) >= max(ema_period, atr_period):
                            bands = calculate_atr_ema_bands(df, ema_period, atr_period, multipliers)

                            # Convert index to timestamps for plotting
                            band_timestamps = [t.timestamp() for t in bands['ema'].index]

                            # Plot EMA line
                            self.chart.plot(
                                band_timestamps,
                                bands['ema'].values,
                                pen=pg.mkPen(ema_color, width=1),
                                name=f"EMA({ema_period})"
                            )

                            # Plot upper bands
                            for mult in multipliers:
                                band_key = f'ema_plus_{mult}_atr'
                                if band_key in bands:
                                    self.chart.plot(
                                        band_timestamps,
                                        bands[band_key].values,
                                        pen=pg.mkPen(upper_color, width=1, style=Qt.DashLine),
                                        name=f"EMA+{mult}ATR"
                                    )

                            # Plot lower bands
                            for mult in multipliers:
                                band_key = f'ema_minus_{mult}_atr'
                                if band_key in bands:
                                    self.chart.plot(
                                        band_timestamps,
                                        bands[band_key].values,
                                        pen=pg.mkPen(lower_color, width=1, style=Qt.DashLine),
                                        name=f"EMA-{mult}ATR"
                                    )

            # Draw position lines if we have positions for this symbol
            self._draw_position_lines(sym, (ts, pr))

            # Draw order levels
            self._draw_order_levels(sym, (ts, pr))

            # Draw best bid and best ask lines with dots
            self._draw_bid_ask_lines(sym, (ts, pr))

            # Restore trend lines if we had any
            if trend_lines_to_restore:
                for item in trend_lines_to_restore:
                    self.chart.addItem(item)
                self.trend_line_items = trend_lines_to_restore

            # Restore the previous view range if we had one
            if current_range:
                view_box = self.chart.getViewBox()
                if view_box:
                    view_box.setRange(xRange=current_range[0], yRange=current_range[1], padding=0)
                    # print(f"Restored tick chart view range: x={current_range[0]}, y={current_range[1]}")

        except Exception as e:
            print(f"Error populating tick chart: {e}")
            import traceback
            traceback.print_exc()

    def _draw_order_levels(self, symbol, price_data):
        """Draw horizontal lines for open orders on the chart

        Args:
            symbol (str): The current symbol being displayed
            price_data (tuple): Tuple of (timestamps, prices) for positioning labels
        """
        try:
            # Store symbol as instance variable to fix undefined variable errors
            sym = symbol
            self.current_symbol = symbol
            chart_type = self.current_chart_type
            tf = self.current_timeframe

            # Get orders for the current symbol
            orders = fetch_open_orders(symbol)

            if DEBUG:
                print(f"Found {len(orders)} orders for symbol {symbol}")

            for order in orders:
                # Get price with safety checks
                try:
                    price = float(order.get('price', 0))
                    size = float(order.get('amount', 0))
                    side = order.get('side', '').lower()

                    if DEBUG:
                        print(f"Order details: price={price}, size={size}, side={side}")

                    if price <= 0 or size <= 0:
                        if DEBUG:
                            print(f"Skipping order with invalid price/size: {price}/{size}")
                        continue

                    # Bright green for buy, bright red for sell; thicker dashed lines
                    color = '#00ff00' if side == 'buy' else '#ff0000'
                    pen = pg.mkPen(color, width=2, style=Qt.DashLine)

                    # Create horizontal line for order
                    line = pg.InfiniteLine(pos=price, angle=0, pen=pen)
                    self.chart.addItem(line)

                    # Label "qty @ price" with background for better visibility
                    label = f"{size:.4f} @ {price:.4f}"
                    html_label = f'<div style="color:{color};background-color:rgba(0,0,0,150);padding:2px">{label}</div>'
                    txt = pg.TextItem(html=html_label, anchor=(1, 0.5))  # Right-aligned

                    # Position at right side of chart
                    # Use the last timestamp from price_data if available
                    x_ref = price_data[0][-1] if price_data and len(price_data[0]) > 0 else datetime.now().timestamp()
                    txt.setPos(x_ref, price)
                    self.chart.addItem(txt)

                    if DEBUG:
                        print(f"Added order line: {side} {size} @ {price}")
                except (ValueError, TypeError) as e:
                    if DEBUG:
                        print(f"Error processing order: {e}")
                    continue
        except Exception as e:
            if DEBUG:
                print(f"Error drawing order levels: {e}")
                import traceback
                traceback.print_exc()

            # TICK CHART MODE
            if chart_type == "Tick":
                print("Drawing tick chart...")
                if sym:
                    # Fetch trades for tick chart
                    trades = fetch_trades(sym, limit=500)

                    if trades and len(trades) > 0:
                        # Extract timestamps and prices
                        timestamps = []
                        prices = []
                        colors = []

                        for trade in trades:
                            # Convert timestamps from ms to seconds for DateAxisItem
                            timestamps.append(trade['timestamp'] / 1000.0)
                            prices.append(trade['price'])
                            # Green for buy, red for sell
                            colors.append('#00ff00' if trade['side'] == 'buy' else '#ff0000')

                        # Plot all trades as dots in one go for better performance
                        buy_timestamps = []
                        buy_prices = []
                        sell_timestamps = []
                        sell_prices = []

                        for i, trade in enumerate(trades):
                            if trade['side'] == 'buy':
                                buy_timestamps.append(timestamps[i])
                                buy_prices.append(prices[i])
                            else:
                                sell_timestamps.append(timestamps[i])
                                sell_prices.append(prices[i])

                        # We're not plotting the connecting line anymore, only the dots

                        # Plot buy markers (green dots)
                        if buy_timestamps:
                            self.chart.plot(
                                buy_timestamps,
                                buy_prices,
                                pen=None,
                                symbol='o',
                                symbolPen=None,
                                symbolBrush='#00ff00',
                                symbolSize=8  # Slightly larger dots since they're the main focus now
                            )

                        # Plot sells (red dots)
                        if sell_timestamps:
                            self.chart.plot(
                                sell_timestamps,
                                sell_prices,
                                pen=None,
                                symbol='o',
                                symbolPen=None,
                                symbolBrush='#ff0000',
                                symbolSize=8  # Slightly larger dots since they're the main focus now
                            )

                        # Set data for position lines
                        price_data = (timestamps, prices)
                        min_y = min(prices) * 0.995  # Add 0.5% margin
                        max_y = max(prices) * 1.005  # Add 0.5% margin

                        print(f"Plotted {len(timestamps)} trades")
                    else:
                        print("No trades data available")

            # CANDLESTICK MODE
            elif chart_type == "Candlestick":
                print("Drawing candlestick chart...")

                # Store the current chart type and symbol for next update
                self.current_chart_type = "Candlestick"
                self.current_symbol = sym

                if sym:
                    # Fetch OHLCV data for candlestick chart
                    if not demo_mode:
                        # Fetch real OHLCV data
                        ohlcv = exchange.fetch_ohlcv(sym, tf, limit=100)
                        if ohlcv and len(ohlcv) > 0:
                            # Convert to numpy array and prepare for CandlestickItem
                            arr = np.array(ohlcv)
                            # Convert timestamps from ms to seconds for DateAxisItem
                            timestamps = arr[:, 0] / 1000.0
                            # Create array in format expected by CandlestickItem: [time, open, high, low, close]
                            candles = np.column_stack([timestamps, arr[:, 1], arr[:, 2], arr[:, 3], arr[:, 4]])

                            # Add volume data if volume display is enabled
                            if hasattr(self, 'show_volume') and self.show_volume and len(arr) > 0 and arr.shape[1] > 5:
                                # Extract volume data (column 5 in OHLCV data)
                                volumes = arr[:, 5]

                                # Create a separate plot for volume at the bottom of the chart
                                # We'll use 20% of the chart height for volume
                                volume_height_ratio = 0.2

                                # Initialize volume bars list if it doesn't exist
                                if not hasattr(self, 'volume_bars'):
                                    self.volume_bars = []
                                else:
                                    # Clear existing volume bars
                                    for bar in self.volume_bars:
                                        self.chart.removeItem(bar)
                                    self.volume_bars = []

                                # Create volume bars with colors based on price movement
                                for i in range(len(timestamps)):
                                    # Determine if price went up or down
                                    is_up = arr[i, 4] >= arr[i, 1]  # close >= open
                                    color = '#00ff00' if is_up else '#ff0000'  # Green for up, red for down

                                    # Calculate bar height based on volume
                                    vol_height = volumes[i] / max(volumes) * (max_y - min_y) * volume_height_ratio

                                    # Create a bar for this volume
                                    bar = pg.BarGraphItem(
                                        x=[timestamps[i]],
                                        height=[vol_height],
                                        width=0.7 * (timestamps[1] - timestamps[0]) if i < len(timestamps)-1 else 0.7,
                                        brush=color,
                                        pen=None
                                    )

                                    # Position the bar at the bottom of the chart
                                    bar.setPos(0, min_y - vol_height)
                                    self.chart.addItem(bar)

                                    # Store reference to the bar for later removal
                                    self.volume_bars.append(bar)

                            # Use the built-in CandlestickItem with anti-flicker approach
                            from pyqtgraph import CandlestickItem

                            # Store the candlestick item as an instance variable to prevent flickering
                            if not hasattr(self, 'candlestick_item') or self.candlestick_item is None:
                                self.candlestick_item = CandlestickItem(candles)
                                self.chart.addItem(self.candlestick_item)
                            else:
                                # Update the existing candlestick item's data instead of creating a new one
                                self.candlestick_item.setData(candles)

                            # Extract data for position lines
                            price_data = (timestamps, arr[:, 4])  # time and close price
                            min_y = min(arr[:, 3]) * 0.995  # Use lowest low for min
                            max_y = max(arr[:, 2]) * 1.005  # Use highest high for max
                    else:
                        # Use our demo data function
                        df = fetch_ohlcv(sym, tf, limit=100)
                        if df is not None and not df.empty:
                            # For demo mode, convert DataFrame to format needed by CandlestickItem
                            timestamps = df.index.astype(np.int64) // 10**6 / 1000  # Convert to seconds
                            candles = np.column_stack([
                                timestamps,
                                df['open'].values,
                                df['high'].values,
                                df['low'].values,
                                df['close'].values
                            ])

                            # Add volume data if volume display is enabled and volume data exists
                            if hasattr(self, 'show_volume') and self.show_volume and 'volume' in df.columns:
                                # Extract volume data
                                volumes = df['volume'].values

                                # Create a separate plot for volume at the bottom of the chart
                                # We'll use 20% of the chart height for volume
                                volume_height_ratio = 0.2

                                # Initialize volume bars list if it doesn't exist
                                if not hasattr(self, 'volume_bars'):
                                    self.volume_bars = []
                                else:
                                    # Clear existing volume bars
                                    for bar in self.volume_bars:
                                        self.chart.removeItem(bar)
                                    self.volume_bars = []

                                # Create volume bars with colors based on price movement
                                for i in range(len(timestamps)):
                                    # Determine if price went up or down
                                    is_up = df['close'].values[i] >= df['open'].values[i]
                                    color = '#00ff00' if is_up else '#ff0000'  # Green for up, red for down

                                    # Calculate bar height based on volume
                                    vol_height = volumes[i] / max(volumes) * (max_y - min_y) * volume_height_ratio

                                    # Create a bar for this volume
                                    bar = pg.BarGraphItem(
                                        x=[timestamps[i]],
                                        height=[vol_height],
                                        width=0.7 * (timestamps[1] - timestamps[0]) if i < len(timestamps)-1 else 0.7,
                                        brush=color,
                                        pen=None
                                    )

                                    # Position the bar at the bottom of the chart
                                    bar.setPos(0, min_y - vol_height)
                                    self.chart.addItem(bar)

                                    # Store reference to the bar for later removal
                                    self.volume_bars.append(bar)

                            # Use the built-in CandlestickItem with anti-flicker approach
                            from pyqtgraph import CandlestickItem

                            # Store the candlestick item as an instance variable to prevent flickering
                            if not hasattr(self, 'candlestick_item') or self.candlestick_item is None:
                                self.candlestick_item = CandlestickItem(candles)
                                self.chart.addItem(self.candlestick_item)
                            else:
                                # Update the existing candlestick item's data instead of creating a new one
                                self.candlestick_item.setData(candles)

                            # Extract data for position lines
                            price_data = (timestamps, df['close'].values)
                            min_y = min(df['low'].values) * 0.995
                            max_y = max(df['high'].values) * 1.005

            # LINE CHART MODE (uses same data as candlestick but displays as a line)
            elif chart_type == "Line Chart":
                print("Drawing line chart...")

                # Store the current chart type and symbol for next update
                self.current_chart_type = "Line Chart"
                self.current_symbol = sym

                if sym:
                    # Fetch OHLCV data for line chart (same as candlestick)
                    if not demo_mode:
                        # Fetch real OHLCV data
                        ohlcv = exchange.fetch_ohlcv(sym, tf, limit=100)
                        if ohlcv and len(ohlcv) > 0:
                            # Convert to numpy array
                            arr = np.array(ohlcv)
                            # Convert timestamps from ms to seconds for DateAxisItem
                            timestamps = arr[:, 0] / 1000.0
                            # Extract close prices for the line
                            closes = arr[:, 4]

                            # Plot the line with a thicker, smoother line
                            self.chart.plot(
                                timestamps,
                                closes,
                                pen=pg.mkPen('#00ff44', width=2),
                                name="Price",
                                antialias=True
                            )

                            # Extract data for position lines
                            price_data = (timestamps, closes)
                            min_y = min(arr[:, 3]) * 0.995  # Use lowest low for min
                            max_y = max(arr[:, 2]) * 1.005  # Use highest high for max

                            # Add volume data if volume display is enabled
                            if hasattr(self, 'show_volume') and self.show_volume and len(arr) > 0 and arr.shape[1] > 5:
                                # Extract volume data (column 5 in OHLCV data)
                                volumes = arr[:, 5]

                                # Create a separate plot for volume at the bottom of the chart
                                # We'll use 20% of the chart height for volume
                                volume_height_ratio = 0.2

                                # Initialize volume bars list if it doesn't exist
                                if not hasattr(self, 'volume_bars'):
                                    self.volume_bars = []
                                else:
                                    # Clear existing volume bars
                                    for bar in self.volume_bars:
                                        self.chart.removeItem(bar)
                                    self.volume_bars = []

                                # Create volume bars with colors based on price movement
                                for i in range(len(timestamps)):
                                    # Determine if price went up or down (for line chart, compare to previous close)
                                    is_up = arr[i, 4] >= (arr[i-1, 4] if i > 0 else arr[i, 4])
                                    color = '#00ff00' if is_up else '#ff0000'  # Green for up, red for down

                                    # Calculate bar height based on volume
                                    vol_height = volumes[i] / max(volumes) * (max_y - min_y) * volume_height_ratio

                                    # Create a bar for this volume
                                    bar = pg.BarGraphItem(
                                        x=[timestamps[i]],
                                        height=[vol_height],
                                        width=0.7 * (timestamps[1] - timestamps[0]) if i < len(timestamps)-1 else 0.7,
                                        brush=color,
                                        pen=None
                                    )

                                    # Position the bar at the bottom of the chart
                                    bar.setPos(0, min_y - vol_height)
                                    self.chart.addItem(bar)

                                    # Store reference to the bar for later removal
                                    self.volume_bars.append(bar)
                    else:
                        # Use our demo data function
                        df = fetch_ohlcv(sym, tf, limit=100)
                        if df is not None and not df.empty:
                            # For demo mode, convert DataFrame
                            timestamps = df.index.astype(np.int64) // 10**6 / 1000  # Convert to seconds
                            closes = df['close'].values

                            # Plot the line with a thicker, smoother line
                            self.chart.plot(
                                timestamps,
                                closes,
                                pen=pg.mkPen('#00ff44', width=2),
                                name="Price",
                                antialias=True
                            )

                            # Extract data for position lines
                            price_data = (timestamps, closes)
                            min_y = min(df['low'].values) * 0.995
                            max_y = max(df['high'].values) * 1.005

                            # Add volume data if volume display is enabled and volume data exists
                            if hasattr(self, 'show_volume') and self.show_volume and 'volume' in df.columns:
                                # Extract volume data
                                volumes = df['volume'].values

                                # Create a separate plot for volume at the bottom of the chart
                                # We'll use 20% of the chart height for volume
                                volume_height_ratio = 0.2

                                # Initialize volume bars list if it doesn't exist
                                if not hasattr(self, 'volume_bars'):
                                    self.volume_bars = []
                                else:
                                    # Clear existing volume bars
                                    for bar in self.volume_bars:
                                        self.chart.removeItem(bar)
                                    self.volume_bars = []

                                # Create volume bars with colors based on price movement
                                for i in range(len(timestamps)):
                                    # Determine if price went up or down (for line chart, compare to previous close)
                                    is_up = df['close'].values[i] >= (df['close'].values[i-1] if i > 0 else df['close'].values[i])
                                    color = '#00ff00' if is_up else '#ff0000'  # Green for up, red for down

                                    # Calculate bar height based on volume
                                    vol_height = volumes[i] / max(volumes) * (max_y - min_y) * volume_height_ratio

                                    # Create a bar for this volume
                                    bar = pg.BarGraphItem(
                                        x=[timestamps[i]],
                                        height=[vol_height],
                                        width=0.7 * (timestamps[1] - timestamps[0]) if i < len(timestamps)-1 else 0.7,
                                        brush=color,
                                        pen=None
                                    )

                                    # Position the bar at the bottom of the chart
                                    bar.setPos(0, min_y - vol_height)
                                    self.chart.addItem(bar)

                                    # Store reference to the bar for later removal
                                    self.volume_bars.append(bar)

            # Fallback to random data if no real data available
            if price_data is None:
                print("Using random data for chart")
                if not hasattr(self, 'x') or not hasattr(self, 'y'):
                    self.x = list(range(100))
                    self.y = [random.gauss(0, 1) for _ in range(100)]
                else:
                    # Only update a few points to reduce CPU usage
                    self.y = self.y[1:] + [random.gauss(0, 1)]

                price_data = (self.x, self.y)
                self.chart.plot(self.x, self.y, pen=pg.mkPen('#00ff44', width=1.5))
                min_y = min(self.y) * 0.9
                max_y = max(self.y) * 1.1

            # If we're not in fallback mode and not in Tick or Candlestick mode, plot the data as a line
            elif chart_type != "Tick" and chart_type != "Candlestick" and chart_type != "Line Chart":
                # Only draw this line if we're not in a special chart mode that already has its own rendering
                self.chart.plot(price_data[0], price_data[1], pen=pg.mkPen('#00ff44', width=1.5))

            # Get ALL open positions first, then filter for display
            positions = []
            print("CHART: Getting ALL open positions first")

            # ALWAYS try to get real positions first - but get ALL positions, not just for current symbol
            print("CHART: Attempting to fetch ALL real positions from exchange")
            all_real_positions = fetch_open_positions()
            print(f"CHART: fetch_open_positions() → {len(all_real_positions)} positions")

            # Check if we got any real positions
            if all_real_positions and len(all_real_positions) > 0:
                # Filter for positions with non-zero contracts
                non_zero_positions = []
                for pos in all_real_positions:
                    try:
                        contracts = float(pos.get('contracts', 0))
                        if abs(contracts) > 0:
                            non_zero_positions.append(pos)
                            print(f"CHART: Found real position: {pos.get('symbol')}, {pos.get('side')}, {contracts}, entry: {pos.get('entryPrice')}")
                    except Exception as e:
                        print(f"CHART: Error processing position: {e}")

                if non_zero_positions:
                    print(f"CHART: Using {len(non_zero_positions)} real positions for chart")
                    positions = non_zero_positions
                else:
                    print("CHART: No non-zero positions found")
            else:
                print("CHART: No real positions found from exchange")

            # If no real positions, fall back to demo positions
            if not positions:
                chart_debug_print("Using demo positions for chart")
                demo_positions = [
                    {"symbol": "BTC/USDT", "side": "long", "contracts": 0.01, "entryPrice": 50000},
                    {"symbol": "ETH/USDT", "side": "short", "contracts": 0.1, "entryPrice": 3000}
                ]

                # Always include a position for the current symbol
                if sym:
                    current_symbol_position = {
                        "symbol": sym,
                        "side": "long",
                        "contracts": 0.01,
                        "entryPrice": 50000 if "BTC" in sym else 3000,
                        "markPrice": 51000 if "BTC" in sym else 3100,
                        "unrealizedPnl": 10
                    }
                    demo_positions.append(current_symbol_position)

                positions = demo_positions
                chart_debug_print(f"Using {len(positions)} demo positions for chart")

                # Print details of positions for debugging
                for p in positions:
                    chart_debug_print(f"Position for chart: {p.get('symbol', '')}, {p.get('side', '')}, {p.get('contracts', 0)}, entry: {p.get('entryPrice', 0)}")

            # Add horizontal lines for open positions
            chart_debug_print(f"Adding horizontal lines for {len(positions)} positions")

            # First, check if we have a real position for the current symbol
            current_symbol_positions = []
            other_positions = []

            # Separate positions into current symbol and other symbols
            for position in positions:
                pos_symbol = position.get('symbol', '')
                # Check if this position matches the current symbol
                if sym and (pos_symbol == sym or
                           ((':' in pos_symbol and ':' in sym) and pos_symbol.split(':')[0] == sym.split(':')[0]) or
                           ((':' not in pos_symbol and ':' in sym) and pos_symbol == sym.split(':')[0]) or
                           ((':' in pos_symbol and ':' not in sym) and pos_symbol.split(':')[0] == sym)):
                    current_symbol_positions.append(position)
                else:
                    other_positions.append(position)

            print(f"CHART POSITIONS: Found {len(current_symbol_positions)} positions for current symbol {sym} and {len(other_positions)} for other symbols (only showing current symbol positions)")

            # Only show positions for the current symbol
            # Never show positions for other symbols
            positions_to_display = current_symbol_positions

            # Now draw the position lines
            for position in positions_to_display:
                # Get entry price with safety checks
                entry_price_raw = position.get("entryPrice")
                if entry_price_raw is None:
                    # Try alternative fields
                    entry_price_raw = position.get('price') or position.get('avgPrice') or 0
                    chart_debug_print(f"Using alternative entry price {entry_price_raw} for {position.get('symbol')}")

                # Convert to float safely
                try:
                    entry_price = float(entry_price_raw)
                    chart_debug_print(f"Processing position with entry price: {entry_price}")
                except (ValueError, TypeError):
                    chart_debug_print(f"Invalid entry price: {entry_price_raw}, skipping line")
                    continue

                if entry_price > 0:
                    # Determine line color based on position side
                    side = position.get("side", "")
                    line_color = "#00ff00" if side.lower() == "long" else "#ff0000"  # Green for long, red for short
                    pos_symbol = position.get("symbol", "Unknown")
                    chart_debug_print(f"Creating {line_color} line for {side} position at {entry_price} for {pos_symbol}")

                    # Create horizontal line for position - SIMPLIFIED VERSION
                    try:
                        # Create a basic horizontal line
                        line = pg.InfiniteLine(
                            pos=entry_price,
                            angle=0,
                            pen=pg.mkPen(line_color, width=3)  # Make it thicker and solid
                        )
                        self.chart.addItem(line)
                        chart_debug_print(f"Successfully added horizontal line at {entry_price}")

                        # Add a simple text label with symbol info
                        label_text = f"{pos_symbol} {side.upper()} @ {entry_price:.4f}"
                        text_item = pg.TextItem(
                            html=f'<div style="color:{line_color};background-color:rgba(0,0,0,150);padding:2px">{label_text}</div>',
                            anchor=(0, 0.5)  # Center vertically
                        )
                        # Position at left side of chart
                        # Use the first timestamp from price_data if available
                        x_pos = price_data[0][0] if price_data and len(price_data[0]) > 0 else 0
                        text_item.setPos(x_pos, entry_price)
                        self.chart.addItem(text_item)
                        chart_debug_print(f"Added text label: {label_text}")
                    except Exception as e:
                        print(f"Error adding position line: {e}")
                        chart_debug_print(f"Exception details: {str(e)}")

            # ─── Draw open orders on chart ──────────────────────────────
            try:
                print("Drawing orders on chart...")
                current_symbol = self.get_symbol()
                print(f"Current symbol: {current_symbol}")
                orders = fetch_open_orders(current_symbol)
                print(f"Got {len(orders)} orders to draw")

                for o in orders:
                    print(f"Processing order: {o}")
                    price = float(o.get('price', 0))
                    size = float(o.get('amount', 0))
                    side = o.get('side', '').lower()
                    print(f"Order details: price={price}, size={size}, side={side}")

                    if price <= 0 or size <= 0:
                        print(f"Skipping order with invalid price/size: {price}/{size}")
                        continue

                    # Bright green for buy, bright red for sell; thicker dashed lines
                    color = '#00ff00' if side == 'buy' else '#ff0000'
                    pen = pg.mkPen(color, width=2, style=Qt.DashLine)

                    line = pg.InfiniteLine(pos=price, angle=0, pen=pen)
                    self.chart.addItem(line)
                    print(f"Added line at price {price}")

                    # label "qty @ price" with background for better visibility
                    label = f"{size:.4f} @ {price:.4f}"
                    html_label = f'<div style="color:{color};background-color:rgba(0,0,0,150);padding:2px">{label}</div>'
                    txt = pg.TextItem(html=html_label, anchor=(1, 0.5))
                    # stick it at the rightmost x
                    x_ref = price_data[0][-1] if price_data and len(price_data[0]) > 0 else datetime.now().timestamp()
                    txt.setPos(x_ref, price)
                    self.chart.addItem(txt)
                    print(f"Added order line: {side} {size} @ {price}")
            except Exception as e:
                print(f"Error drawing order-levels: {e}")
                import traceback
                traceback.print_exc()

            # Set Y range to ensure position and order lines are visible
            if min_y is not None and max_y is not None:
                # Adjust range to include position lines
                for position in positions:
                    entry_price = float(position.get("entryPrice", 0))
                    if entry_price > 0:
                        min_y = min(min_y, entry_price * 0.995)
                        max_y = max(max_y, entry_price * 1.005)

                # Also adjust range to include order lines
                orders = fetch_open_orders(self.get_symbol())
                for order in orders:
                    order_price = float(order.get("price", 0))
                    if order_price > 0:
                        min_y = min(min_y, order_price * 0.995)
                        max_y = max(max_y, order_price * 1.005)
                        print(f"Adjusting Y range to include order at {order_price}")

                # Restore the previous view range if we had one and we're not in tick chart mode
                # (tick chart already handles this in its own method)
                view_box = self.chart.getViewBox()
                saved_range = view_box.viewRange() if view_box else None

                if saved_range and self.current_chart_type != "Tick":
                    if view_box:
                        # Only restore the view range if the user has zoomed in
                        # We can detect this by checking if the current range is smaller than the default range
                        current_x_range = view_box.viewRange()[0]
                        current_y_range = view_box.viewRange()[1]

                        # Check if the user has zoomed in (range is smaller than default)
                        x_zoomed = (current_x_range[1] - current_x_range[0]) < (price_data[0][-1] - price_data[0][0])
                        y_zoomed = (current_y_range[1] - current_y_range[0]) < (max_y - min_y)

                        if x_zoomed or y_zoomed:
                            view_box.setRange(xRange=saved_range[0], yRange=saved_range[1], padding=0)
                            # print(f"Restored chart view range: x={saved_range[0]}, y={saved_range[1]}")
                            # Skip the setYRange call below
                            return

                # Only set the Y range if we didn't restore a custom view range
                self.chart.setYRange(min_y, max_y)

        except Exception as e:
            # Don't print the same error repeatedly
            error_msg = str(e)
            if not hasattr(self._update_chart, 'last_error') or self._update_chart.last_error != error_msg:
                print(f"Error updating chart: {e}")
                self._update_chart.last_error = error_msg

            # Fallback to random data
            if not hasattr(self, 'x') or not hasattr(self, 'y'):
                self.x = list(range(100))
                self.y = [random.gauss(0, 1) for _ in range(100)]
            else:
                self.y = self.y[1:] + [random.gauss(0, 1)]

            self.chart.clear()
            self.chart.plot(self.x, self.y, pen=pg.mkPen('#00ff44'))

    def _update_account(self):
        try:
            # Use a class variable to track last update time to prevent excessive updates
            current_time = datetime.now()
            if hasattr(self, 'last_account_update') and (current_time - self.last_account_update).total_seconds() < 5.0:
                return  # Skip update if less than 5 seconds have passed

            self.last_account_update = current_time

            if demo_mode:
                # Demo mode - show simulated data in the account status label
                if hasattr(self, 'account_status_label'):
                    current_text = self.account_status_label.text()
                    account_name = current_text.split("LIVE:")[-1].strip() if "LIVE:" in current_text else ""
                    self.account_status_label.setText(f"Equity: $10,000.00 Free: $10,000.00 Status: Demo Mode | LIVE: {account_name}")
                    self.account_status_label.setStyleSheet("color: #ffaa00; padding: 0 10px;")
            else:
                # Real mode - fetch actual data
                bal = exchange.fetch_balance()
                eq = bal['total'].get('USDT', 0)
                fr = bal['free'].get('USDT', 0)

                # Update the account status label
                if hasattr(self, 'account_status_label'):
                    current_text = self.account_status_label.text()
                    account_name = current_text.split("LIVE:")[-1].strip() if "LIVE:" in current_text else ""
                    self.account_status_label.setText(f"Equity: ${eq:.2f} Free: ${fr:.2f} Status: Connected | LIVE: {account_name}")
                    self.account_status_label.setStyleSheet("color: #00ff44; padding: 0 10px;")

            # Positions and orders are now updated by their own timers
            # No need to call them here
        except Exception as e:
            # Don't print the same error repeatedly
            error_msg = str(e)
            if not hasattr(self._update_account, 'last_error') or self._update_account.last_error != error_msg:
                print(f"Error updating account: {e}")
                self._update_account.last_error = error_msg

            # Update the account status label to show error
            if hasattr(self, 'account_status_label'):
                current_text = self.account_status_label.text()
                account_name = current_text.split("LIVE:")[-1].strip() if "LIVE:" in current_text else ""
                self.account_status_label.setText(f"Status: Error | LIVE: {account_name}")
                self.account_status_label.setStyleSheet("color: #ff4444; padding: 0 10px;")

    def _update_positions(self):
        """Update the positions table with real data or demo data using a worker thread"""
        try:
            # Implement proper debouncing for positions updates
            current_time = datetime.now()

            # Skip if we've updated recently
            if hasattr(self, 'last_positions_update') and (current_time - self.last_positions_update).total_seconds() < 2.0:
                return  # Skip update if less than 2 seconds have passed

            # Skip if an update is already scheduled
            if hasattr(self, '_positions_update_scheduled') and self._positions_update_scheduled:
                return

            # Get the positions table widget
            positions_tab = self.tabs.widget(0)
            positions_table = positions_tab.findChild(QTableWidget)
            if not positions_table:
                return

            # Skip if a worker is already running
            if hasattr(self, '_positions_worker') and self._positions_worker.isRunning():
                # Schedule an update for when the current one finishes
                self._positions_update_scheduled = True
                self._positions_worker.finished.connect(self._on_positions_worker_finished)
                return

            # We're good to go - update the timestamp and clear any scheduled flag
            self.last_positions_update = current_time
            self._positions_update_scheduled = False

            # Create and start the worker thread
            self._positions_worker = workers.PositionsWorker()
            self._positions_worker.fetched.connect(self._populate_positions_table)
            self._positions_worker.error.connect(self._handle_positions_error)
            self._positions_worker.start()
        except Exception as e:
            if DEBUG:
                print(f"Error starting positions worker: {e}")

    def _on_positions_worker_finished(self):
        """Called when a positions worker finishes - handles scheduled updates"""
        # If we have a scheduled update pending, trigger it now
        if hasattr(self, '_positions_update_scheduled') and self._positions_update_scheduled:
            # Use a single-shot timer with a small delay to avoid recursion
            QTimer.singleShot(100, self._update_positions)
            self._positions_update_scheduled = False

    def _handle_positions_error(self, error_msg):
        """Handle errors from the positions worker thread"""
        if DEBUG:
            print(f"Positions worker error: {error_msg}")
        self.statusBar().showMessage(f"Error loading positions: {error_msg}")

    def _populate_positions_table(self, positions):
        """Populate the positions table with the fetched positions data"""
        try:
            # Get the positions table widget
            positions_tab = self.tabs.widget(0)
            positions_table = positions_tab.findChild(QTableWidget)
            if not positions_table:
                return

            # Clear the table
            positions_table.setRowCount(0)

            # Get the current symbol for highlighting
            symbol = self.get_symbol()
            position_count = 0

            # Prepare trade data for performance dashboard
            trades_for_dashboard = []

            # Process each position
            for position in positions:
                # Extract position data with safety checks
                pos_symbol = position.get('symbol', 'Unknown')
                side = position.get('side', 'Unknown').upper()
                contracts = float(position.get('contracts', 0))
                entry_price = float(position.get('entryPrice', 0))

                # Skip positions with zero contracts
                if abs(contracts) <= 0:
                    continue

                # Get current price with fallbacks - handle None values safely
                mark_price = position.get('markPrice')
                current_price = float(mark_price) if mark_price is not None else 0

                # If current price is still 0, try lastPrice
                if current_price == 0:
                    last_price = position.get('lastPrice')
                    current_price = float(last_price) if last_price is not None else 0

                # Get leverage, margin, and other values with fallbacks - handle None values safely
                leverage_val = position.get('leverage')
                leverage = float(leverage_val) if leverage_val is not None else 0

                # Get notional value first
                notional_val = position.get('notional')
                notional = float(notional_val) if notional_val is not None else 0

                # Calculate notional if it's 0
                if notional == 0 and current_price > 0:
                    notional = current_price * contracts

                # Try to get collateral/margin with fallbacks
                collateral_val = position.get('collateral')
                collateral = float(collateral_val) if collateral_val is not None else 0

                # If collateral is still 0, try to get it from info
                if collateral == 0:
                    info_margin = position.get('info', {}).get('margin')
                    collateral = float(info_margin) if info_margin is not None else 0

                # If still 0, try to calculate from notional and leverage
                if collateral == 0 and leverage > 0 and notional > 0:
                    collateral = notional / leverage

                # Calculate PnL if we have prices
                if current_price > 0 and entry_price > 0:
                    if side == 'LONG':
                        pnl = (current_price - entry_price) * contracts
                    else:  # SHORT
                        pnl = (entry_price - current_price) * contracts
                else:
                    pnl = float(position.get('unrealizedPnl', 0))

                # Calculate PnL percentage
                pnl_percent = 0
                if entry_price > 0 and contracts > 0 and current_price > 0:  # Add check for current_price > 0
                    if side == 'LONG':
                        pnl_percent = ((current_price / entry_price) - 1) * 100
                    else:  # SHORT
                        pnl_percent = ((entry_price / current_price) - 1) * 100

                # Calculate PnL ratio (return on margin)
                pnl_ratio = 0
                if collateral > 0:
                    pnl_ratio = (pnl / collateral) * 100

                # Format values for display
                size_str = f"{contracts:.4f}".rstrip('0').rstrip('.') if contracts != int(contracts) else f"{int(contracts)}"
                entry_str = f"{entry_price:.8f}".rstrip('0').rstrip('.') if entry_price < 0.1 else f"{entry_price:.4f}".rstrip('0').rstrip('.')
                current_str = f"{current_price:.8f}".rstrip('0').rstrip('.') if current_price < 0.1 else f"{current_price:.4f}".rstrip('0').rstrip('.')
                pnl_str = f"${pnl:.2f}"
                pnl_percent_str = f"{pnl_percent:+.2f}%"
                leverage_str = f"{leverage:.1f}x" if leverage > 0 else "1.0x"
                margin_str = f"${collateral:.2f}" if collateral > 0 else "$0.00"
                pnl_ratio_str = f"{pnl_ratio:+.2f}%"

                # Add a new row
                row = positions_table.rowCount()
                positions_table.insertRow(row)

                # Determine text color based on PnL
                text_color = "#00ff44" if pnl > 0 else "#ff4444" if pnl < 0 else "#ffffff"

                # Highlight the current symbol
                bg_color = "rgba(0, 100, 0, 50)" if pos_symbol == symbol else "transparent"

                # Create and add table items with styling
                values = [pos_symbol, side, size_str, entry_str, current_str, pnl_str, pnl_percent_str, leverage_str, margin_str, pnl_ratio_str]
                for col, value in enumerate(values):
                    item = QTableWidgetItem(value)
                    item.setTextAlignment(Qt.AlignCenter)
                    # Apply styling
                    if col >= 5:  # PnL and related columns
                        item.setForeground(QColor(text_color))
                    if bg_color != "transparent":
                        item.setBackground(QColor(bg_color))
                    positions_table.setItem(row, col, item)

                position_count += 1

                # Add position data to trades for dashboard
                if hasattr(self, 'performance_dashboard'):
                    trade = {
                        'symbol': pos_symbol,
                        'direction': side.lower(),
                        'entry_price': entry_price,
                        'exit_price': current_price,  # Current price as "exit" for unrealized
                        'size': contracts,
                        'pnl': pnl,
                        'pnl_percent': pnl_percent,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'open',
                        'leverage': leverage,
                        'margin': collateral,
                        'timeframe': self.get_timeframe()
                    }
                    trades_for_dashboard.append(trade)

            # Update status message
            if position_count > 0:
                self.statusBar().showMessage(f"Loaded {position_count} positions")
            else:
                self.statusBar().showMessage("No open positions")

            # Update performance dashboard with collected trade data
            if trades_for_dashboard:
                self._update_performance_dashboard(trades_for_dashboard)

        except Exception as e:
            print(f"Error populating positions table: {e}")
            import traceback
            traceback.print_exc()


    def _update_orders(self):
        """Update the orders table with real data or demo data"""
        try:
            # Use a class variable to track last update time to prevent excessive updates
            current_time = datetime.now()
            if hasattr(self, 'last_orders_update') and (current_time - self.last_orders_update).total_seconds() < 2.0:
                return  # Skip update if less than 2 seconds have passed

            self.last_orders_update = current_time

            # Get the orders table widget
            orders_tab = self.tabs.widget(1)
            orders_table = orders_tab.findChild(QTableWidget)
            if not orders_table:
                return

            # Clear the table
            orders_table.setRowCount(0)

            if demo_mode:
                # Demo mode - show sample orders
                demo_orders = [
                    {"symbol": "BTC/USDT", "side": "buy", "type": "limit", "amount": 0.01, "price": 49500, "status": "open"},
                    {"symbol": "ETH/USDT", "side": "sell", "type": "limit", "amount": 0.1, "price": 3100, "status": "open"}
                ]
                orders = demo_orders
            else:
                # Fetch real orders - validate symbol first
                symbol = self.get_symbol()
                # Only use symbol if it's valid
                if symbol and len(symbol) >= 3 and '/' in symbol:
                    orders = fetch_open_orders(symbol)
                else:
                    orders = fetch_open_orders()

            # Populate the table
            for i, order in enumerate(orders):
                orders_table.insertRow(i)

                # Symbol
                orders_table.setItem(i, 0, QTableWidgetItem(order.get('symbol', 'Unknown')))

                # Side
                side = order.get('side', 'Unknown')
                side_item = QTableWidgetItem(side.upper())
                side_item.setForeground(Qt.green if side == 'buy' else Qt.red)
                orders_table.setItem(i, 1, side_item)

                # Type
                orders_table.setItem(i, 2, QTableWidgetItem(order.get('type', 'Unknown').upper()))

                # Size
                orders_table.setItem(i, 3, QTableWidgetItem(str(order.get('amount', 0))))

                # Price
                orders_table.setItem(i, 4, QTableWidgetItem(str(order.get('price', 0))))

                # Status
                orders_table.setItem(i, 5, QTableWidgetItem(order.get('status', 'Unknown').upper()))
        except Exception as e:
            # Don't print the same error repeatedly
            error_msg = str(e)
            if not hasattr(self._update_orders, 'last_error') or self._update_orders.last_error != error_msg:
                print(f"Error updating orders: {e}")
                self._update_orders.last_error = error_msg

    def show_api_credentials_dialog(self):
        """Show dialog to enter and manage API credentials"""
        global exchange, demo_mode

        dialog = QDialog(self)
        dialog.setWindowTitle("API Credentials Manager")
        dialog.setMinimumWidth(600)
        dialog.setMinimumHeight(500)

        layout = QVBoxLayout()

        # Create tabs for different functions
        tabs = QTabWidget()

        # Tab 1: Select Account
        select_account_tab = QWidget()
        select_layout = QVBoxLayout(select_account_tab)

        # Account selection
        select_layout.addWidget(QLabel("Select an account to use:"))

        # Account list table
        account_table = QTableWidget(0, 4)
        account_table.setHorizontalHeaderLabels(["Account Name", "Exchange", "Description", "Select"])
        account_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        account_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        account_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        select_layout.addWidget(account_table)

        # Load accounts button
        refresh_btn = QPushButton("Refresh Accounts")
        select_layout.addWidget(refresh_btn)

        # Tab 2: Add/Edit Account
        add_account_tab = QWidget()
        add_layout = QVBoxLayout(add_account_tab)

        # Account name
        account_label = QLabel("Account Name:")
        account_input = QLineEdit()
        account_input.setPlaceholderText("Enter a name for this account")
        add_layout.addWidget(account_label)
        add_layout.addWidget(account_input)

        # Exchange selection
        exchange_label = QLabel("Exchange:")
        exchange_combo = QComboBox()
        exchange_combo.addItems(["huobi", "binance", "okx", "bybit"])
        add_layout.addWidget(exchange_label)
        add_layout.addWidget(exchange_combo)

        # API Key
        api_key_label = QLabel("API Key:")
        api_key_input = QLineEdit()
        api_key_input.setPlaceholderText("Enter your API key")
        add_layout.addWidget(api_key_label)
        add_layout.addWidget(api_key_input)

        # Secret Key
        secret_key_label = QLabel("Secret Key:")
        secret_key_input = QLineEdit()
        secret_key_input.setPlaceholderText("Enter your secret key")
        secret_key_input.setEchoMode(QLineEdit.Password)
        add_layout.addWidget(secret_key_label)
        add_layout.addWidget(secret_key_input)

        # Description
        description_label = QLabel("Description:")
        description_input = QLineEdit()
        description_input.setPlaceholderText("Enter a description for this account")
        add_layout.addWidget(description_label)
        add_layout.addWidget(description_input)

        # Set as default checkbox
        default_checkbox = QCheckBox("Set as default account")
        add_layout.addWidget(default_checkbox)

        # Save account button
        save_btn = QPushButton("Save Account")
        add_layout.addWidget(save_btn)

        # Add tabs to tab widget
        tabs.addTab(select_account_tab, "Select Account")
        tabs.addTab(add_account_tab, "Add/Edit Account")

        layout.addWidget(tabs)

        # Close button
        close_btn = QPushButton("Close")
        layout.addWidget(close_btn)

        dialog.setLayout(layout)

        # Function to load accounts into the table
        def load_accounts():
            account_table.setRowCount(0)  # Clear existing rows

            try:
                # Try both the current directory and the me3 - Copy directory
                credentials_path = 'credentials.yaml'
                if not os.path.exists(credentials_path):
                    credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

                if not os.path.exists(credentials_path):
                    print(f"Credentials file not found at: {credentials_path}")
                    return

                print(f"Loading credentials from: {credentials_path}")
                with open(credentials_path, 'r') as file:
                    credentials = yaml.safe_load(file)

                    if not credentials or 'accounts' not in credentials:
                        print("No accounts found in credentials file")
                        return

                    accounts = credentials.get('accounts', [])
                    default_account = credentials.get('default_account', '')

                    print(f"Found {len(accounts)} accounts in credentials file")

                    for i, account in enumerate(accounts):
                        account_table.insertRow(i)

                        # Account name
                        name_item = QTableWidgetItem(account.get('name', ''))
                        account_table.setItem(i, 0, name_item)

                        # Exchange
                        exchange_item = QTableWidgetItem(account.get('exchange', ''))
                        account_table.setItem(i, 1, exchange_item)

                        # Description
                        description_item = QTableWidgetItem(account.get('description', ''))
                        account_table.setItem(i, 2, description_item)

                        # Select button
                        select_btn = QPushButton("Select")
                        select_btn.setProperty('account_index', i)
                        account_table.setCellWidget(i, 3, select_btn)

                        # Highlight default account
                        if account.get('name', '') == default_account:
                            for col in range(3):
                                item = account_table.item(i, col)
                                item.setBackground(QColor(0, 100, 0, 50))  # Light green background

                        # Connect button to select account function
                        select_btn.clicked.connect(lambda _, idx=i: select_account(idx))  # Use _ to indicate unused parameter
            except Exception as e:
                print(f"Error loading accounts: {e}")

        # Function to select an account
        def select_account(index):
            try:
                # Try both the current directory and the me3 - Copy directory
                credentials_path = 'credentials.yaml'
                if not os.path.exists(credentials_path):
                    credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

                if not os.path.exists(credentials_path):
                    print(f"Credentials file not found at: {credentials_path}")
                    return

                print(f"Loading credentials from: {credentials_path}")
                with open(credentials_path, 'r') as file:
                    credentials = yaml.safe_load(file)

                    if not credentials or 'accounts' not in credentials:
                        print("No accounts found in credentials file")
                        return

                    accounts = credentials.get('accounts', [])

                    if index < 0 or index >= len(accounts):
                        print(f"Invalid account index: {index}")
                        return

                    account = accounts[index]

                    # Get account details
                    account_name = account.get('name', '')
                    exchange_name = account.get('exchange', '')
                    api_key = account.get('api_key', '')
                    secret_key = account.get('secret_key', '')

                    if not api_key or not secret_key:
                        QMessageBox.warning(dialog, "Invalid Credentials", "API Key and Secret Key are required.")
                        return

                    # Initialize the exchange with the selected account
                    exchange_class = getattr(ccxt, exchange_name)
                    new_exchange = exchange_class({
                        'apiKey': api_key,
                        'secret': secret_key,
                        'enableRateLimit': True,
                        'options': {
                            'defaultType': 'swap',  # Use swap for futures trading
                        }
                    })

                    # Load markets
                    try:
                        print(f"Loading markets for account: {account_name}...")
                        new_exchange.load_markets()
                        print("Markets loaded successfully")

                        # Update the global exchange
                        global exchange
                        exchange = new_exchange

                        # Update demo mode flag
                        global demo_mode
                        demo_mode = False

                        # Update all components that depend on the exchange
                        dialog.accept()  # Close the dialog

                        # Update the UI
                        self.update_after_exchange_change()

                        QMessageBox.information(dialog, "Success", f"Now using account: {account_name} ({exchange_name})")
                    except Exception as e:
                        error_msg = f"Error loading markets: {e}"
                        print(error_msg)
                        QMessageBox.critical(dialog, "Error", error_msg)
            except Exception as e:
                error_msg = f"Error selecting account: {e}"
                print(error_msg)
                QMessageBox.critical(dialog, "Error", error_msg)

        # Function to save a new account
        def save_account():
            # Get values from inputs
            account_name = account_input.text().strip()
            exchange_name = exchange_combo.currentText()
            api_key = api_key_input.text().strip()
            secret_key = secret_key_input.text().strip()
            description = description_input.text().strip()
            set_as_default = default_checkbox.isChecked()

            if not account_name:
                QMessageBox.warning(dialog, "Invalid Input", "Account Name is required.")
                return

            if not api_key or not secret_key:
                QMessageBox.warning(dialog, "Invalid Input", "API Key and Secret Key are required.")
                return

            try:
                # Try both the current directory and the me3 - Copy directory
                credentials_path = 'credentials.yaml'
                if not os.path.exists(credentials_path):
                    credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

                # Load existing credentials or create new structure
                credentials = {'accounts': []}
                if os.path.exists(credentials_path):
                    print(f"Loading credentials from: {credentials_path}")
                    with open(credentials_path, 'r') as file:
                        loaded_creds = yaml.safe_load(file)
                        if loaded_creds:
                            credentials = loaded_creds

                # Ensure accounts list exists
                if 'accounts' not in credentials:
                    credentials['accounts'] = []

                # Check if account with same name already exists
                account_exists = False
                for i, account in enumerate(credentials['accounts']):
                    if account.get('name', '') == account_name:
                        # Update existing account
                        credentials['accounts'][i] = {
                            'name': account_name,
                            'exchange': exchange_name,
                            'api_key': api_key,
                            'secret_key': secret_key,
                            'description': description
                        }
                        account_exists = True
                        break

                # Add new account if it doesn't exist
                if not account_exists:
                    credentials['accounts'].append({
                        'name': account_name,
                        'exchange': exchange_name,
                        'api_key': api_key,
                        'secret_key': secret_key,
                        'description': description
                    })

                # Set as default if checked
                if set_as_default:
                    credentials['default_account'] = account_name

                # Save to file
                print(f"Saving credentials to: {credentials_path}")
                with open(credentials_path, 'w') as file:
                    yaml.dump(credentials, file)

                # Refresh the accounts table
                load_accounts()

                # Clear inputs
                account_input.clear()
                api_key_input.clear()
                secret_key_input.clear()
                description_input.clear()
                default_checkbox.setChecked(False)

                QMessageBox.information(dialog, "Success", f"Account '{account_name}' saved successfully.")

                # Switch to select account tab
                tabs.setCurrentIndex(0)

            except Exception as e:
                error_msg = f"Error saving account: {e}"
                print(error_msg)
                QMessageBox.critical(dialog, "Error", error_msg)

        # Connect signals
        refresh_btn.clicked.connect(load_accounts)
        save_btn.clicked.connect(save_account)
        close_btn.clicked.connect(dialog.reject)

        # Load accounts initially
        load_accounts()

        # Show the dialog
        dialog.exec()

    def toggle_demo_mode(self):
        """Toggle between demo and live mode"""
        global demo_mode

        if demo_mode:
            # Currently in demo mode, try to switch to live mode
            # Try both the current directory and the me3 - Copy directory
            credentials_path = 'credentials.yaml'
            if not os.path.exists(credentials_path):
                credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

            if os.path.exists(credentials_path):
                # We have credentials, ask if user wants to switch to live mode
                reply = QMessageBox.question(self, "Switch to Live Mode",
                                           f"Do you want to switch to live mode using saved credentials from {credentials_path}?",
                                           QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    # Try to initialize the exchange with saved credentials
                    try:
                        self.initialize_exchange_from_credentials()
                    except Exception as e:
                        error_msg = f"Error switching to live mode: {e}"
                        print(error_msg)
                        QMessageBox.critical(self, "Error", error_msg)
            else:
                # No credentials file, show the credentials dialog
                print(f"No credentials file found at: {credentials_path}")
                self.show_api_credentials_dialog()
        else:
            # Currently in live mode, ask if user wants to switch to demo mode
            reply = QMessageBox.question(self, "Switch to Demo Mode",
                                       "Do you want to switch to demo mode? No real trades will be executed.",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                # Switch to demo mode
                global exchange
                exchange = None
                demo_mode = True

                # Update all components that depend on the exchange
                self.update_after_exchange_change()

                QMessageBox.information(self, "Success", "Switched to demo mode. No real trades will be executed.")

    def initialize_exchange_from_credentials(self):
        """Initialize the exchange using saved credentials (default account)"""
        global exchange, demo_mode

        try:
            # Try both the current directory and the me3 - Copy directory
            credentials_path = 'credentials.yaml'
            if not os.path.exists(credentials_path):
                credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

            if not os.path.exists(credentials_path):
                raise ValueError(f"Credentials file not found at: {credentials_path}")

            print(f"Loading credentials from: {credentials_path}")
            with open(credentials_path, 'r') as file:
                credentials = yaml.safe_load(file)

                if not credentials or 'accounts' not in credentials:
                    raise ValueError("No accounts found in credentials file")

                # Get default account name
                default_account_name = credentials.get('default_account', '')

                # Find the default account
                default_account = None
                for account in credentials.get('accounts', []):
                    if account.get('name', '') == default_account_name:
                        default_account = account
                        break

                # If no default account found, use the first account
                if not default_account and credentials.get('accounts', []):
                    default_account = credentials['accounts'][0]
                    print(f"No default account found, using first account: {default_account.get('name', '')}")

                if not default_account:
                    raise ValueError("No accounts found in credentials file")

                # Get account details
                account_name = default_account.get('name', '')
                exchange_name = default_account.get('exchange', 'huobi')
                api_key = default_account.get('api_key', '')
                secret_key = default_account.get('secret_key', '')

                if not api_key or not secret_key:
                    raise ValueError("API Key or Secret Key is missing in credentials file")

                # Initialize the exchange
                exchange_class = getattr(ccxt, exchange_name)
                new_exchange = exchange_class({
                    'apiKey': api_key,
                    'secret': secret_key,
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'swap',  # Use swap for futures trading
                    }
                })

                # Load markets
                print(f"Loading markets for account: {account_name}...")
                new_exchange.load_markets()
                print("Markets loaded successfully")

                # Update the global exchange
                exchange = new_exchange

                # Update demo mode flag
                demo_mode = False

                # Update all components that depend on the exchange
                self.update_after_exchange_change()

                QMessageBox.information(self, "Success", f"Switched to live mode using account: {account_name} ({exchange_name})")

                return True
        except Exception as e:
            error_msg = f"Error initializing exchange from credentials: {e}"
            print(error_msg)
            QMessageBox.critical(self, "Error", error_msg)
            return False

    def update_after_exchange_change(self):
        """Update all components that depend on the exchange"""
        global exchange, demo_mode

        # Update status bar
        mode_text = "DEMO MODE" if demo_mode else "LIVE MODE"
        self.statusBar().showMessage(f"Running in {mode_text}", 5000)

        # Update account status label in menu bar
        if demo_mode:
            self.account_status_label.setText("DEMO MODE")
            self.account_status_label.setStyleSheet("color: #ff9900; padding: 0 10px;")
        else:
            # Account name will be set in the code below when fetching account info
            self.account_status_label.setText("LIVE MODE")
            self.account_status_label.setStyleSheet("color: #00ff44; padding: 0 10px;")

        # Update the dashboard if it exists
        if hasattr(self, 'dashboard_tab'):
            self.dashboard_tab.refresh_dashboard()

        # Update the performance dashboard if it exists
        if hasattr(self, 'dashboard_tab') and hasattr(self.dashboard_tab, 'performance_dashboard'):
            self.dashboard_tab.performance_dashboard.refresh_dashboard()

        # Update positions and orders
        if hasattr(self, '_update_positions'):
            self._update_positions()

        if hasattr(self, '_update_orders'):
            self._update_orders()

        # Update chart data
        if hasattr(self, '_update_chart'):
            self._update_chart()

        # Update order book
        if hasattr(self, 'orderbook') and hasattr(self.orderbook, 'update'):
            self.orderbook.update(self.get_symbol())

        # Update mini widgets
        if hasattr(self, 'mini_widgets'):
            self.mini_widgets.update_all(self.get_symbol())

    def _update_performance_dashboard(self, trades=None):
        """Update the performance dashboard with trade data"""
        # Check if we have a dashboard tab with a performance dashboard
        if not hasattr(self, 'dashboard_tab') or not hasattr(self.dashboard_tab, 'performance_dashboard'):
            return

        try:
            if trades:
                # Add new trades to the dashboard
                for trade in trades:
                    self.dashboard_tab.performance_dashboard.add_trade(trade)

            # Generate equity curve data if not already present
            if not hasattr(self, 'equity_curve_data'):
                self.equity_curve_data = []

                # Start with initial equity
                initial_equity = 10000  # Default starting equity

                # Try to get actual account balance
                try:
                    if not demo_mode and exchange is not None:
                        balance = exchange.fetch_balance()
                        if balance and 'total' in balance and 'USDT' in balance['total']:
                            initial_equity = balance['total']['USDT']
                except Exception as e:
                    print(f"Error fetching account balance: {e}")

                # Create initial equity point
                start_date = datetime.now() - timedelta(days=30)
                self.equity_curve_data.append({
                    'timestamp': start_date,
                    'equity': initial_equity,
                    'drawdown': 0
                })

            # Update equity curve with latest data
            if self.equity_curve_data:
                # Get latest equity
                latest_equity = self.equity_curve_data[-1]['equity']

                # Add PnL from current positions
                positions = fetch_open_positions()
                total_pnl = sum(float(pos.get('unrealizedPnl', 0)) for pos in positions)

                # Add new equity point
                current_equity = latest_equity + total_pnl

                # Calculate drawdown
                peak_equity = max(point['equity'] for point in self.equity_curve_data)
                if current_equity > peak_equity:
                    peak_equity = current_equity

                drawdown = 0
                if peak_equity > 0:
                    drawdown = ((peak_equity - current_equity) / peak_equity) * 100

                # Add to equity curve
                self.equity_curve_data.append({
                    'timestamp': datetime.now(),
                    'equity': current_equity,
                    'drawdown': drawdown
                })

                # Limit equity curve to 1000 points
                if len(self.equity_curve_data) > 1000:
                    self.equity_curve_data = self.equity_curve_data[-1000:]

            # Update the dashboard with equity curve
            self.dashboard_tab.performance_dashboard.update_dashboard({
                'equity_curve': self.equity_curve_data
            })

        except Exception as e:
            print(f"Error updating performance dashboard: {e}")
            import traceback
            traceback.print_exc()
            try:
                # Try both the current directory and the me3 - Copy directory
                credentials_path = 'credentials.yaml'
                if not os.path.exists(credentials_path):
                    credentials_path = os.path.join('me3 - Copy', 'credentials.yaml')

                if os.path.exists(credentials_path):
                    print(f"Loading credentials from: {credentials_path}")
                    with open(credentials_path, 'r') as file:
                        credentials = yaml.safe_load(file)

                        # Check if we can determine which account is being used
                        if exchange and hasattr(exchange, 'apiKey'):
                            api_key = exchange.apiKey

                            # Find the account with this API key
                            for account in credentials.get('accounts', []):
                                if account.get('api_key', '') == api_key:
                                    account_name = account.get('name', 'LIVE')
                                    break
            except Exception as e:
                print(f"Error getting account name: {e}")

            # Get account info to display equity and free balance
            try:
                acct_info = fetch_account_info()
                equity = acct_info.get('equity', '$0.00')
                free = acct_info.get('free_balance', '$0.00')
                status = acct_info.get('status', 'inactive').capitalize()

                # Format the account status label with equity and free balance
                self.account_status_label.setText(f"Equity: {equity} Free: {free} Status: {status} | LIVE: {account_name}")
            except Exception as e:
                print(f"Error getting account info for status label: {e}")
                self.account_status_label.setText(f"LIVE: {account_name}")

            self.account_status_label.setStyleSheet("color: #00ff44; padding: 0 10px;")

        # Update worker module
        init_workers()

        # Update checkbox functions module
        checkbox_functions.init_checkbox_functions(
            exchange,
            demo_mode,
            fetch_open_positions,
            fetch_best_bid,
            fetch_best_ask,
            place_limit_order,
            place_market_order,
            close_position,
            fetch_ohlcv,
            set_leverage,
            DEBUG
        )

        # Clear caches
        position_cache['data'] = []
        position_cache['timestamp'] = 0
        order_cache['data'] = {}
        order_cache['timestamp'] = 0
        account_cache['data'] = {}
        account_cache['timestamp'] = 0
        orderbook_cache['data'] = {}
        orderbook_cache['timestamp'] = 0

        # Force update of UI components
        if hasattr(self, '_update_positions'):
            self._update_positions()
        if hasattr(self, '_update_orders'):
            self._update_orders()

        # Log the mode change (only if not already logged during initialization)
        if not hasattr(initialize_exchange, 'demo_mode_printed'):
            print(f"🔍 demo_mode = {demo_mode}")
            initialize_exchange.demo_mode_printed = True

    def show_help(self):
        print("EpiNn0x: Advanced futures trading interface.")
        self.statusBar().showMessage("EpiNn0x: Advanced futures trading interface.", 3000)

    # ─── Quick‐Qty Presets & Slider Callbacks ────────────────────────────
    def apply_qty_preset(self, factor):
        """Apply a preset factor to the quantity based on free balance"""
        try:
            acct = fetch_account_info()   # Call the free function, not self.fetch_account_info
            free_str = acct.get('free_balance', '$0').replace('$','').replace(',','')
            free = float(free_str)
            target = free if factor is None else free * factor
            self.qty_sb.setValue(round(target, self.qty_sb.decimals()))
            print(f"Qty preset {factor or 'Max'} → {target}")
        except Exception as e:
            print(f"apply_qty_preset error: {e}")

    def on_qty_slider(self, pct):
        """Set quantity based on slider percentage of free balance"""
        try:
            acct = fetch_account_info()
            free = float(acct.get('free_balance','$0').replace('$','').replace(',',''))
            qty = free * pct/100.0
            self.qty_sb.setValue(round(qty, self.qty_sb.decimals()))
            print(f"Qty slider {pct}% → {qty}")
        except Exception as e:
            print(f"on_qty_slider error: {e}")

    # ─── Chart‐Click Order Entry ─────────────────────────────────────
    def on_chart_click(self, event):
        """
        Handle chart clicks:
        - Normal mode: Left-click = Buy, Right-click = Sell
        - Trend line mode: First click starts line, second click completes it
        """
        # Ensure the click is directly on the chart view
        if not self.chart.plotItem.vb.sceneBoundingRect().contains(event.scenePos()):
            # Click was not on the chart area
            return

        # Map scene→view
        p = event.scenePos()
        mouse = self.chart.plotItem.vb.mapSceneToView(p)
        click_x = mouse.x()  # Time value
        click_y = mouse.y()  # Price value

        # Handle trend line drawing mode
        if hasattr(self, 'trend_line_mode') and self.trend_line_mode:
            if not self.drawing_trend_line:
                # First click - start the trend line
                self.trend_line_start = (click_x, click_y)
                self.drawing_trend_line = True
                self.statusBar().showMessage(f"Trend line started at price {click_y:.4f}. Click again to complete.")

                # Add a temporary marker at the start point
                start_marker = pg.ScatterPlotItem()
                start_marker.addPoints([click_x], [click_y], symbol='o', size=10, pen='w', brush='y')
                self.chart.addItem(start_marker)
                self.trend_line_items.append(start_marker)

                return
            else:
                # Second click - complete the trend line
                # Create a line from start to end point
                start_x, start_y = self.trend_line_start

                # Calculate the angle of the line in degrees
                dx = click_x - start_x
                dy = click_y - start_y
                angle = math.degrees(math.atan2(dy, dx))

                # Calculate the position (which is the midpoint of the line)
                mid_x = (start_x + click_x) / 2

                # Calculate the length of the line
                length = math.sqrt(dx**2 + dy**2)

                # Create the movable trend line
                trend_line = MovableTrendLine(
                    pos=QPointF(mid_x, (start_y + click_y) / 2),  # Position at midpoint (x,y)
                    angle=angle,  # Angle in degrees
                    pen=pg.mkPen('y', width=2),  # Yellow pen
                    movable=True,  # Make it movable
                    label=f"Trend: {(dy/start_y)*100:.2f}%"  # Add a label
                )

                # Set the line length
                trend_line.setLength(length)

                self.chart.addItem(trend_line)
                self.trend_line_items.append(trend_line)

                # We don't need to add a separate label since our MovableTrendLine already has one

                # Reset drawing state
                self.drawing_trend_line = False
                self.trend_line_start = None
                self.statusBar().showMessage(f"Trend line completed. Press 'T' to exit trend line mode.")
                return

        # Normal trading mode (when not in trend line mode)
        price = click_y
        sym = self.get_symbol()
        qty = self.get_quantity()
        if price <= 0 or qty <= 0:
            return

        # Left-click = Buy, Right-click = Sell
        if event.button() == Qt.LeftButton:
            side = 'buy'
        elif event.button() == Qt.RightButton:
            side = 'sell'
        else:
            return

        params = {'offset':'open','lever_rate': self.get_leverage()}
        place_limit_order(sym, side, qty, round(price,4), params)
        print(f"Chart-click→{side} {sym} {qty}@{price:.4f}")

    # ─── Positions Table Context Menu ────────────────────────────────
    def on_position_menu(self, point):
        """Right-click menu: Reverse / Hedge / Limit-Close / Market-Close"""
        row = self.positions_table.rowAt(point.y())
        if row < 0:
            return

        sym  = self.positions_table.item(row,0).text()
        side = self.positions_table.item(row,1).text().lower()
        size = float(self.positions_table.item(row,2).text())

        menu = QMenu(self)
        act_reverse     = menu.addAction("Reverse")
        act_hedge       = menu.addAction("Hedge")
        act_limit_close = menu.addAction("Limit Close")
        act_market_close= menu.addAction("Market Close")
        action = menu.exec(self.positions_table.mapToGlobal(point))
        if not action:
            return

        # what side we need to place to close
        opposite = 'sell' if side=='long' else 'buy'
        lev = self.get_leverage()

        if action==act_reverse:
            # close existing then open opposite
            place_market_order(sym, opposite, size, params={'offset':'close', 'lever_rate': lev})
            place_market_order(sym, opposite, size, params={'offset':'open', 'lever_rate': lev})
        elif action==act_hedge:
            # just open opposite
            place_market_order(sym, opposite, size, params={'offset':'open', 'lever_rate': lev})
        elif action==act_limit_close:
            # pick best bid/ask
            price = fetch_best_bid(sym) if side=='long' else fetch_best_ask(sym)
            if price:
                place_limit_order(sym, opposite, size, price, params={'offset':'close', 'lever_rate': lev})
        elif action==act_market_close:
            place_market_order(sym, opposite, size, params={'offset':'close', 'lever_rate': lev})

        # finally refresh the table
        self._update_positions()

    # ─── Helpers to read UI fields ───────────────────────────────────────
    def get_symbol(self):
        try:
            # Get the current text from the dropdown
            symbol_text = self.symbol_cb.currentText().strip()

            # Extract the base symbol without the price change info
            # Format is like "BTC/USDT:USDT (+2.45%)" or "ETH/USDT:USDT (-1.23%)"
            base_symbol = symbol_text.split(' ')[0] if ' ' in symbol_text else symbol_text

            debug_print(f"get_symbol returning: {base_symbol} (from {symbol_text})")
            return base_symbol
        except Exception as e:
            debug_print(f"Error in get_symbol: {e}, returning default: {default_symbol}")
            return default_symbol

    def get_quantity(self):
        return float(self.qty_sb.value())

    def get_leverage(self):
        return int(self.lev_sb.value())

    def get_timeframe(self):
        """Get the current timeframe"""
        if hasattr(self, 'current_timeframe'):
            return self.current_timeframe
        elif hasattr(self, 'timeframe_cb') and self.timeframe_cb.currentText():
            return self.timeframe_cb.currentText()
        else:
            return "5m"  # Default timeframe

    # ─── Bracket Order Helper ────────────────────────────────────
    def create_bracket_order(self, symbol, side, size, entry_price, stop_price, take_profit_price):
        """
        Place entry limit + stop-market SL + limit TP.
        Returns a tuple of (entry, stop_order, tp_order) or None on failure.
        """
        # 1) Entry
        entry = place_limit_order(
            symbol, side, size, entry_price,
            params={'offset':'open','lever_rate':self.get_leverage()}
        )
        if not entry:
            print(f"Failed to place entry order for bracket order")
            return None

        # opposite side for SL/TP
        opp = 'sell' if side=='buy' else 'buy'

        # Wait a moment for the entry order to be processed
        # This helps avoid the "Insufficient open positions" error
        time.sleep(0.5)

        # 2) Stop-Loss - with specific offset and reduceOnly parameters for Huobi/HTX
        stop_params = {
            'lever_rate': self.get_leverage(),
            'offset': 'close',
            'reduceOnly': True
        }

        # For stop loss, we'll try a limit order if the stop order fails
        try:
            stop_order = place_stop_order(
                symbol, opp, size, stop_price,
                params=stop_params
            )

            if not stop_order:
                print(f"Warning: Failed to place stop loss order. Trying limit order...")
                # Try with a limit order at the stop price instead
                stop_order = place_limit_order(
                    symbol, opp, size, stop_price,
                    params={'offset':'close', 'lever_rate':self.get_leverage()}
                )
        except Exception as e:
            print(f"Error placing stop loss: {e}")
            stop_order = None

        # 3) Take-Profit
        tp_order = place_limit_order(
            symbol, opp, size, take_profit_price,
            params={'offset':'close','lever_rate':self.get_leverage()}
        )

        return entry, stop_order, tp_order

    def place_limit_long(self):
        sym = self.get_symbol()
        ts = datetime.now().strftime("%H:%M:%S")
        try:
            ob = fetch_order_book(sym)
            if not ob or 'bids' not in ob or not ob['bids']:
                print(f"Error: Could not fetch order book for {sym}")
                return

            price = ob['bids'][0][0]  # Use best bid price
            qty = self.get_quantity()
            lev = self.get_leverage()

            # Set leverage and place order
            set_leverage(sym, lev)

            # Check if SL and TP are set
            sl_price = self.sl_sb.value()
            tp_price = self.tp_sb.value()

            if sl_price > 0 and tp_price > 0:
                # Use bracket order
                result = self.create_bracket_order(sym, 'buy', qty, price, sl_price, tp_price)
                if result:
                    # Unpack result but we don't need to use the variables
                    _, _, _ = result
                    print(f"{ts} Bracket Long: Entry @ {price}, SL @ {sl_price}, TP @ {tp_price}")
                    self.statusBar().showMessage(f"Bracket Long placed", 3000)
                else:
                    print("Failed to place bracket order")
                    self.statusBar().showMessage("Failed to place bracket order", 3000)
            else:
                # Regular limit order
                params = {'offset': 'open', 'lever_rate': lev}
                order = place_limit_order(sym, 'buy', qty, price, params)
                if order:
                    print(f"{ts} Limit Long: Placed @ {price}")
                    self.statusBar().showMessage(f"Limit Long placed @ {price}", 3000)
                else:
                    print("Failed to place order")
                    self.statusBar().showMessage("Failed to place order", 3000)

            # Update tables
            self._update_positions()
        except Exception as e:
            print(f"Error in place_limit_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_limit_short(self):
        sym = self.get_symbol()
        ts = datetime.now().strftime("%H:%M:%S")
        try:
            ob = fetch_order_book(sym)
            if not ob or 'asks' not in ob or not ob['asks']:
                print(f"Error: Could not fetch order book for {sym}")
                return

            price = ob['asks'][0][0]  # Use best ask price
            qty = self.get_quantity()
            lev = self.get_leverage()

            # Set leverage and place order
            set_leverage(sym, lev)

            # Check if SL and TP are set
            sl_price = self.sl_sb.value()
            tp_price = self.tp_sb.value()

            if sl_price > 0 and tp_price > 0:
                # Use bracket order
                result = self.create_bracket_order(sym, 'sell', qty, price, sl_price, tp_price)
                if result:
                    # Unpack result but we don't need to use the variables
                    _, _, _ = result
                    print(f"{ts} Bracket Short: Entry @ {price}, SL @ {sl_price}, TP @ {tp_price}")
                    self.statusBar().showMessage(f"Bracket Short placed", 3000)
                else:
                    print("Failed to place bracket order")
                    self.statusBar().showMessage("Failed to place bracket order", 3000)
            else:
                # Regular limit order
                params = {'offset': 'open', 'lever_rate': lev}
                order = place_limit_order(sym, 'sell', qty, price, params)
                if order:
                    print(f"{ts} Limit Short: Placed @ {price}")
                    self.statusBar().showMessage(f"Limit Short placed @ {price}", 3000)
                else:
                    print("Failed to place order")
                    self.statusBar().showMessage("Failed to place order", 3000)

            # Update tables
            self._update_positions()
        except Exception as e:
            print(f"Error in place_limit_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_long(self):
        sym = self.get_symbol()
        ts = datetime.now().strftime("%H:%M:%S")
        try:
            qty = self.get_quantity()
            lev = self.get_leverage()

            # Set leverage and place order
            set_leverage(sym, lev)
            params = {'offset': 'open', 'lever_rate': lev}
            order = place_market_order(sym, 'buy', qty, params)

            if order:
                print(f"{ts} Market Long: Executed @ Market")
                self.statusBar().showMessage(f"Market Long executed", 3000)
                # Update tables
                self._update_positions()
            else:
                print("Failed to place market order")
                self.statusBar().showMessage("Failed to place market order", 3000)
        except Exception as e:
            print(f"Error in place_market_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_short(self):
        sym = self.get_symbol()
        ts = datetime.now().strftime("%H:%M:%S")
        try:
            qty = self.get_quantity()
            lev = self.get_leverage()

            # Set leverage and place order
            set_leverage(sym, lev)
            params = {'offset': 'open', 'lever_rate': lev}
            order = place_market_order(sym, 'sell', qty, params)

            if order:
                print(f"{ts} Market Short: Executed @ Market")
                self.statusBar().showMessage(f"Market Short executed", 3000)
                # Update tables
                self._update_positions()
            else:
                print("Failed to place market order")
                self.statusBar().showMessage("Failed to place market order", 3000)
        except Exception as e:
            print(f"Error in place_market_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def close_all_positions(self):
        ts = datetime.now().strftime("%H:%M:%S")
        try:
            result = close_all_positions()
            if result:
                print(f"{ts} All positions closed")
                # Update tables
                self._update_positions()
            else:
                print("Failed to close positions")
        except Exception as e:
            print(f"Error closing positions: {str(e)}")

    def cancel_all_orders(self):
        ts = datetime.now().strftime("%H:%M:%S")
        try:
            result = cancel_all_orders()
            if result:
                print(f"{ts} All open orders canceled")
                # Update orders table
                self._update_orders()
            else:
                print("Failed to cancel orders")
        except Exception as e:
            print(f"Error canceling orders: {str(e)}")

    def toggle_trend_line_mode(self):
        """Toggle trend line drawing mode on/off"""
        # Initialize variables if they don't exist
        if not hasattr(self, 'trend_line_mode'):
            self.trend_line_mode = False
            self.drawing_trend_line = False
            self.trend_line_start = None
            self.trend_line_items = []

        # Toggle the mode
        self.trend_line_mode = not self.trend_line_mode

        # Update the toolbar button state if it exists
        if hasattr(self, 'trend_line_btn'):
            self.trend_line_btn.setChecked(self.trend_line_mode)

        if self.trend_line_mode:
            # Entering trend line mode
            self.statusBar().showMessage("Trend line mode activated. Click on chart to start drawing a trend line.")
            # Change cursor to indicate drawing mode
            self.chart.setCursor(Qt.CrossCursor)
        else:
            # Exiting trend line mode
            self.statusBar().showMessage("Trend line mode deactivated.")
            # Reset cursor
            self.chart.setCursor(Qt.ArrowCursor)
            # Reset drawing state
            self.drawing_trend_line = False
            self.trend_line_start = None

    def clear_trend_lines(self):
        """Clear all trend lines from the chart"""
        if hasattr(self, 'trend_line_items'):
            for item in self.trend_line_items:
                self.chart.removeItem(item)
            self.trend_line_items = []
            self.statusBar().showMessage("All trend lines cleared.")

    def show_indicators_dialog(self):
        """Show the indicator settings dialog"""
        # Get current indicator settings
        if not hasattr(self, 'indicator_settings'):
            # Default settings
            self.indicator_settings = {
                "Moving Average": {
                    "enabled": True,
                    "type": "SMA",
                    "period": 20,
                    "color": "#ffffff",
                    "width": 1.0,
                    "style": "Dash"
                },
                "Bollinger Bands": {
                    "enabled": False,
                    "period": 20,
                    "std_dev": 2.0,
                    "color": "#8a2be2",
                    "width": 1.0,
                    "fill": True,
                    "fill_alpha": 0.1
                },
                "ATR-EMA Bands": {
                    "enabled": False,
                    "ema_period": 20,
                    "atr_period": 14,
                    "multipliers": [1, 2, 3],
                    "ema_color": "#ffff00",
                    "upper_band_color": "#ff4444",
                    "lower_band_color": "#44ff44",
                    "width": 1.0,
                    "fill": True,
                    "fill_alpha": 0.05
                },
                "RSI": {
                    "enabled": False,
                    "period": 14,
                    "overbought": 70,
                    "oversold": 30,
                    "color": "#ffaa00",
                    "width": 1.0
                },
                "MACD": {
                    "enabled": False,
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9,
                    "macd_color": "#00aaff",
                    "signal_color": "#ff00aa",
                    "histogram_color": "#00ff00",
                    "width": 1.0
                },
                "Stochastic": {
                    "enabled": False,
                    "k_period": 14,
                    "d_period": 3,
                    "slowing": 3,
                    "overbought": 80,
                    "oversold": 20,
                    "k_color": "#00ffff",
                    "d_color": "#ffff00",
                    "width": 1.0
                }
            }

        # Create and show the dialog
        dialog = IndicatorSettingsDialog(self, self.indicator_settings)
        dialog.settings_applied.connect(self.apply_indicator_settings)
        dialog.exec()

    def apply_indicator_settings(self, settings):
        """Apply indicator settings from the dialog"""
        self.indicator_settings = settings

        # Update menu checkboxes
        for name, action in self.indicator_actions.items():
            action.setChecked(settings[name]["enabled"])

        # Force chart update
        self._update_chart()

        # Show confirmation
        self.statusBar().showMessage("Indicator settings applied")

    def toggle_indicator(self, name, enabled):
        """Toggle an indicator on or off"""
        if hasattr(self, 'indicator_settings'):
            self.indicator_settings[name]["enabled"] = enabled

            # Force chart update
            self._update_chart()

            # Show confirmation
            status = "enabled" if enabled else "disabled"
            self.statusBar().showMessage(f"{name} {status}")

    def show_auto_trade_settings(self):
        """Show the auto trade settings dialog"""
        try:
            # Import the dialog class
            from dialogs.auto_trade_settings_dialog import AutoTradeSettingsDialog

            # Load current config
            config = None
            try:
                from config import load_config
                config = load_config()
            except ImportError:
                print("Could not import load_config function")

                # Try to load from checkbox_functions
                if hasattr(checkbox_functions, 'load_config'):
                    config = checkbox_functions.load_config()

            # Create and show the dialog
            dialog = AutoTradeSettingsDialog(self, config)

            # Connect the settings_applied signal
            dialog.settings_applied.connect(self.apply_auto_trade_settings)

            # Show the dialog
            dialog.exec()
        except ImportError:
            print("Error: AutoTradeSettingsDialog not available")
            self.statusBar().showMessage("Auto Trade Settings dialog not available. Please check installation.", 5000)

            # Show a message box with more details
            QMessageBox.warning(
                self,
                "Missing Component",
                "The Auto Trade Settings dialog is not available.\n\n"
                "This could be because:\n"
                "1. The dialogs module is not installed\n"
                "2. The auto_trade_settings_dialog.py file is missing\n\n"
                "Please check that the dialogs directory exists in the application folder."
            )
        except Exception as e:
            print(f"Error showing auto trade settings dialog: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def apply_auto_trade_settings(self, config):
        """Apply auto trade settings from the dialog"""
        try:
            # Update the strategy configuration
            if hasattr(checkbox_functions, 'strategy') and checkbox_functions.strategy:
                checkbox_functions.strategy.update_config(config)
                print("Auto trade settings applied to running strategy")

                # Update the status bar
                self.statusBar().showMessage("Auto trade settings applied", 3000)
        except Exception as e:
            print(f"Error applying auto trade settings: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def toggle_feature(self, feature_name, state):
        """Toggle a trading feature on or off"""
        global epinnox_thread_flag, pnl_perc_thread_flag, auto_transfer_thread_flag, auto_audit_thread_flag, auto_advice_thread_flag

        try:
            if feature_name == "Auto Trade":
                if state:
                    # Start auto trading thread
                    if not epinnox_thread_flag:
                        print("Auto Trading enabled")
                        self.statusBar().showMessage("Auto Trading enabled", 3000)

                        # Get the current symbol and timeframe
                        symbol = self.get_symbol()
                        timeframe = self.tf_selector.currentText() if hasattr(self, 'tf_selector') else "5m"

                        # Get quantity, leverage, and max margin from UI
                        quantity = self.qty_sb.value()
                        leverage = self.lev_sb.value()
                        max_margin = self.max_margin_sb.value()

                        # Define a status callback function to update the UI in a thread-safe way
                        def status_callback(message):
                            print(f"AUTO TRADE: {message}")
                            # Emit the signal to update the UI in the main thread
                            self.status_update_signal.emit(message)

                        # Create a getter function for max margin to always get the current value
                        def max_margin_getter():
                            return self.max_margin_sb.value()

                        # Log the trading parameters
                        print(f"Starting auto trading with quantity: {quantity}, leverage: {leverage}x, max margin: {max_margin}")

                        # Start the auto trading thread
                        epinnox_thread_flag = True
                        checkbox_functions.start_auto_trade_thread(symbol, timeframe, status_callback, quantity, leverage, max_margin_getter)
                else:
                    # Stop auto trading thread
                    epinnox_thread_flag = False
                    checkbox_functions.stop_auto_trade()
                    print("Auto Trading disabled")
                    self.statusBar().showMessage("Auto Trading disabled", 3000)

            elif feature_name == "Audit Pos":
                if state:
                    # Start position auditing thread
                    if not auto_audit_thread_flag:
                        print("Position Auditing enabled")
                        self.statusBar().showMessage("Position Auditing enabled", 3000)

                        # Create getter functions to always get the current values from the UI
                        def tp_getter():
                            return self.tp_sb.value()

                        def sl_getter():
                            return self.sl_sb.value()

                        # Log the initial values for debugging
                        print(f"Starting position audit with initial TP: {tp_getter()}%, SL: {sl_getter()}%")
                        print(f"TP spinbox value: {self.tp_sb.value()}")
                        print(f"SL spinbox value: {self.sl_sb.value()}")

                        # Define a status callback function to update the UI in a thread-safe way
                        def status_callback(message):
                            print(f"STATUS: {message}")
                            # Emit the signal to update the UI in the main thread
                            self.status_update_signal.emit(message)

                        # Start the audit positions thread with getter functions
                        auto_audit_thread_flag = True
                        checkbox_functions.start_audit_positions_thread(tp_getter, sl_getter, status_callback)

                        # Also check current positions for debugging
                        positions = fetch_open_positions(force_refresh=True)
                        if positions:
                            print(f"Found {len(positions)} open positions")
                            for pos in positions:
                                # Use our debug function to check PnL ratio
                                pnl_ratio = checkbox_functions.check_position_pnl_ratio(pos)
                                print(f"Position: {pos.get('symbol')} {pos.get('side')}, PnL ratio: {pnl_ratio:.4f}%")
                                print(f"Will trigger TP at {tp_getter()}%, SL at -{sl_getter()}%")
                        else:
                            print("No open positions found")
                else:
                    # Stop position auditing thread
                    auto_audit_thread_flag = False
                    checkbox_functions.stop_audit_positions()
                    print("Position Auditing disabled")
                    self.statusBar().showMessage("Position Auditing disabled", 3000)

            elif feature_name == "Trend":
                if state:
                    print("Trend Analysis enabled")
                    self.statusBar().showMessage("Trend Analysis enabled", 3000)
                    # Implement trend analysis
                else:
                    print("Trend Analysis disabled")
                    self.statusBar().showMessage("Trend Analysis disabled", 3000)

            elif feature_name == "Advice":
                if state:
                    # Start trading advice thread
                    if not auto_advice_thread_flag:
                        print("Trading Advice enabled")
                        self.statusBar().showMessage("Trading Advice enabled", 3000)
                        # start_thread("auto_advice", trading_advice_function)
                else:
                    # Stop trading advice thread
                    auto_advice_thread_flag = False
                    print("Trading Advice disabled")
                    self.statusBar().showMessage("Trading Advice disabled", 3000)
        except Exception as e:
            print(f"Error toggling feature: {str(e)}")
            print(traceback.format_exc())
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def _update_account(self, force_refresh=False):
        """Update account information in the UI using a worker thread"""
        try:
            # Implement debouncing for account updates
            current_time = datetime.now()

            # Skip if we've updated recently and not forcing refresh
            if not force_refresh and hasattr(self, 'last_account_update') and (current_time - self.last_account_update).total_seconds() < 3.0:
                return  # Skip update if less than 3 seconds have passed and not forcing refresh

            # Skip if an update is already scheduled
            if hasattr(self, '_account_update_scheduled') and self._account_update_scheduled:
                return

            # Skip if a worker is already running
            if hasattr(self, '_account_worker') and self._account_worker.isRunning():
                # Schedule an update for when the current one finishes
                self._account_update_scheduled = True
                self._account_worker.finished.connect(self._on_account_worker_finished)
                return

            # We're good to go - update the timestamp and clear any scheduled flag
            self.last_account_update = current_time
            self._account_update_scheduled = False

            # Create and start the worker thread
            self._account_worker = workers.AccountWorker(force_refresh)
            self._account_worker.fetched.connect(self._populate_account_info)
            self._account_worker.error.connect(self._handle_account_error)
            self._account_worker.start()
        except Exception as e:
            if DEBUG:
                print(f"Error starting account worker: {e}")

    def _on_account_worker_finished(self):
        """Called when an account worker finishes - handles scheduled updates"""
        # If we have a scheduled update pending, trigger it now
        if hasattr(self, '_account_update_scheduled') and self._account_update_scheduled:
            # Use a single-shot timer with a small delay to avoid recursion
            QTimer.singleShot(100, self._update_account)
            self._account_update_scheduled = False

    def _handle_account_error(self, error_msg):
        """Handle errors from the account worker thread"""
        if DEBUG:
            print(f"Account worker error: {error_msg}")
        self.statusBar().showMessage(f"Error updating account: {error_msg}")

        # Update the account status label in the menu bar to show error
        current_text = self.account_status_label.text()
        if "LIVE:" in current_text:
            account_name = current_text.split("LIVE:")[-1].strip()
            self.account_status_label.setText(f"Status: Error | LIVE: {account_name}")
        else:
            self.account_status_label.setText("Status: Error")

        # Set the status color to red
        self.account_status_label.setStyleSheet("color: #ff4444; padding: 0 10px;")

    def _populate_account_info(self, info):
        """Populate the account information with the fetched data"""
        try:
            # Account info labels have been removed - now displayed in the top navigation bar

            # Update the account status label in the menu bar to include equity, free, and status
            # Get the current account name from the account status label
            current_text = self.account_status_label.text()
            account_name = current_text.split("LIVE:")[-1].strip() if "LIVE:" in current_text else ""

            # Update the account status label with equity, free, and status information
            self.account_status_label.setText(f"Equity: {info['equity']} Free: {info['free_balance']} Status: {info['status'].capitalize()} | LIVE: {account_name}")

            # Update status bar with more detailed info
            self.statusBar().showMessage(
                f"Account: {info['equity']} | Used: {info['used_balance']} | Free: {info['free_balance']} | Margin: {info['margin_ratio']:.2%}"
            )

            # Store the full balance data
            self.full_balance_data = info.get('full_balance', {})

            # Update the full balance display in the top bar
            self._update_full_balance_display()

        except Exception as e:
            print(f"Error populating account info: {e}")
            self.statusBar().showMessage(f"Error updating account: {str(e)}")

            # Update the account status label to show error
            current_text = self.account_status_label.text()
            account_name = current_text.split("LIVE:")[-1].strip() if "LIVE:" in current_text else ""
            self.account_status_label.setText(f"Status: Error | LIVE: {account_name}")
            self.account_status_label.setStyleSheet("color: #ff4444; padding: 0 10px;")

    def _update_full_balance_display(self):
        """Update the full balance display in the status bar"""
        try:
            if not hasattr(self, 'full_balance_data') or not self.full_balance_data:
                return

            # Since we no longer have equity_label, we'll update the account_status_label instead
            if not hasattr(self, 'account_status_label') or not self.account_status_label:
                print("Warning: account_status_label not found, cannot update balance display")
                return

            # We'll update the status bar with the balance information

            # Get the balance data
            balance_data = self.full_balance_data
            assets = []

            # Get all assets with non-zero balance
            for asset, total in balance_data.get('total', {}).items():
                if asset != 'USDT' and float(total) > 0:
                    assets.append((asset, float(total)))

            # Sort by value (highest first)
            assets.sort(key=lambda x: x[1], reverse=True)

            # Format the top assets for display in the status bar
            asset_display = ""
            for i, (asset, total) in enumerate(assets[:3]):
                if i > 0:
                    asset_display += " | "

                # Format the balance
                if total < 0.001:
                    asset_display += f"{asset}: {total:.8f}"
                else:
                    asset_display += f"{asset}: {total:.4f}"

            # Update the status bar with the asset information
            if asset_display:
                current_status = self.statusBar().currentMessage()
                if current_status:
                    self.statusBar().showMessage(f"{current_status} | {asset_display}")
                else:
                    self.statusBar().showMessage(asset_display)

        except Exception as e:
            print(f"Error updating full balance display: {e}")

    def show_full_balance(self):
        """Show a dialog with the full spot account balance"""
        try:
            # Create a dialog to display the full balance
            dialog = QDialog(self)
            dialog.setWindowTitle("Full Account Balance")
            dialog.setMinimumWidth(400)
            dialog.setMinimumHeight(300)
            dialog.setStyleSheet("background-color: #1a1a1a; color: #e6e6e6;")

            # Create layout
            layout = QVBoxLayout(dialog)

            # Create a table for the balance
            table = QTableWidget()
            table.setColumnCount(3)
            table.setHorizontalHeaderLabels(["Asset", "Free", "Total"])
            table.horizontalHeader().setStretchLastSection(True)
            table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
            table.setStyleSheet("""
                QTableWidget {
                    background-color: #222;
                    color: #e6e6e6;
                    border: none;
                }
                QHeaderView::section {
                    background-color: #333;
                    color: #00ff44;
                    padding: 5px;
                    border: none;
                }
                QTableWidget::item {
                    padding: 5px;
                }
            """)

            # Fetch the latest balance data
            if hasattr(self, 'full_balance_data') and self.full_balance_data:
                balance_data = self.full_balance_data
            else:
                # Fetch fresh data if we don't have any
                if exchange is not None:
                    balance = exchange.fetch_balance()
                    balance_data = {
                        'free': balance.get('free', {}),
                        'total': balance.get('total', {})
                    }
                else:
                    balance_data = {'free': {}, 'total': {}}

            # Populate the table with balance data
            assets = set(list(balance_data.get('free', {}).keys()) + list(balance_data.get('total', {}).keys()))
            # Filter out assets with zero balance
            assets = [asset for asset in assets if
                     float(balance_data.get('free', {}).get(asset, 0)) > 0 or
                     float(balance_data.get('total', {}).get(asset, 0)) > 0]

            table.setRowCount(len(assets))

            for i, asset in enumerate(sorted(assets)):
                # Asset name
                asset_item = QTableWidgetItem(asset)
                asset_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(i, 0, asset_item)

                # Free balance
                free_val = float(balance_data.get('free', {}).get(asset, 0))
                free_item = QTableWidgetItem(f"{free_val:.8f}".rstrip('0').rstrip('.') if free_val > 0 else "0")
                free_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                table.setItem(i, 1, free_item)

                # Total balance
                total_val = float(balance_data.get('total', {}).get(asset, 0))
                total_item = QTableWidgetItem(f"{total_val:.8f}".rstrip('0').rstrip('.') if total_val > 0 else "0")
                total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                table.setItem(i, 2, total_item)

            layout.addWidget(table)

            # Add a refresh button
            refresh_btn = QPushButton("Refresh")
            refresh_btn.setStyleSheet("""
                QPushButton {
                    background-color: #222;
                    color: #00ff44;
                    border: 1px solid #333;
                    padding: 5px;
                    border-radius: 2px;
                }
                QPushButton:hover {
                    background-color: #333;
                }
            """)

            def refresh_balance():
                # Force a refresh of account data
                self._update_account(force_refresh=True)
                # Close and reopen the dialog to show updated data
                dialog.accept()
                QTimer.singleShot(500, self.show_full_balance)

            refresh_btn.clicked.connect(refresh_balance)
            layout.addWidget(refresh_btn)

            # Show the dialog
            dialog.exec()

        except Exception as e:
            print(f"Error showing full balance: {e}")
            QMessageBox.warning(self, "Error", f"Could not fetch balance data: {str(e)}")

    def update_status_message(self, message):
        """Thread-safe method to update the status bar message"""
        try:
            self.statusBar().showMessage(message, 5000)
        except Exception as e:
            print(f"Error updating status message: {str(e)}")

    def toggle_always_on_top(self, checked):
        """Toggle the always-on-top window flag"""
        try:
            from PySide6.QtCore import Qt
            flags = self.windowFlags()

            if checked:
                # Set the window to be always on top
                self.setWindowFlags(flags | Qt.WindowStaysOnTopHint)
                self.show()  # Need to show again after changing flags
                self.statusBar().showMessage("Always on Top enabled", 3000)
            else:
                # Remove the always on top flag
                self.setWindowFlags(flags & ~Qt.WindowStaysOnTopHint)
                self.show()  # Need to show again after changing flags
                self.statusBar().showMessage("Always on Top disabled", 3000)
        except Exception as e:
            print(f"Error toggling always on top: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def show_performance_settings(self):
        """Show the performance settings dialog"""
        try:
            dialog = SettingsDialog(self)
            if dialog.exec() == QDialog.Accepted:
                # Apply settings when OK is clicked
                dialog.apply_settings()
                self.statusBar().showMessage("Performance settings applied", 3000)
        except Exception as e:
            print(f"Error showing performance settings: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def show_update_speed_settings(self):
        """Show the update speed settings dialog (alias for show_performance_settings)"""
        self.show_performance_settings()

    def show_help(self):
        """Show help information in a dialog and console"""
        # Print to console for reference
        print("Epinnox Trader's Interface - Version 1.0")
        print("A powerful trading interface for cryptocurrency markets.")

        # Create help dialog
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("Epinnox Help")
        help_dialog.setMinimumWidth(600)
        help_dialog.setMinimumHeight(500)

        layout = QVBoxLayout(help_dialog)

        # Title
        title_label = QLabel("Epinnox Trader's Interface - Version 1.0")
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold; color: #00ff44;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Description
        desc_label = QLabel("A powerful trading interface for cryptocurrency markets.")
        desc_label.setStyleSheet("font-size: 11pt; color: #cccccc;")
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)

        # Create a tab widget for different help sections
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabBar::tab { background: #222; padding: 8px; color: #ddd; }
            QTabBar::tab:selected { background: #444; }
        """)

        # Keyboard shortcuts tab
        keyboard_widget = QWidget()
        keyboard_layout = QVBoxLayout(keyboard_widget)
        keyboard_text = """
        <b>B</b> - Quick market buy at current position size
        <b>S</b> - Quick market sell at current position size
        <b>L</b> - Place limit buy order
        <b>K</b> - Place limit sell order
        <b>Esc</b> - Cancel all open orders
        <b>C</b> - Close all positions
        <b>+</b> / <b>-</b> - Increase/decrease quantity
        <b>1-5</b> - Quick select quantity presets
        """
        keyboard_label = QLabel(keyboard_text)
        keyboard_label.setStyleSheet("font-size: 11pt; color: #ffffff;")
        keyboard_label.setTextFormat(Qt.RichText)
        keyboard_layout.addWidget(keyboard_label)
        keyboard_layout.addStretch()

        # Mouse interactions tab
        mouse_widget = QWidget()
        mouse_layout = QVBoxLayout(mouse_widget)
        mouse_text = """
        <h3>Order Book:</h3>
        • <b>Left-click</b> on Ask (left column) - Place a limit buy order at that price
        • <b>Left-click</b> on Bid (right column) - Place a limit sell order at that price
        • <b>Right-click</b> on Order Book - Open context menu to cancel orders

        <h3>Chart:</h3>
        • <b>Left-click</b> on chart - Place a limit BUY (LONG) order at clicked price level
        • <b>Right-click</b> on chart - Place a limit SELL (SHORT) order at clicked price level (no menu)
        • <b>Scroll wheel</b> - Zoom in/out on chart
        • <b>Middle-click drag</b> - Pan chart view

        <h3>Positions Table:</h3>
        • <b>Right-click</b> on position - Open context menu with options:
          - <i>Reverse</i> - Close position and open opposite direction
          - <i>Hedge</i> - Open opposite position with same size
          - <i>Limit Close</i> - Close position with limit order
          - <i>Market Close</i> - Close position immediately at market price
        """
        mouse_label = QLabel(mouse_text)
        mouse_label.setStyleSheet("font-size: 11pt; color: #ffffff;")
        mouse_label.setTextFormat(Qt.RichText)
        mouse_layout.addWidget(mouse_label)
        mouse_layout.addStretch()

        # Order placement tab
        orders_widget = QWidget()
        orders_layout = QVBoxLayout(orders_widget)
        orders_text = """
        <h3>Basic Order Placement:</h3>
        1. Select symbol from dropdown
        2. Set quantity using slider or presets
        3. Set leverage
        4. Optional: Set Stop-Loss and Take-Profit prices
        5. Click Buy/Sell buttons or use keyboard shortcuts

        <h3>Bracket Orders (Entry + SL + TP):</h3>
        1. Set Stop-Loss price in SL field
        2. Set Take-Profit price in TP field
        3. Place a limit or market order - SL and TP will be attached automatically
        """
        orders_label = QLabel(orders_text)
        orders_label.setStyleSheet("font-size: 11pt; color: #ffffff;")
        orders_label.setTextFormat(Qt.RichText)
        orders_layout.addWidget(orders_label)
        orders_layout.addStretch()

        # Add tabs to tab widget
        tabs.addTab(keyboard_widget, "Keyboard Shortcuts")
        tabs.addTab(mouse_widget, "Mouse Interactions")
        tabs.addTab(orders_widget, "Order Placement")

        layout.addWidget(tabs)

        # Close button
        close_button = QPushButton("Close")
        close_button.setStyleSheet("""
            background-color: #333333;
            color: #00ff44;
            padding: 8px;
            font-weight: bold;
        """)
        close_button.clicked.connect(help_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        # Show the dialog
        help_dialog.exec_()

        # Also print to console for reference
        print("\n=== KEYBOARD SHORTCUTS ===")
        print("B - Quick market buy at current position size")
        print("S - Quick market sell at current position size")
        print("L - Place limit buy order")
        print("K - Place limit sell order")
        print("Esc - Cancel all open orders")
        print("C - Close all positions")
        print("+ / - - Increase/decrease quantity")
        print("1-5 - Quick select quantity presets")

        print("\n=== MOUSE INTERACTIONS ===")
        print("Order Book:")
        print("• Left-click on Ask (left column) - Place a limit buy order at that price")
        print("• Left-click on Bid (right column) - Place a limit sell order at that price")
        print("• Right-click on Order Book - Open context menu to cancel orders")

        print("\nChart:")
        print("• Left-click on chart - Place a limit BUY (LONG) order at clicked price level")
        print("• Right-click on chart - Place a limit SELL (SHORT) order at clicked price level (no menu)")
        print("• Scroll wheel - Zoom in/out on chart")
        print("• Middle-click drag - Pan chart view")

        print("\nPositions Table:")
        print("• Right-click on position - Open context menu with options:")
        print("  - Reverse - Close position and open opposite direction")
        print("  - Hedge - Open opposite position with same size")
        print("  - Limit Close - Close position with limit order")
        print("  - Market Close - Close position immediately at market price")

        print("\n=== ORDER PLACEMENT ===")
        print("1. Select symbol from dropdown")
        print("2. Set quantity using slider or presets")
        print("3. Set leverage")
        print("4. Optional: Set Stop-Loss and Take-Profit prices")
        print("5. Click Buy/Sell buttons or use keyboard shortcuts")

        print("\n=== BRACKET ORDERS ===")
        print("To place a bracket order (entry + SL + TP):")
        print("1. Set Stop-Loss price in SL field")
        print("2. Set Take-Profit price in TP field")
        print("3. Place a limit or market order - SL and TP will be attached automatically")

    def configure_bracket_dialog(self):
        """Show dialog to configure bracket orders (stop loss and take profit)"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Configure Bracket Order")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout(dialog)

        # Symbol and side selection
        form_layout = QGridLayout()
        form_layout.addWidget(QLabel("Symbol:"), 0, 0)
        symbol_combo = QComboBox()
        symbol_combo.addItems([self.get_symbol()])
        form_layout.addWidget(symbol_combo, 0, 1)

        form_layout.addWidget(QLabel("Side:"), 1, 0)
        side_combo = QComboBox()
        side_combo.addItems(["Long", "Short"])
        form_layout.addWidget(side_combo, 1, 1)

        form_layout.addWidget(QLabel("Quantity:"), 2, 0)
        qty_spin = QDoubleSpinBox()
        qty_spin.setRange(0.001, 1000)
        qty_spin.setDecimals(4)
        qty_spin.setValue(self.get_quantity())
        form_layout.addWidget(qty_spin, 2, 1)

        form_layout.addWidget(QLabel("Entry Price:"), 3, 0)
        price_spin = QDoubleSpinBox()
        price_spin.setRange(0.001, 1000000)
        price_spin.setDecimals(4)
        # Get current price from order book
        try:
            ob = fetch_order_book(self.get_symbol())
            if ob and ob.get('bids') and len(ob['bids']) > 0:
                price_spin.setValue(ob['bids'][0][0])
        except:
            price_spin.setValue(0)
        form_layout.addWidget(price_spin, 3, 1)

        # Stop Loss and Take Profit
        form_layout.addWidget(QLabel("Stop Loss:"), 4, 0)
        sl_spin = QDoubleSpinBox()
        sl_spin.setRange(0, 1000000)
        sl_spin.setDecimals(4)
        sl_spin.setValue(self.sl_sb.value())
        form_layout.addWidget(sl_spin, 4, 1)

        form_layout.addWidget(QLabel("Take Profit:"), 5, 0)
        tp_spin = QDoubleSpinBox()
        tp_spin.setRange(0, 1000000)
        tp_spin.setDecimals(4)
        tp_spin.setValue(self.tp_sb.value())
        form_layout.addWidget(tp_spin, 5, 1)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QHBoxLayout()
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.reject)
        place_btn = QPushButton("Place Bracket Order")
        place_btn.clicked.connect(dialog.accept)
        place_btn.setStyleSheet("background-color: #006600; color: white;")
        button_box.addWidget(cancel_btn)
        button_box.addWidget(place_btn)
        layout.addLayout(button_box)

        # Execute dialog
        if dialog.exec_() == QDialog.Accepted:
            symbol = symbol_combo.currentText()
            side = side_combo.currentText().lower()
            qty = qty_spin.value()
            price = price_spin.value()
            sl = sl_spin.value()
            tp = tp_spin.value()

            # Place the main order
            order_side = 'buy' if side == 'long' else 'sell'
            main_order = place_limit_order(symbol, order_side, qty, price)

            if main_order:
                # Place stop loss if specified
                if sl > 0:
                    sl_side = 'sell' if side == 'long' else 'buy'
                    sl_price = sl
                    place_limit_order(symbol, sl_side, qty, sl_price, params={'stopLoss': True})

                # Place take profit if specified
                if tp > 0:
                    tp_side = 'sell' if side == 'long' else 'buy'
                    tp_price = tp
                    place_limit_order(symbol, tp_side, qty, tp_price, params={'takeProfit': True})

                print(f"Placed bracket order: {side.upper()} {qty} {symbol} @ {price}")
                if sl > 0:
                    print(f"Stop Loss: {sl}")
                if tp > 0:
                    print(f"Take Profit: {tp}")
                self.statusBar().showMessage(f"Bracket order placed", 3000)

def show_splash(app, gif_path, on_finished, splash_duration=7000):
    """
    Displays a frameless splash with an animated gif, ensuring the animation
    plays fully before calling `on_finished()` to continue startup.
    """
    # Create a container widget for the splash screen
    splash_container = QWidget()
    splash_container.setWindowFlags(Qt.FramelessWindowHint | Qt.SplashScreen)
    splash_container.setAttribute(Qt.WA_TranslucentBackground)

    # Create a layout for the container with smaller margins
    layout = QVBoxLayout(splash_container)
    layout.setContentsMargins(0, 0, 0, 0)  # Remove margins
    layout.setSpacing(0)  # Remove spacing between widgets

    # Create the GIF label
    splash_lbl = QLabel()
    movie = QMovie(gif_path)

    # Set a fixed size for the movie/label to make it smaller
    movie.setScaledSize(QSize(240, 180))  # Even smaller size (4:3 aspect ratio)

    splash_lbl.setMovie(movie)
    splash_lbl.setAlignment(Qt.AlignCenter)
    layout.addWidget(splash_lbl)

    # Create a status label with improved styling
    status_label = QLabel("Initializing...")
    status_label.setAlignment(Qt.AlignCenter)
    status_label.setStyleSheet("""
        color: #00ff44;
        background-color: rgba(0, 0, 0, 180);
        padding: 8px;
        border-radius: 8px;
        font-weight: bold;
        font-size: 10pt;
    """)

    # Add some margin at the bottom
    status_layout = QHBoxLayout()
    status_layout.addWidget(status_label)
    status_layout.setContentsMargins(10, 0, 10, 10)
    layout.addLayout(status_layout)

    # Center the splash screen on the desktop
    desktop = QApplication.primaryScreen().availableGeometry()
    splash_container.adjustSize()  # Make sure the container size is updated
    splash_container.move(
        desktop.width() // 2 - splash_container.width() // 2,
        desktop.height() // 2 - splash_container.height() // 2
    )

    # Variables to track initialization progress
    initialization_completed = False
    animation_timer_triggered = False

    # Create a function to update the status text
    def update_status(text):
        nonlocal initialization_completed
        status_label.setText(text)

        # If text indicates we're done with initialization
        if text.startswith("Loading main application"):
            initialization_completed = True
            check_ready_to_proceed()

        app.processEvents()  # Process events to update the UI

    # Function to check if we're ready to proceed to main window
    def check_ready_to_proceed():
        nonlocal animation_timer_triggered, initialization_completed
        if animation_timer_triggered and initialization_completed:
            _finish_splash(splash_container, movie, on_finished, update_status)

    # Start the movie and show the splash
    splash_container.show()
    movie.start()

    # Set a timer to ensure the animation completes at least once
    # For a 10fps animation with 71 frames, this would be about 7.1 seconds
    # We'll use the splash_duration parameter as a minimum
    def animation_timer():
        nonlocal animation_timer_triggered
        animation_timer_triggered = True
        check_ready_to_proceed()

    QTimer.singleShot(splash_duration, animation_timer)

    # Return the update_status function so it can be called from outside
    return update_status

def initialize_with_status(update_status):
    """Perform initialization tasks with status updates"""
    # Update status messages during initialization
    update_status("Loading credentials...")

    # Initialize exchange and load markets
    update_status("Connecting to exchange...")
    initialize_exchange()

    # Update status based on connection result
    if demo_mode:
        update_status("Running in demo mode (no valid API credentials)...")
    else:
        update_status("Connected to exchange in live mode...")

    # Signal that initialization is complete
    update_status("Loading main application...")

def _finish_splash(splash_container, movie, callback, _):
    """Finish the splash screen and transition to the main application"""
    # Stop the movie
    movie.stop()
    # Call the callback function to initialize the main window
    callback()
    # Close the splash screen
    splash_container.close()

if __name__ == "__main__":

    app = QApplication(sys.argv)

    # First, authenticate the user
    if not secure.authenticate():
        print("Authentication failed or cancelled. Exiting application.")
        sys.exit(1)

    print("Authentication successful. Proceeding to application startup...")

    def startup_main_window():
        # This function is called when both animation and initialization are complete
        print("Starting main application...")

        # Create and show the main window
        win = EpinnoxTraderInterface()

        # Ensure the dashboard is refreshed before showing the window
        if hasattr(win, 'dashboard_tab'):
            print("Refreshing dashboard before showing main window...")
            win.dashboard_tab.refresh_dashboard()

            # Also refresh the performance dashboard if it exists
            if hasattr(win.dashboard_tab, 'performance_dashboard'):
                print("Refreshing performance dashboard...")
                win.dashboard_tab.performance_dashboard.refresh_dashboard()

        # Now show the window
        win.show()

    # Show splash with enough time for animation to complete
    update_status = show_splash(app, "resources/launch.gif", startup_main_window, splash_duration=7000)

    # Perform initialization tasks during splash screen display
    QTimer.singleShot(100, lambda: initialize_with_status(update_status))

    # Start the Qt event loop
    sys.exit(app.exec())
