#!/usr/bin/env python3
"""
Test script to demonstrate LLM request logging
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llama.lmstudio_runner import LMStudioRunner
import logging

# Setup logging to see all details
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_llm_logging():
    """Test LLM request logging with detailed context"""
    
    print("🧪 Testing LLM Request Logging...")
    
    try:
        # Initialize LMStudio runner
        runner = LMStudioRunner()
        
        # Get available models
        models = runner.get_available_models()
        print(f"📋 Available models: {models}")
        
        if not models:
            print("❌ No models available")
            return
        
        # Use the first available model
        current_model = models[0]
        print(f"🎯 Using model: {current_model}")
        
        # Create a test market analysis prompt
        test_prompt = """Analyze the current market conditions for DOGE/USDT:
        
Current Price: 0.167500
ML Predictions: ['LONG', 'SHORT', 'WAIT']
ML Confidences: ['75.2%', '68.9%', '82.1%']

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]"""

        print("\n🚀 Sending test request to LMStudio...")
        print("=" * 80)
        
        # Make the request with detailed logging
        response = runner.run_inference(
            prompt=test_prompt,
            temperature=0.7,
            max_tokens=200
        )
        
        print("=" * 80)
        print(f"✅ Test completed!")
        print(f"📄 Final response: {response}")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_llm_logging()
