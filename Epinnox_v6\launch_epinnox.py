#!/usr/bin/env python3
"""
Epinnox v6 Trading System Launcher
Clean launch script for the integrated trading interface
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 imports
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    print("✓ PyQt5 loaded successfully")
except ImportError:
    print("✗ PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# PyQtGraph for charting
try:
    import pyqtgraph as pg
    print("✓ PyQtGraph loaded successfully")
    # Configure PyQtGraph
    pg.setConfigOptions(useOpenGL=False, antialias=True)
except ImportError:
    print("✗ PyQtGraph not installed. Install with: pip install pyqtgraph")
    sys.exit(1)

# Try to import ccxt for real trading functionality
try:
    import ccxt
    print("✓ CCXT library available")

    # Initialize exchange (HTX/Huobi for demo - you can modify this)
    try:
        # Try to initialize HTX exchange in sandbox mode
        exchange = ccxt.htx({
            'sandbox': True,  # Use sandbox for testing
            'enableRateLimit': True,
        })
        demo_mode = True  # Keep in demo mode for safety
        print("✓ HTX exchange initialized in sandbox mode")
    except Exception as e:
        print(f"⚠ Could not initialize exchange: {e}")
        exchange = None
        demo_mode = True

    # Real trading functions using ccxt
    def place_limit_order(symbol, side, amount, price, params={}):
        """Place limit order using ccxt"""
        if exchange and not demo_mode:
            try:
                order = exchange.create_limit_order(symbol, side, amount, price, params)
                print(f"Real: Placed {side} limit order for {amount} {symbol} at {price}")
                return order
            except Exception as e:
                print(f"Error placing limit order: {e}")
                return None
        else:
            print(f"DEMO: Placed {side} limit order for {amount} {symbol} at {price}")
            return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params={}):
        """Place market order using ccxt"""
        if exchange and not demo_mode:
            try:
                order = exchange.create_market_order(symbol, side, amount, params)
                print(f"Real: Placed {side} market order for {amount} {symbol}")
                return order
            except Exception as e:
                print(f"Error placing market order: {e}")
                return None
        else:
            print(f"DEMO: Placed {side} market order for {amount} {symbol}")
            return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_ohlcv(symbol, timeframe="1m", limit=100):
        """Fetch OHLCV data using ccxt"""
        if exchange:
            try:
                return exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            except Exception as e:
                print(f"Error fetching OHLCV: {e}")
                return generate_mock_ohlcv(limit)
        else:
            return generate_mock_ohlcv(limit)

    def fetch_order_book(symbol):
        """Fetch order book using ccxt"""
        if exchange:
            try:
                return exchange.fetch_order_book(symbol)
            except Exception as e:
                print(f"Error fetching order book: {e}")
                return generate_mock_orderbook()
        else:
            return generate_mock_orderbook()

    def fetch_best_bid(symbol):
        """Fetch best bid using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'bids' in ob and ob['bids']:
                return ob['bids'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best bid: {e}")
            return None

    def fetch_best_ask(symbol):
        """Fetch best ask using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'asks' in ob and ob['asks']:
                return ob['asks'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best ask: {e}")
            return None

    def generate_mock_ohlcv(limit=100):
        """Generate mock OHLCV data"""
        import random
        import time

        data = []
        base_price = 0.35  # DOGE price
        current_time = int(time.time() * 1000)

        for i in range(limit):
            timestamp = current_time - (limit - i) * 60000  # 1 minute intervals
            open_price = base_price + random.uniform(-0.01, 0.01)
            high_price = open_price + random.uniform(0, 0.005)
            low_price = open_price - random.uniform(0, 0.005)
            close_price = open_price + random.uniform(-0.005, 0.005)
            volume = random.uniform(1000, 10000)

            data.append([timestamp, open_price, high_price, low_price, close_price, volume])
            base_price = close_price

        return data

    def generate_mock_orderbook():
        """Generate mock order book"""
        import random
        base_price = 0.35
        return {
            'bids': [[base_price - 0.001, 1000], [base_price - 0.002, 2000]],
            'asks': [[base_price + 0.001, 1000], [base_price + 0.002, 2000]]
        }

    def close_all_positions():
        """Close all positions"""
        print("DEMO: Closing all positions")
        return True

    def cancel_all_orders():
        """Cancel all orders"""
        print("DEMO: Cancelling all orders")
        return True

    def set_leverage(symbol, leverage):
        """Set leverage"""
        print(f"DEMO: Set leverage {leverage}x for {symbol}")
        return True

except ImportError as e:
    print(f"⚠ Could not import trading functions: {e}")
    print("⚠ Using mock implementations")

    # Fallback mock implementations
    def place_limit_order(symbol, side, amount, price, params={}):
        """Mock limit order placement"""
        print(f"MOCK: Placed {side} limit order for {amount} {symbol} at {price}")
        return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params={}):
        """Mock market order placement"""
        print(f"MOCK: Placed {side} market order for {amount} {symbol}")
        return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_ohlcv(symbol, timeframe="1m", limit=100):
        """Mock OHLCV data"""
        import random
        import time

        data = []
        base_price = 0.35  # DOGE price
        current_time = int(time.time() * 1000)

        for i in range(limit):
            timestamp = current_time - (limit - i) * 60000  # 1 minute intervals
            open_price = base_price + random.uniform(-0.01, 0.01)
            high_price = open_price + random.uniform(0, 0.005)
            low_price = open_price - random.uniform(0, 0.005)
            close_price = open_price + random.uniform(-0.005, 0.005)
            volume = random.uniform(1000, 10000)

            data.append([timestamp, open_price, high_price, low_price, close_price, volume])
            base_price = close_price

        return data

    def fetch_order_book(symbol):
        """Mock order book"""
        import random
        base_price = 0.35
        return {
            'bids': [[base_price - 0.001, 1000], [base_price - 0.002, 2000]],
            'asks': [[base_price + 0.001, 1000], [base_price + 0.002, 2000]]
        }

    def fetch_best_bid(symbol):
        """Mock best bid"""
        ob = fetch_order_book(symbol)
        return ob['bids'][0][0] if ob['bids'] else None

    def fetch_best_ask(symbol):
        """Mock best ask"""
        ob = fetch_order_book(symbol)
        return ob['asks'][0][0] if ob['asks'] else None

    def close_all_positions():
        """Mock close all positions"""
        print("MOCK: Closing all positions")
        return True

    def cancel_all_orders():
        """Mock cancel all orders"""
        print("MOCK: Cancelling all orders")
        return True

    def set_leverage(symbol, leverage):
        """Mock set leverage"""
        print(f"MOCK: Set leverage {leverage}x for {symbol}")
        return True

    exchange = None
    demo_mode = True

# Matrix Theme
class MatrixTheme:
    """Matrix-inspired theme colors and styling"""
    
    # Colors
    BLACK = "#000000"
    BACKGROUND = "#000000"
    GREEN = "#00FF00"
    TEXT = "#00FF00"
    DARK_GREEN = "#003300"
    LIGHT_GREEN = "#00CC00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    WHITE = "#FFFFFF"
    GRAY = "#666666"
    GRAY = "#808080"
    
    # Font sizes
    FONT_SIZE_SMALL = 10
    FONT_SIZE_MEDIUM = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: 'Courier New', monospace;
            font-size: {cls.FONT_SIZE_MEDIUM}px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {cls.DARK_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.GREEN};
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
            font-weight: bold;
        }}
        
        QGroupBox {{
            border: 2px solid {cls.DARK_GREEN};
            border-radius: 5px;
            margin: 5px;
            padding-top: 10px;
            color: {cls.GREEN};
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {cls.LIGHT_GREEN};
        }}
        
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
            padding: 8px 16px;
            font-weight: bold;
            border-radius: 3px;
        }}
        
        QPushButton:hover {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.LIGHT_GREEN};
            color: {cls.BLACK};
        }}
        
        QLabel {{
            color: {cls.GREEN};
            background-color: transparent;
        }}
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            font-family: 'Courier New', monospace;
        }}
        
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.DARK_GREEN};
            color: {cls.LIGHT_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 5px;
            border: 1px solid {cls.GREEN};
            font-weight: bold;
        }}
        
        QStatusBar {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-top: 1px solid {cls.DARK_GREEN};
        }}
        
        QCheckBox {{
            color: {cls.GREEN};
            spacing: 5px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
        }}
        
        QCheckBox::indicator:unchecked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.GREEN};
        }}
        """

# Simple Trading Interface
class EpinnoxTradingInterface(QMainWindow):
    """Simplified Epinnox Trading Interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(100, 100, 1200, 800)

        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())

        # Analysis control
        self.analysis_timer = None
        self.is_analyzing = False

        self.setup_ui()
        self.setup_timers()

        print("✓ Epinnox Trading Interface initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = self.create_header()
        layout.addLayout(header_layout)
        
        # Main content
        content_layout = self.create_content()
        layout.addLayout(content_layout)
        
        # Status bar
        self.statusBar().showMessage("Epinnox v6 System Ready")
    
    def create_header(self):
        """Create header section"""
        layout = QHBoxLayout()
        
        # Title
        title = QLabel("EPINNOX v6 TRADING SYSTEM")
        title.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_XLARGE}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 10px;
        """)
        
        # Status
        self.status_label = QLabel("SYSTEM: READY")
        self.status_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
        """)
        
        # Time
        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            color: {MatrixTheme.GREEN};
        """)
        
        layout.addWidget(title, 2)
        layout.addWidget(self.status_label, 1)
        layout.addWidget(self.time_label, 1)
        
        return layout
    
    def create_content(self):
        """Create main content area with comprehensive panels"""
        layout = QHBoxLayout()

        # Left column - Controls and Analysis
        left_column = QVBoxLayout()

        # Symbol selection and controls
        symbol_panel = self.create_symbol_panel()
        left_column.addWidget(symbol_panel)

        # Current analysis
        current_analysis_panel = self.create_current_analysis_panel()
        left_column.addWidget(current_analysis_panel)

        # Risk Warnings (moved to prominent position)
        risk_warnings_panel = self.create_risk_warnings_panel()
        left_column.addWidget(risk_warnings_panel)

        # LLM Analysis
        llm_panel = self.create_llm_analysis_panel()
        left_column.addWidget(llm_panel)

        # Leverage Analysis
        leverage_panel = self.create_leverage_panel()
        left_column.addWidget(leverage_panel)

        left_widget = QWidget()
        left_widget.setLayout(left_column)
        layout.addWidget(left_widget, 1)

        # Middle column - Analysis Results
        middle_column = QVBoxLayout()

        # Signal Hierarchy Analysis
        signal_hierarchy_panel = self.create_signal_hierarchy_panel()
        middle_column.addWidget(signal_hierarchy_panel)

        # Market Analysis
        market_analysis_panel = self.create_market_analysis_panel()
        middle_column.addWidget(market_analysis_panel)

        # Analysis Log
        analysis_log_panel = self.create_analysis_log_panel()
        middle_column.addWidget(analysis_log_panel)

        # ML Models Status (moved from left column)
        ml_models_panel = self.create_ml_models_panel()
        middle_column.addWidget(ml_models_panel)

        middle_widget = QWidget()
        middle_widget.setLayout(middle_column)
        layout.addWidget(middle_widget, 2)

        # Right column - Chart and Manual Trading
        right_column = QVBoxLayout()

        # Manual Trading Controls
        trading_panel = self.create_manual_trading_panel()
        right_column.addWidget(trading_panel)

        # Chart Panel
        chart_panel = self.create_chart_panel()
        right_column.addWidget(chart_panel)

        right_widget = QWidget()
        right_widget.setLayout(right_column)
        layout.addWidget(right_widget, 3)

        return layout
    
    def create_symbol_panel(self):
        """Create symbol selection panel"""
        group = QGroupBox("Symbol Selection")
        layout = QVBoxLayout(group)

        # Trading Symbol
        symbol_layout = QHBoxLayout()
        symbol_layout.addWidget(QLabel("Trading Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT"])
        symbol_layout.addWidget(self.symbol_combo)
        layout.addLayout(symbol_layout)

        # Checkboxes
        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.live_data_checkbox.setChecked(True)
        layout.addWidget(self.live_data_checkbox)

        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (30s)")
        self.auto_refresh_checkbox.setChecked(True)
        layout.addWidget(self.auto_refresh_checkbox)

        # Buttons
        self.analyze_button = QPushButton("ANALYZE SYMBOL")
        self.analyze_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.analyze_button)

        self.stop_button = QPushButton("STOP ANALYSIS")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_analysis)
        layout.addWidget(self.stop_button)

        return group

    def create_current_analysis_panel(self):
        """Create current analysis panel"""
        group = QGroupBox("Current Analysis")
        layout = QVBoxLayout(group)

        self.decision_label = QLabel("Decision: WAIT")
        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.decision_label)

        self.confidence_label = QLabel("Confidence: 86.0%")
        layout.addWidget(self.confidence_label)

        self.last_update_label = QLabel("Last Update: 19:32:58")
        layout.addWidget(self.last_update_label)

        return group

    def create_ml_models_panel(self):
        """Create ML models status panel"""
        group = QGroupBox("ML Models Status")
        layout = QVBoxLayout(group)

        # Create table
        self.ml_models_table = QTableWidget(3, 3)
        # ─ Stretch columns ─
        header = self.ml_models_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # ─ Transparent viewport & dark scrollbar ─
        self.ml_models_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.ml_models_table.setHorizontalHeaderLabels(["Model", "Decision", "Confidence"])
        self.ml_models_table.verticalHeader().setVisible(False)
        self.ml_models_table.setMaximumHeight(120)

        # Add sample data
        models_data = [
            ["SVM", "WAIT", "58.2%"],
            ["Random Forest", "LONG", "72.1%"],
            ["LSTM", "WAIT", "61.4%"]
        ]

        for row, (model, decision, confidence) in enumerate(models_data):
            self.ml_models_table.setItem(row, 0, QTableWidgetItem(model))

            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)

            self.ml_models_table.setItem(row, 2, QTableWidgetItem(confidence))

        layout.addWidget(self.ml_models_table)

        return group

    def create_llm_analysis_panel(self):
        """Create LLM analysis panel"""
        group = QGroupBox("LLM Analysis")
        layout = QVBoxLayout(group)

        # LLM Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Model:"))
        self.llm_model_combo = QComboBox()
        self.llm_model_combo.addItems(["Phi-3.5-mini", "LLaMA-3-8B", "GPT-4o-mini"])
        model_layout.addWidget(self.llm_model_combo)
        layout.addLayout(model_layout)

        # LLM Decision
        self.llm_decision_label = QLabel("LLM Decision: WAIT")
        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.llm_decision_label)

        # LLM Confidence
        self.llm_confidence_label = QLabel("LLM Confidence: 75.0%")
        layout.addWidget(self.llm_confidence_label)

        # LLM Reasoning (scrollable)
        reasoning_label = QLabel("LLM Reasoning:")
        layout.addWidget(reasoning_label)

        self.llm_reasoning_text = QTextEdit()
        self.llm_reasoning_text.setReadOnly(True)
        self.llm_reasoning_text.setMaximumHeight(80)
        self.llm_reasoning_text.setText("Market shows mixed signals. Technical indicators suggest consolidation while volume patterns indicate potential breakout. Recommend WAIT for clearer direction.")
        layout.addWidget(self.llm_reasoning_text)

        return group

    def create_leverage_panel(self):
        """Create leverage analysis panel"""
        group = QGroupBox("Leverage Analysis")
        layout = QVBoxLayout(group)

        self.max_leverage_label = QLabel("Max Available: 1.0x")
        layout.addWidget(self.max_leverage_label)

        self.recommended_leverage_label = QLabel("Recommended: 1.0x")
        layout.addWidget(self.recommended_leverage_label)

        self.effective_leverage_label = QLabel("Effective: 0.4x")
        layout.addWidget(self.effective_leverage_label)

        self.position_size_label = QLabel("Position Size: 0.00 units ($0.00)")
        layout.addWidget(self.position_size_label)

        self.risk_per_trade_label = QLabel("Risk per Trade: $0.00")
        layout.addWidget(self.risk_per_trade_label)

        return group
    
    def create_signal_hierarchy_panel(self):
        """Create signal hierarchy analysis panel"""
        group = QGroupBox("Signal Hierarchy Analysis")
        layout = QVBoxLayout(group)

        # Create table
        self.signal_hierarchy_table = QTableWidget(4, 4)
        # ─ Stretch columns to fill the box ─
        header = self.signal_hierarchy_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # ─ Make viewport & scrollbars dark/transparent ─
        self.signal_hierarchy_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.signal_hierarchy_table.setHorizontalHeaderLabels(["Source", "Decision", "Confidence", "Weight"])
        self.signal_hierarchy_table.verticalHeader().setVisible(False)
        self.signal_hierarchy_table.setMaximumHeight(180)

        # Add sample data
        signals_data = [
            ["ml_ensemble", "WAIT", "68.7%", "0.3"],
            ["llm_analysis", "WAIT", "75.0%", "0.3"],
            ["technical_signals", "WAIT", "83.0%", "0.2"],
            ["multi_timeframe", "SHORT", "0.0%", "0.2"]
        ]

        for row, (source, decision, confidence, weight) in enumerate(signals_data):
            self.signal_hierarchy_table.setItem(row, 0, QTableWidgetItem(source))

            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.signal_hierarchy_table.setItem(row, 1, decision_item)

            self.signal_hierarchy_table.setItem(row, 2, QTableWidgetItem(confidence))
            self.signal_hierarchy_table.setItem(row, 3, QTableWidgetItem(weight))

        layout.addWidget(self.signal_hierarchy_table)

        return group

    def create_market_analysis_panel(self):
        """Create market analysis panel"""
        group = QGroupBox("Market Analysis")
        layout = QVBoxLayout(group)

        self.market_regime_label = QLabel("Market Regime: STRONG_TREND")
        layout.addWidget(self.market_regime_label)

        self.trend_strength_label = QLabel("Trend Strength: 0.00")
        layout.addWidget(self.trend_strength_label)

        self.volatility_label = QLabel("Volatility: 0.00%")
        layout.addWidget(self.volatility_label)

        self.liquidity_score_label = QLabel("Liquidity Score: --")
        layout.addWidget(self.liquidity_score_label)

        return group

    def create_analysis_log_panel(self):
        """Create analysis log panel"""
        group = QGroupBox("Analysis Log")
        layout = QVBoxLayout(group)

        self.analysis_log = QTextEdit()
        self.analysis_log.setReadOnly(True)
        self.analysis_log.setMaximumHeight(200)

        # Add sample log entries
        sample_logs = [
            "[19:20:02] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:20:19] Started analysis for DOGE/USDT:USDT",
            "[19:20:43] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:20:56] Started analysis for DOGE/USDT:USDT",
            "[19:21:00] Analysis complete for DOGE/USDT:USDT: LONG",
            "[19:21:17] Started analysis for DOGE/USDT:USDT",
            "[19:21:31] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:21:45] Started analysis for DOGE/USDT:USDT",
            "[19:21:56] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:22:11] Started analysis for DOGE/USDT:USDT",
            "[19:22:31] Analysis complete for DOGE/USDT:USDT: LONG",
            "[19:22:45] Started analysis for DOGE/USDT:USDT",
            "[19:23:01] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:23:16] Started analysis for DOGE/USDT:USDT"
        ]

        for log_entry in sample_logs:
            self.analysis_log.append(log_entry)

        layout.addWidget(self.analysis_log)

        return group

    def create_risk_warnings_panel(self):
        """Create comprehensive risk warnings panel"""
        group = QGroupBox("Risk Management & Warnings")
        layout = QVBoxLayout(group)

        # Risk Metrics
        metrics_layout = QGridLayout()

        # Row 1: Portfolio Risk
        metrics_layout.addWidget(QLabel("Portfolio Risk:"), 0, 0)
        self.portfolio_risk_label = QLabel("2.5%")
        self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.portfolio_risk_label, 0, 1)

        # Row 2: Max Drawdown
        metrics_layout.addWidget(QLabel("Max Drawdown:"), 1, 0)
        self.max_drawdown_label = QLabel("5.2%")
        self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.max_drawdown_label, 1, 1)

        # Row 3: Correlation Risk
        metrics_layout.addWidget(QLabel("Correlation Risk:"), 2, 0)
        self.correlation_risk_label = QLabel("LOW")
        self.correlation_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.correlation_risk_label, 2, 1)

        # Row 4: Liquidity Risk
        metrics_layout.addWidget(QLabel("Liquidity Risk:"), 3, 0)
        self.liquidity_risk_label = QLabel("MEDIUM")
        self.liquidity_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.liquidity_risk_label, 3, 1)

        layout.addLayout(metrics_layout)

        # Active Warnings
        warnings_label = QLabel("Active Warnings:")
        warnings_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        layout.addWidget(warnings_label)

        self.risk_warnings_log = QTextEdit()
        self.risk_warnings_log.setReadOnly(True)
        self.risk_warnings_log.setMaximumHeight(100)

        # Add comprehensive sample warnings
        sample_warnings = [
            "⚠️ HIGH VOLATILITY: 24h volatility >5% - reduce position size",
            "⚠️ LOW LIQUIDITY: Order book depth <$50k - limit order size",
            "⚠️ CORRELATION ALERT: 0.85 correlation with BTC - diversify",
            "⚠️ LEVERAGE WARNING: Current 3.2x exceeds recommended 2.5x"
        ]

        for warning in sample_warnings:
            self.risk_warnings_log.append(warning)

        layout.addWidget(self.risk_warnings_log)

        return group

    def create_manual_trading_panel(self):
        """Create manual trading controls panel"""
        group = QGroupBox("Manual Trading")
        layout = QVBoxLayout(group)

        # Trading parameters
        params_layout = QGridLayout()

        # Quantity
        params_layout.addWidget(QLabel("Quantity:"), 0, 0)
        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setRange(0.0001, 100000)
        self.quantity_spinbox.setDecimals(4)
        self.quantity_spinbox.setValue(50.0)
        params_layout.addWidget(self.quantity_spinbox, 0, 1)

        # Leverage
        params_layout.addWidget(QLabel("Leverage:"), 1, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 125)
        self.leverage_spinbox.setValue(20)
        params_layout.addWidget(self.leverage_spinbox, 1, 1)

        # Best Bid/Ask display (read-only)
        params_layout.addWidget(QLabel("Best Bid:"), 2, 0)
        self.best_bid_label = QLabel("--")
        self.best_bid_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        params_layout.addWidget(self.best_bid_label, 2, 1)

        params_layout.addWidget(QLabel("Best Ask:"), 3, 0)
        self.best_ask_label = QLabel("--")
        self.best_ask_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        params_layout.addWidget(self.best_ask_label, 3, 1)

        # Trading mode status
        params_layout.addWidget(QLabel("Mode:"), 4, 0)
        mode_text = "DEMO" if demo_mode else "LIVE"
        mode_color = MatrixTheme.YELLOW if demo_mode else MatrixTheme.RED
        self.mode_label = QLabel(mode_text)
        self.mode_label.setStyleSheet(f"color: {mode_color}; font-weight: bold; font-size: 12px;")
        params_layout.addWidget(self.mode_label, 4, 1)

        layout.addLayout(params_layout)

        # Trading buttons
        buttons_layout = QGridLayout()

        # Long buttons (green)
        self.limit_long_btn = QPushButton("LIMIT LONG")
        self.limit_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #006600;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GREEN};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.limit_long_btn.clicked.connect(self.place_limit_long)
        buttons_layout.addWidget(self.limit_long_btn, 0, 0)

        self.market_long_btn = QPushButton("MARKET LONG")
        self.market_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #009900;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GREEN};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.market_long_btn.clicked.connect(self.place_market_long)
        buttons_layout.addWidget(self.market_long_btn, 0, 1)

        # Short buttons (red)
        self.limit_short_btn = QPushButton("LIMIT SHORT")
        self.limit_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #660000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.limit_short_btn.clicked.connect(self.place_limit_short)
        buttons_layout.addWidget(self.limit_short_btn, 1, 0)

        self.market_short_btn = QPushButton("MARKET SHORT")
        self.market_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #990000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.market_short_btn.clicked.connect(self.place_market_short)
        buttons_layout.addWidget(self.market_short_btn, 1, 1)

        # Control buttons
        self.close_all_btn = QPushButton("CLOSE ALL")
        self.close_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #ff6600;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.YELLOW};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.YELLOW};
                color: black;
            }}
        """)
        self.close_all_btn.clicked.connect(self.close_all_positions)
        buttons_layout.addWidget(self.close_all_btn, 2, 0)

        self.cancel_all_btn = QPushButton("CANCEL ALL")
        self.cancel_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #666666;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GRAY};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GRAY};
                color: white;
            }}
        """)
        self.cancel_all_btn.clicked.connect(self.cancel_all_orders)
        buttons_layout.addWidget(self.cancel_all_btn, 2, 1)

        layout.addLayout(buttons_layout)

        return group

    def create_chart_panel(self):
        """Create interactive chart panel with live data integration"""
        group = QGroupBox("Live Chart")
        layout = QVBoxLayout(group)

        # Import and create live chart widget
        try:
            from gui.charts.live_chart_widget import LiveChartWidget

            # Create live chart widget
            self.live_chart = LiveChartWidget()
            self.live_chart.setMinimumHeight(400)

            # Connect chart signals
            self.live_chart.chart_clicked.connect(self.on_live_chart_click)
            self.live_chart.symbol_changed.connect(self.on_chart_symbol_changed)
            self.live_chart.timeframe_changed.connect(self.on_chart_timeframe_changed)

            layout.addWidget(self.live_chart)

            # Initialize live data manager
            self.setup_live_data_manager()

            # Initialize real trading interface
            self.setup_real_trading_interface()

            # Initialize signal trading engine
            self.setup_signal_trading_engine()

            # Initialize session management
            self.setup_session_management()

        except ImportError as e:
            print(f"Could not import LiveChartWidget: {e}")
            # Fallback to simple chart
            layout.addWidget(self.create_fallback_chart())

        return group

    def create_fallback_chart(self):
        """Create fallback chart if live chart widget is not available"""
        # Chart controls
        controls_layout = QHBoxLayout()

        # Timeframe selector
        controls_layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText("1m")
        self.timeframe_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.timeframe_combo)

        # Chart type selector
        controls_layout.addWidget(QLabel("Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Line", "Candlestick"])
        self.chart_type_combo.setCurrentText("Line")
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.chart_type_combo)

        controls_layout.addStretch()

        fallback_widget = QWidget()
        fallback_layout = QVBoxLayout(fallback_widget)
        fallback_layout.addLayout(controls_layout)

        # Create PyQtGraph chart
        self.chart_widget = pg.PlotWidget(
            background='#000000',
            enableMenu=False
        )
        self.chart_widget.setMinimumHeight(300)
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.chart_widget.setLabel('left', 'Price', color=MatrixTheme.GREEN)
        self.chart_widget.setLabel('bottom', 'Time', color=MatrixTheme.GREEN)

        # Connect chart click event for order placement
        self.chart_widget.scene().sigMouseClicked.connect(self.on_chart_click)

        fallback_layout.addWidget(self.chart_widget)

        # Chart instructions
        instructions = QLabel("💡 Left-click: BUY order | Right-click: SELL order")
        instructions.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: 10px; padding: 5px;")
        fallback_layout.addWidget(instructions)

        # Initialize chart with sample data
        self.update_chart()

        return fallback_widget

    def setup_live_data_manager(self):
        """Setup live data manager for real-time chart updates"""
        try:
            from data.live_data_manager import LiveDataManager

            # Create live data manager
            self.live_data_manager = LiveDataManager("htx")

            # Connect signals
            self.live_data_manager.chart_data_updated.connect(self.on_live_chart_data_updated)
            self.live_data_manager.price_updated.connect(self.on_live_price_updated)
            self.live_data_manager.orderbook_updated.connect(self.on_live_orderbook_updated)
            self.live_data_manager.connection_status_changed.connect(self.on_live_connection_status)

            # Subscribe to current symbol
            current_symbol = self.symbol_combo.currentText()
            self.live_data_manager.subscribe_symbol(current_symbol, ["1m", "5m", "15m"])

            # Connect to live data
            self.live_data_manager.connect()

            print("✓ Live data manager initialized")

        except ImportError as e:
            print(f"Could not import LiveDataManager: {e}")
            self.live_data_manager = None

    def setup_real_trading_interface(self):
        """Setup real trading interface for actual order execution"""
        try:
            from trading.real_trading_interface import RealTradingInterface

            # Create real trading interface
            self.real_trading = RealTradingInterface("htx", demo_mode=True)  # Start in demo mode for safety

            # Connect signals
            self.real_trading.order_status_updated.connect(self.on_order_status_updated)
            self.real_trading.position_status_updated.connect(self.on_position_status_updated)
            self.real_trading.balance_status_updated.connect(self.on_balance_status_updated)
            self.real_trading.trading_error.connect(self.on_trading_error)
            self.real_trading.trading_status.connect(self.on_trading_status)
            self.real_trading.pnl_updated.connect(self.on_pnl_updated)
            self.real_trading.risk_warning.connect(self.on_risk_warning)

            # Set current symbol
            current_symbol = self.symbol_combo.currentText()
            self.real_trading.set_current_symbol(current_symbol)

            print("✓ Real trading interface initialized")

        except ImportError as e:
            print(f"Could not import RealTradingInterface: {e}")
            self.real_trading = None

    def setup_signal_trading_engine(self):
        """Setup signal trading engine for automated trading"""
        try:
            from trading.signal_trading_engine import SignalTradingEngine

            if hasattr(self, 'real_trading') and self.real_trading:
                # Create signal trading engine
                self.signal_trading = SignalTradingEngine(self.real_trading)

                # Connect signals
                self.signal_trading.signal_received.connect(self.on_signal_received)
                self.signal_trading.trade_decision_made.connect(self.on_trade_decision_made)
                self.signal_trading.automated_trade_executed.connect(self.on_automated_trade_executed)
                self.signal_trading.risk_limit_triggered.connect(self.on_risk_limit_triggered)
                self.signal_trading.engine_status_changed.connect(self.on_engine_status_changed)

                print("✓ Signal trading engine initialized")
            else:
                print("⚠ Real trading interface not available for signal trading")
                self.signal_trading = None

        except ImportError as e:
            print(f"Could not import SignalTradingEngine: {e}")
            self.signal_trading = None

    def setup_session_management(self):
        """Setup session management and persistence"""
        try:
            from storage.database_manager import DatabaseManager
            from storage.session_manager import SessionManager, TradeRecorder

            # Create database manager
            self.db_manager = DatabaseManager()

            # Create session manager
            self.session_manager = SessionManager(self.db_manager)

            # Create trade recorder
            self.trade_recorder = TradeRecorder(self.session_manager)

            # Connect signals
            self.session_manager.session_started.connect(self.on_session_started)
            self.session_manager.session_ended.connect(self.on_session_ended)
            self.session_manager.trade_recorded.connect(self.on_trade_recorded_to_db)
            self.session_manager.signal_recorded.connect(self.on_signal_recorded_to_db)

            # Start a session automatically
            current_symbol = self.symbol_combo.currentText()
            session_id = self.session_manager.start_session(
                mode="demo",
                symbol=current_symbol,
                initial_balance=1000.0,
                configuration={
                    "leverage": self.leverage_spinbox.value(),
                    "base_position_size": self.quantity_spinbox.value(),
                    "auto_trading": False
                }
            )

            print("✓ Session management initialized")
            print(f"✓ Started session: {session_id}")

        except ImportError as e:
            print(f"Could not import session management: {e}")
            self.session_manager = None
            self.trade_recorder = None

    def setup_timers(self):
        """Setup update timers"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

        # Chart update timer
        self.chart_timer = QTimer()
        self.chart_timer.timeout.connect(self.update_chart)
        self.chart_timer.start(5000)  # Update chart every 5 seconds

        # Bid/Ask update timer (more frequent for real-time trading)
        self.bid_ask_timer = QTimer()
        self.bid_ask_timer.timeout.connect(self.update_bid_ask_display)
        self.bid_ask_timer.start(1000)  # Update bid/ask every 1 second
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def start_analysis(self):
        """Start trading analysis"""
        symbol = self.symbol_combo.currentText()
        use_live = self.live_data_checkbox.isChecked()

        self.is_analyzing = True
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("SYSTEM: ANALYZING")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")

        self.log_message(f"Starting analysis for {symbol} (Live: {use_live})")

        # Simulate analysis
        QTimer.singleShot(3000, self.complete_analysis)

    def stop_analysis(self):
        """Stop analysis"""
        self.is_analyzing = False

        # Stop any pending analysis timer
        if self.analysis_timer:
            self.analysis_timer.stop()
            self.analysis_timer = None

        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("SYSTEM: READY")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        self.log_message("Analysis stopped by user")
    
    def complete_analysis(self):
        """Complete analysis simulation"""
        import random

        decisions = ["LONG", "SHORT", "WAIT"]

        # Generate individual ML model decisions
        ml_decisions = [random.choice(decisions) for _ in range(3)]
        ml_confidences = [random.uniform(50, 85) for _ in range(3)]

        # Generate LLM decision
        llm_decision = random.choice(decisions)
        llm_confidence = random.uniform(65, 85)

        # Weighted decision making (ML ensemble: 30%, LLM: 30%, Technical: 20%, Multi-timeframe: 20%)
        decision_scores = {"LONG": 0, "SHORT": 0, "WAIT": 0}

        # ML ensemble contribution (30%)
        ml_ensemble_decision = max(set(ml_decisions), key=ml_decisions.count)  # Majority vote
        decision_scores[ml_ensemble_decision] += 0.3

        # LLM contribution (30%)
        decision_scores[llm_decision] += 0.3

        # Technical signals (20%)
        tech_decision = random.choice(decisions)
        decision_scores[tech_decision] += 0.2

        # Multi-timeframe (20%)
        mtf_decision = random.choice(decisions)
        decision_scores[mtf_decision] += 0.2

        # Final decision is the highest scored
        decision = max(decision_scores, key=decision_scores.get)
        confidence = (decision_scores[decision] * 100) + random.uniform(-10, 10)  # Add some noise
        confidence = max(50, min(95, confidence))  # Clamp between 50-95%

        # Update current analysis panel
        self.decision_label.setText(f"Decision: {decision}")
        self.confidence_label.setText(f"Confidence: {confidence:.1f}%")
        self.last_update_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

        # Color code decision
        if decision == "LONG":
            color = MatrixTheme.GREEN
        elif decision == "SHORT":
            color = MatrixTheme.RED
        else:
            color = MatrixTheme.YELLOW

        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {color};
            padding: 5px;
        """)

        # Update ML models with generated decisions
        models_data = [
            ["SVM", ml_decisions[0], f"{ml_confidences[0]:.1f}%"],
            ["Random Forest", ml_decisions[1], f"{ml_confidences[1]:.1f}%"],
            ["LSTM", ml_decisions[2], f"{ml_confidences[2]:.1f}%"]
        ]

        for row, (model, ml_decision, ml_confidence) in enumerate(models_data):
            decision_item = QTableWidgetItem(ml_decision)
            if ml_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif ml_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)
            self.ml_models_table.setItem(row, 2, QTableWidgetItem(ml_confidence))

        # Generate LLM analysis
        llm_decision = random.choice(decisions)
        llm_confidence = random.uniform(65, 85)
        llm_reasonings = [
            "Technical indicators show bullish divergence with strong volume support. RSI oversold suggests potential reversal.",
            "Market sentiment appears bearish with declining volume. Multiple resistance levels suggest downward pressure.",
            "Mixed signals detected. Consolidation pattern forming with unclear directional bias. Await breakout confirmation.",
            "Strong momentum indicators align with price action. Volume profile supports current trend continuation.",
            "Risk-off sentiment dominates. Correlation with broader market suggests defensive positioning appropriate."
        ]
        llm_reasoning = random.choice(llm_reasonings)

        # Update LLM panel
        self.llm_decision_label.setText(f"LLM Decision: {llm_decision}")
        if llm_decision == "LONG":
            llm_color = MatrixTheme.GREEN
        elif llm_decision == "SHORT":
            llm_color = MatrixTheme.RED
        else:
            llm_color = MatrixTheme.YELLOW

        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {llm_color};
            padding: 5px;
        """)

        self.llm_confidence_label.setText(f"LLM Confidence: {llm_confidence:.1f}%")
        self.llm_reasoning_text.setText(llm_reasoning)

        # Update signal hierarchy (now includes LLM)
        signals_data = [
            ["ml_ensemble", ml_ensemble_decision, f"{sum(ml_confidences)/3:.1f}%", "0.3"],
            ["llm_analysis", llm_decision, f"{llm_confidence:.1f}%", "0.3"],
            ["technical_signals", tech_decision, f"{random.uniform(70, 90):.1f}%", "0.2"],
            ["multi_timeframe", mtf_decision, f"{random.uniform(40, 80):.1f}%", "0.2"]
        ]

        for row, (source, sig_decision, sig_confidence, weight) in enumerate(signals_data):
            decision_item = QTableWidgetItem(sig_decision)
            if sig_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif sig_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.signal_hierarchy_table.setItem(row, 1, decision_item)
            self.signal_hierarchy_table.setItem(row, 2, QTableWidgetItem(sig_confidence))
            self.signal_hierarchy_table.setItem(row, 3, QTableWidgetItem(weight))

        # Update market analysis
        trend_strength = random.uniform(0, 1)
        volatility = random.uniform(0, 5)
        self.trend_strength_label.setText(f"Trend Strength: {trend_strength:.2f}")
        self.volatility_label.setText(f"Volatility: {volatility:.2f}%")

        # Update leverage analysis
        max_leverage = random.uniform(1, 5)
        recommended_leverage = max_leverage * 0.7
        effective_leverage = recommended_leverage * 0.6
        position_size = random.uniform(100, 1000)
        risk_per_trade = position_size * 0.02

        self.max_leverage_label.setText(f"Max Available: {max_leverage:.1f}x")
        self.recommended_leverage_label.setText(f"Recommended: {recommended_leverage:.1f}x")
        self.effective_leverage_label.setText(f"Effective: {effective_leverage:.1f}x")
        self.position_size_label.setText(f"Position Size: {position_size:.2f} units (${position_size:.2f})")
        self.risk_per_trade_label.setText(f"Risk per Trade: ${risk_per_trade:.2f}")

        # Update risk metrics
        portfolio_risk = random.uniform(1, 8)
        max_drawdown = random.uniform(2, 15)
        correlation = random.uniform(0.1, 0.9)
        liquidity_score = random.uniform(0.1, 1.0)

        # Update portfolio risk
        self.portfolio_risk_label.setText(f"{portfolio_risk:.1f}%")
        if portfolio_risk > 5:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif portfolio_risk > 3:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update max drawdown
        self.max_drawdown_label.setText(f"{max_drawdown:.1f}%")
        if max_drawdown > 10:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif max_drawdown > 5:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update correlation risk
        if correlation > 0.7:
            corr_text, corr_color = "HIGH", MatrixTheme.RED
        elif correlation > 0.4:
            corr_text, corr_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            corr_text, corr_color = "LOW", MatrixTheme.GREEN

        self.correlation_risk_label.setText(corr_text)
        self.correlation_risk_label.setStyleSheet(f"color: {corr_color}; font-weight: bold;")

        # Update liquidity risk
        if liquidity_score < 0.3:
            liq_text, liq_color = "HIGH", MatrixTheme.RED
        elif liquidity_score < 0.6:
            liq_text, liq_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            liq_text, liq_color = "LOW", MatrixTheme.GREEN

        self.liquidity_risk_label.setText(liq_text)
        self.liquidity_risk_label.setStyleSheet(f"color: {liq_color}; font-weight: bold;")

        # Generate dynamic risk warnings
        warnings = []
        if volatility > 4:
            warnings.append(f"⚠️ HIGH VOLATILITY: 24h volatility {volatility:.1f}% - reduce position size")
        if liquidity_score < 0.4:
            warnings.append(f"⚠️ LOW LIQUIDITY: Order book depth insufficient - limit order size")
        if correlation > 0.7:
            warnings.append(f"⚠️ CORRELATION ALERT: {correlation:.2f} correlation with BTC - diversify")
        if effective_leverage > recommended_leverage * 1.2:
            warnings.append(f"⚠️ LEVERAGE WARNING: Current {effective_leverage:.1f}x exceeds recommended {recommended_leverage:.1f}x")
        if portfolio_risk > 5:
            warnings.append(f"⚠️ PORTFOLIO RISK: {portfolio_risk:.1f}% exceeds 5% limit - reduce exposure")
        if max_drawdown > 10:
            warnings.append(f"⚠️ DRAWDOWN ALERT: {max_drawdown:.1f}% approaching stop-loss threshold")

        # Update warnings log
        if warnings:
            self.risk_warnings_log.clear()
            for warning in warnings:
                self.risk_warnings_log.append(warning)
        else:
            self.risk_warnings_log.clear()
            self.risk_warnings_log.append("✅ No active risk warnings - all metrics within acceptable ranges")

        # Reset buttons
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("SYSTEM: READY")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        self.log_message(f"Analysis complete for {self.symbol_combo.currentText()}: {decision}")
        self.statusBar().showMessage(f"Analysis complete: {decision} (ML: {decision}, LLM: {llm_decision})")

        # Re-enable analysis if auto-refresh is on and still analyzing
        if self.auto_refresh_checkbox.isChecked() and self.is_analyzing:
            self.analysis_timer = QTimer()
            self.analysis_timer.setSingleShot(True)
            self.analysis_timer.timeout.connect(self.start_analysis)
            self.analysis_timer.start(30000)  # Auto-refresh every 30 seconds
    
    def log_message(self, message):
        """Add message to analysis log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.analysis_log.append(log_entry)

    # Manual Trading Methods
    def place_limit_long(self):
        """Place a limit long order using best bid price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Limit LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit LONG order placed", 3000)
                else:
                    self.log_message(f"Failed to place limit LONG order")
                    self.statusBar().showMessage("Failed to place limit LONG order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'bids' not in ob or not ob['bids']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['bids'][0][0]  # Use best bid price
            set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'buy', quantity, price, params)

            if result:
                self.log_message(f"Limit LONG: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit LONG placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit LONG order")
                self.statusBar().showMessage("Failed to place limit LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_limit_short(self):
        """Place a limit short order using best ask price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Limit SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit SHORT order placed", 3000)
                else:
                    self.log_message(f"Failed to place limit SHORT order")
                    self.statusBar().showMessage("Failed to place limit SHORT order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'asks' not in ob or not ob['asks']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['asks'][0][0]  # Use best ask price
            set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'sell', quantity, price, params)

            if result:
                self.log_message(f"Limit SHORT: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit SHORT placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit SHORT order")
                self.statusBar().showMessage("Failed to place limit SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_long(self):
        """Place a market long order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market LONG executed", 3000)
                else:
                    self.log_message(f"Failed to place market LONG order")
                    self.statusBar().showMessage("Failed to place market LONG order", 3000)
                return

            # Fallback to original implementation
            set_leverage(symbol, leverage)
            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'buy', quantity, params)

            if result:
                self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market LONG executed", 3000)
            else:
                self.log_message(f"Failed to place market LONG order")
                self.statusBar().showMessage("Failed to place market LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_short(self):
        """Place a market short order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market SHORT executed", 3000)
                else:
                    self.log_message(f"Failed to place market SHORT order")
                    self.statusBar().showMessage("Failed to place market SHORT order", 3000)
                return

            # Fallback to original implementation
            set_leverage(symbol, leverage)
            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'sell', quantity, params)

            if result:
                self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market SHORT executed", 3000)
            else:
                self.log_message(f"Failed to place market SHORT order")
                self.statusBar().showMessage("Failed to place market SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def close_all_positions(self):
        """Close all open positions"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.close_all_positions()
                if count > 0:
                    self.log_message(f"Closed {count} positions")
                    self.statusBar().showMessage(f"Closed {count} positions", 3000)
                else:
                    self.log_message("No positions to close")
                    self.statusBar().showMessage("No positions to close", 3000)
                return

            # Fallback to original implementation
            result = close_all_positions()
            if result:
                self.log_message("All positions closed")
                self.statusBar().showMessage("All positions closed", 3000)
            else:
                self.log_message("Failed to close positions")
                self.statusBar().showMessage("Failed to close positions", 3000)
        except Exception as e:
            self.log_message(f"Error closing positions: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.cancel_all_orders()
                if count > 0:
                    self.log_message(f"Cancelled {count} orders")
                    self.statusBar().showMessage(f"Cancelled {count} orders", 3000)
                else:
                    self.log_message("No orders to cancel")
                    self.statusBar().showMessage("No orders to cancel", 3000)
                return

            # Fallback to original implementation
            result = cancel_all_orders()
            if result:
                self.log_message("All orders cancelled")
                self.statusBar().showMessage("All orders cancelled", 3000)
            else:
                self.log_message("Failed to cancel orders")
                self.statusBar().showMessage("Failed to cancel orders", 3000)
        except Exception as e:
            self.log_message(f"Error cancelling orders: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def update_chart(self):
        """Update chart with latest data (fallback chart only)"""
        try:
            # Only update if using fallback chart
            if not hasattr(self, 'chart_widget'):
                return

            symbol = self.symbol_combo.currentText()

            # Check if we have timeframe combo (fallback chart)
            if hasattr(self, 'timeframe_combo'):
                timeframe = self.timeframe_combo.currentText()
            else:
                timeframe = "1m"  # Default

            # Check if we have chart type combo (fallback chart)
            if hasattr(self, 'chart_type_combo'):
                chart_type = self.chart_type_combo.currentText()
            else:
                chart_type = "Line"  # Default

            # Fetch OHLCV data
            ohlcv_data = fetch_ohlcv(symbol, timeframe, 100)

            if not ohlcv_data:
                return

            # Clear previous data
            self.chart_widget.clear()

            # Extract data
            timestamps = [item[0] / 1000 for item in ohlcv_data]  # Convert to seconds
            prices = [item[4] for item in ohlcv_data]  # Close prices

            if chart_type == "Line":
                # Plot line chart
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=2)
                self.chart_widget.plot(timestamps, prices, pen=pen)
            else:
                # Plot candlestick chart (simplified as line for now)
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=1)
                self.chart_widget.plot(timestamps, prices, pen=pen)

            # Update best bid/ask display
            self.update_bid_ask_display()

        except Exception as e:
            print(f"Error updating chart: {e}")

    def update_bid_ask_display(self):
        """Update best bid/ask display"""
        try:
            symbol = self.symbol_combo.currentText()

            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                best_bid, best_ask = self.real_trading.get_best_bid_ask(symbol)
            else:
                # Fallback to original implementation
                best_bid = fetch_best_bid(symbol)
                best_ask = fetch_best_ask(symbol)

            # Update labels
            if best_bid:
                self.best_bid_label.setText(f"{best_bid:.6f}")
            else:
                self.best_bid_label.setText("--")

            if best_ask:
                self.best_ask_label.setText(f"{best_ask:.6f}")
            else:
                self.best_ask_label.setText("--")

        except Exception as e:
            print(f"Error updating bid/ask display: {e}")
            self.best_bid_label.setText("--")
            self.best_ask_label.setText("--")

    def on_chart_click(self, event):
        """Handle chart click events for order placement using best bid/ask"""
        try:
            if event.button() == 1:  # Left click - BUY (use best bid)
                self.place_limit_long()
            elif event.button() == 2:  # Right click - SELL (use best ask)
                self.place_limit_short()
            else:
                return

        except Exception as e:
            print(f"Error handling chart click: {e}")

    def on_live_chart_click(self, price: float, timestamp: float):
        """Handle live chart click events"""
        try:
            # For now, just trigger the same order placement logic
            # In the future, could use the clicked price for limit orders
            print(f"Live chart clicked at price: {price:.6f}, time: {timestamp}")

            # Use current best bid/ask instead of clicked price for safety
            self.place_limit_long()  # Default to long, could add right-click detection

        except Exception as e:
            print(f"Error handling live chart click: {e}")

    def on_chart_symbol_changed(self, symbol: str):
        """Handle symbol change from chart"""
        try:
            # Update main symbol selector
            self.symbol_combo.setCurrentText(symbol)

            # Subscribe to new symbol in live data manager
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                self.live_data_manager.subscribe_symbol(symbol, ["1m", "5m", "15m"])

            self.log_message(f"Chart symbol changed to: {symbol}")

        except Exception as e:
            print(f"Error handling chart symbol change: {e}")

    def on_chart_timeframe_changed(self, timeframe: str):
        """Handle timeframe change from chart"""
        try:
            self.log_message(f"Chart timeframe changed to: {timeframe}")

        except Exception as e:
            print(f"Error handling chart timeframe change: {e}")

    def on_live_chart_data_updated(self, symbol: str, chart_data: dict):
        """Handle live chart data updates"""
        try:
            if hasattr(self, 'live_chart'):
                # Update the live chart widget with new data
                ohlcv_data = chart_data.get("ohlcv", [])
                if ohlcv_data:
                    self.live_chart.update_ohlcv_data(ohlcv_data)

        except Exception as e:
            print(f"Error handling live chart data update: {e}")

    def on_live_price_updated(self, symbol: str, price: float):
        """Handle live price updates"""
        try:
            # Update current price display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                # Update any price displays
                pass

        except Exception as e:
            print(f"Error handling live price update: {e}")

    def on_live_orderbook_updated(self, symbol: str, orderbook_data: dict):
        """Handle live order book updates"""
        try:
            # Update bid/ask display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                bids = orderbook_data.get("bids", [])
                asks = orderbook_data.get("asks", [])

                best_bid = bids[0][0] if bids else None
                best_ask = asks[0][0] if asks else None

                # Update live chart bid/ask display
                if hasattr(self, 'live_chart'):
                    self.live_chart.update_bid_ask_data(best_bid, best_ask)

                # Update manual trading panel bid/ask display
                self.update_bid_ask_display()

        except Exception as e:
            print(f"Error handling live orderbook update: {e}")

    def on_live_connection_status(self, connected: bool):
        """Handle live data connection status changes"""
        try:
            # Update live chart connection status
            if hasattr(self, 'live_chart'):
                self.live_chart.update_connection_status(connected)

            # Update status in log
            status = "Connected to live data" if connected else "Disconnected from live data"
            self.log_message(status)

        except Exception as e:
            print(f"Error handling live connection status: {e}")

    # Real Trading Interface Signal Handlers
    def on_order_status_updated(self, order_info: dict):
        """Handle order status updates from real trading interface"""
        try:
            order_type = order_info.get('type', '')
            order = order_info.get('order', {})

            if order_type == 'order_placed':
                self.log_message(f"Order placed: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')}")
            elif order_type == 'order_filled':
                self.log_message(f"Order filled: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')} @ {order.get('price', 0)}")
            elif order_type == 'order_cancelled':
                self.log_message(f"Order cancelled: {order.get('id', 'N/A')}")

        except Exception as e:
            print(f"Error handling order status update: {e}")

    def on_position_status_updated(self, position_info: dict):
        """Handle position status updates from real trading interface"""
        try:
            symbol = position_info.get('symbol', '')
            size = position_info.get('size', 0)
            side = position_info.get('side', '')
            pnl = position_info.get('unrealized_pnl', 0)

            if size != 0:
                self.log_message(f"Position update: {side} {abs(size)} {symbol} - PnL: ${pnl:.2f}")

        except Exception as e:
            print(f"Error handling position status update: {e}")

    def on_balance_status_updated(self, balance_info: dict):
        """Handle balance status updates from real trading interface"""
        try:
            usdt_balance = balance_info.get('USDT', {}).get('free', 0)
            self.log_message(f"Balance update: ${usdt_balance:.2f} USDT available")

        except Exception as e:
            print(f"Error handling balance status update: {e}")

    def on_trading_error(self, error_message: str):
        """Handle trading errors from real trading interface"""
        try:
            self.log_message(f"Trading Error: {error_message}")
            self.statusBar().showMessage(f"Trading Error: {error_message}", 5000)

        except Exception as e:
            print(f"Error handling trading error: {e}")

    def on_trading_status(self, status_message: str):
        """Handle trading status updates from real trading interface"""
        try:
            self.log_message(f"Trading Status: {status_message}")

        except Exception as e:
            print(f"Error handling trading status: {e}")

    def on_pnl_updated(self, pnl_summary: dict):
        """Handle PnL updates from real trading interface"""
        try:
            unrealized = pnl_summary.get('unrealized_pnl', 0)
            realized = pnl_summary.get('realized_pnl', 0)
            total = pnl_summary.get('total_pnl', 0)

            # Update PnL display (could add to UI later)
            if total != 0:
                self.log_message(f"PnL Update: Total ${total:.2f} (Unrealized: ${unrealized:.2f}, Realized: ${realized:.2f})")

        except Exception as e:
            print(f"Error handling PnL update: {e}")

    def on_risk_warning(self, warning_type: str, warning_message: str):
        """Handle risk warnings from real trading interface"""
        try:
            self.log_message(f"RISK WARNING [{warning_type.upper()}]: {warning_message}")
            self.statusBar().showMessage(f"RISK WARNING: {warning_message}", 10000)

        except Exception as e:
            print(f"Error handling risk warning: {e}")

    # Signal Trading Engine Signal Handlers
    def on_signal_received(self, signal_data: dict):
        """Handle signal received from signal trading engine"""
        try:
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            confidence = signal_data.get('confidence', 0)

            self.log_message(f"Signal received: {source} -> {decision} ({confidence:.1%})")

            # Record signal in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_signal(signal_data)

        except Exception as e:
            print(f"Error handling signal received: {e}")

    def on_trade_decision_made(self, decision_data: dict):
        """Handle trade decision from signal trading engine"""
        try:
            symbol = decision_data.get('symbol', '')
            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0)
            reasoning = decision_data.get('reasoning', '')

            self.log_message(f"Trade decision: {decision} {symbol} ({confidence:.1%}) - {reasoning}")

        except Exception as e:
            print(f"Error handling trade decision: {e}")

    def on_automated_trade_executed(self, trade_data: dict):
        """Handle automated trade execution"""
        try:
            symbol = trade_data.get('symbol', '')
            decision = trade_data.get('decision', '')
            position_size = trade_data.get('position_size', 0)
            confidence = trade_data.get('confidence', 0)

            self.log_message(f"🤖 Automated trade: {decision} {position_size} {symbol} (confidence: {confidence:.1%})")
            self.statusBar().showMessage(f"Automated {decision} executed", 5000)

            # Record trade in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_trade(trade_data)

        except Exception as e:
            print(f"Error handling automated trade: {e}")

    def on_risk_limit_triggered(self, limit_type: str, details: dict):
        """Handle risk limit triggered"""
        try:
            self.log_message(f"🚨 RISK LIMIT TRIGGERED: {limit_type} - {details}")
            self.statusBar().showMessage(f"RISK LIMIT: {limit_type}", 10000)

        except Exception as e:
            print(f"Error handling risk limit: {e}")

    def on_engine_status_changed(self, status: str):
        """Handle signal trading engine status changes"""
        try:
            self.log_message(f"Engine status: {status}")

        except Exception as e:
            print(f"Error handling engine status: {e}")

    # Session Management Signal Handlers
    def on_session_started(self, session_id: str):
        """Handle session started"""
        try:
            self.log_message(f"📊 Session started: {session_id}")

        except Exception as e:
            print(f"Error handling session started: {e}")

    def on_session_ended(self, session_id: str, summary: dict):
        """Handle session ended"""
        try:
            trade_stats = summary.get('trade_stats', {})
            total_trades = trade_stats.get('total_trades', 0)
            win_rate = trade_stats.get('win_rate', 0)
            total_pnl = trade_stats.get('total_pnl', 0)

            self.log_message(f"📊 Session ended: {session_id}")
            self.log_message(f"📈 Summary: {total_trades} trades, {win_rate:.1f}% win rate, ${total_pnl:.2f} PnL")

        except Exception as e:
            print(f"Error handling session ended: {e}")

    def on_trade_recorded_to_db(self, trade_data: dict):
        """Handle trade recorded to database"""
        try:
            trade_id = trade_data.get('trade_id', 'N/A')
            pnl = trade_data.get('pnl', 0)
            self.log_message(f"💾 Trade recorded: {trade_id} (PnL: ${pnl:.2f})")

        except Exception as e:
            print(f"Error handling trade recorded: {e}")

    def on_signal_recorded_to_db(self, signal_data: dict):
        """Handle signal recorded to database"""
        try:
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            # Log at debug level to avoid spam
            # self.log_message(f"💾 Signal recorded: {source} -> {decision}")

        except Exception as e:
            print(f"Error handling signal recorded: {e}")

def main():
    """Main application entry point"""
    try:
        print("Starting Epinnox v6 Trading System...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxTradingInterface()
        window.show()
        
        print("✓ Epinnox v6 GUI started successfully")
        print("✓ Ready for trading analysis")
        
        # Run application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ Error starting Epinnox v6: {e}")
        print("\nMake sure you have:")
        print("1. PyQt5 installed: pip install PyQt5")
        print("2. Running from the Epinnox_v6 directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
