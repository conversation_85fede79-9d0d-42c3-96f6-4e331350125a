#!/usr/bin/env python3
"""
Epinnox v6 Trading System Launcher
Clean launch script for the integrated trading interface
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 imports
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    print("✓ PyQt5 loaded successfully")
except ImportError:
    print("✗ PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# Matrix Theme
class MatrixTheme:
    """Matrix-inspired theme colors and styling"""
    
    # Colors
    BLACK = "#000000"
    BACKGROUND = "#000000"
    GREEN = "#00FF00"
    TEXT = "#00FF00"
    DARK_GREEN = "#003300"
    LIGHT_GREEN = "#00CC00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    WHITE = "#FFFFFF"
    GRAY = "#808080"
    
    # Font sizes
    FONT_SIZE_SMALL = 10
    FONT_SIZE_MEDIUM = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: 'Courier New', monospace;
            font-size: {cls.FONT_SIZE_MEDIUM}px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {cls.DARK_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.GREEN};
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
            font-weight: bold;
        }}
        
        QGroupBox {{
            border: 2px solid {cls.DARK_GREEN};
            border-radius: 5px;
            margin: 5px;
            padding-top: 10px;
            color: {cls.GREEN};
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {cls.LIGHT_GREEN};
        }}
        
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
            padding: 8px 16px;
            font-weight: bold;
            border-radius: 3px;
        }}
        
        QPushButton:hover {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.LIGHT_GREEN};
            color: {cls.BLACK};
        }}
        
        QLabel {{
            color: {cls.GREEN};
            background-color: transparent;
        }}
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            font-family: 'Courier New', monospace;
        }}
        
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.DARK_GREEN};
            color: {cls.LIGHT_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 5px;
            border: 1px solid {cls.GREEN};
            font-weight: bold;
        }}
        
        QStatusBar {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-top: 1px solid {cls.DARK_GREEN};
        }}
        
        QCheckBox {{
            color: {cls.GREEN};
            spacing: 5px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
        }}
        
        QCheckBox::indicator:unchecked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.GREEN};
        }}
        """

# Simple Trading Interface
class EpinnoxTradingInterface(QMainWindow):
    """Simplified Epinnox Trading Interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(100, 100, 1200, 800)

        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())

        # Analysis control
        self.analysis_timer = None
        self.is_analyzing = False

        self.setup_ui()
        self.setup_timers()

        print("✓ Epinnox Trading Interface initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = self.create_header()
        layout.addLayout(header_layout)
        
        # Main content
        content_layout = self.create_content()
        layout.addLayout(content_layout)
        
        # Status bar
        self.statusBar().showMessage("Epinnox v6 System Ready")
    
    def create_header(self):
        """Create header section"""
        layout = QHBoxLayout()
        
        # Title
        title = QLabel("EPINNOX v6 TRADING SYSTEM")
        title.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_XLARGE}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 10px;
        """)
        
        # Status
        self.status_label = QLabel("SYSTEM: READY")
        self.status_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
        """)
        
        # Time
        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            color: {MatrixTheme.GREEN};
        """)
        
        layout.addWidget(title, 2)
        layout.addWidget(self.status_label, 1)
        layout.addWidget(self.time_label, 1)
        
        return layout
    
    def create_content(self):
        """Create main content area with comprehensive panels"""
        layout = QHBoxLayout()

        # Left column - Controls and Analysis
        left_column = QVBoxLayout()

        # Symbol selection and controls
        symbol_panel = self.create_symbol_panel()
        left_column.addWidget(symbol_panel)

        # Current analysis
        current_analysis_panel = self.create_current_analysis_panel()
        left_column.addWidget(current_analysis_panel)

        # Risk Warnings (moved to prominent position)
        risk_warnings_panel = self.create_risk_warnings_panel()
        left_column.addWidget(risk_warnings_panel)

        # LLM Analysis
        llm_panel = self.create_llm_analysis_panel()
        left_column.addWidget(llm_panel)

        # Leverage Analysis
        leverage_panel = self.create_leverage_panel()
        left_column.addWidget(leverage_panel)

        left_widget = QWidget()
        left_widget.setLayout(left_column)
        layout.addWidget(left_widget, 1)

        # Right column - Analysis Results
        right_column = QVBoxLayout()

        # Signal Hierarchy Analysis
        signal_hierarchy_panel = self.create_signal_hierarchy_panel()
        right_column.addWidget(signal_hierarchy_panel)

        # Market Analysis
        market_analysis_panel = self.create_market_analysis_panel()
        right_column.addWidget(market_analysis_panel)

        # Analysis Log
        analysis_log_panel = self.create_analysis_log_panel()
        right_column.addWidget(analysis_log_panel)

        # ML Models Status (moved from left column)
        ml_models_panel = self.create_ml_models_panel()
        right_column.addWidget(ml_models_panel)

        right_widget = QWidget()
        right_widget.setLayout(right_column)
        layout.addWidget(right_widget, 2)

        return layout
    
    def create_symbol_panel(self):
        """Create symbol selection panel"""
        group = QGroupBox("Symbol Selection")
        layout = QVBoxLayout(group)

        # Trading Symbol
        symbol_layout = QHBoxLayout()
        symbol_layout.addWidget(QLabel("Trading Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT"])
        symbol_layout.addWidget(self.symbol_combo)
        layout.addLayout(symbol_layout)

        # Checkboxes
        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.live_data_checkbox.setChecked(True)
        layout.addWidget(self.live_data_checkbox)

        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (30s)")
        self.auto_refresh_checkbox.setChecked(True)
        layout.addWidget(self.auto_refresh_checkbox)

        # Buttons
        self.analyze_button = QPushButton("ANALYZE SYMBOL")
        self.analyze_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.analyze_button)

        self.stop_button = QPushButton("STOP ANALYSIS")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_analysis)
        layout.addWidget(self.stop_button)

        return group

    def create_current_analysis_panel(self):
        """Create current analysis panel"""
        group = QGroupBox("Current Analysis")
        layout = QVBoxLayout(group)

        self.decision_label = QLabel("Decision: WAIT")
        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.decision_label)

        self.confidence_label = QLabel("Confidence: 86.0%")
        layout.addWidget(self.confidence_label)

        self.last_update_label = QLabel("Last Update: 19:32:58")
        layout.addWidget(self.last_update_label)

        return group

    def create_ml_models_panel(self):
        """Create ML models status panel"""
        group = QGroupBox("ML Models Status")
        layout = QVBoxLayout(group)

        # Create table
        self.ml_models_table = QTableWidget(3, 3)
        self.ml_models_table.setHorizontalHeaderLabels(["Model", "Decision", "Confidence"])
        self.ml_models_table.verticalHeader().setVisible(False)
        self.ml_models_table.setMaximumHeight(120)

        # Apply Matrix theme styling
        self.ml_models_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                padding: 5px;
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                border: none;
                font-weight: bold;
            }}
        """)
        self.ml_models_table.horizontalHeader().setStretchLastSection(False)
        self.ml_models_table.resizeColumnsToContents()

        # Add sample data
        models_data = [
            ["SVM", "WAIT", "58.2%"],
            ["Random Forest", "LONG", "72.1%"],
            ["LSTM", "WAIT", "61.4%"]
        ]

        for row, (model, decision, confidence) in enumerate(models_data):
            self.ml_models_table.setItem(row, 0, QTableWidgetItem(model))

            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)

            self.ml_models_table.setItem(row, 2, QTableWidgetItem(confidence))

        self.ml_models_table.resizeColumnsToContents()
        layout.addWidget(self.ml_models_table)

        return group

    def create_llm_analysis_panel(self):
        """Create LLM analysis panel"""
        group = QGroupBox("LLM Analysis")
        layout = QVBoxLayout(group)

        # LLM Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Model:"))
        self.llm_model_combo = QComboBox()
        self.llm_model_combo.addItems(["Phi-3.5-mini", "LLaMA-3-8B", "GPT-4o-mini"])
        model_layout.addWidget(self.llm_model_combo)
        layout.addLayout(model_layout)

        # LLM Decision
        self.llm_decision_label = QLabel("LLM Decision: WAIT")
        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.llm_decision_label)

        # LLM Confidence
        self.llm_confidence_label = QLabel("LLM Confidence: 75.0%")
        layout.addWidget(self.llm_confidence_label)

        # LLM Reasoning (scrollable)
        reasoning_label = QLabel("LLM Reasoning:")
        layout.addWidget(reasoning_label)

        self.llm_reasoning_text = QTextEdit()
        self.llm_reasoning_text.setReadOnly(True)
        self.llm_reasoning_text.setMaximumHeight(80)
        self.llm_reasoning_text.setText("Market shows mixed signals. Technical indicators suggest consolidation while volume patterns indicate potential breakout. Recommend WAIT for clearer direction.")
        layout.addWidget(self.llm_reasoning_text)

        return group

    def create_leverage_panel(self):
        """Create leverage analysis panel"""
        group = QGroupBox("Leverage Analysis")
        layout = QVBoxLayout(group)

        self.max_leverage_label = QLabel("Max Available: 1.0x")
        layout.addWidget(self.max_leverage_label)

        self.recommended_leverage_label = QLabel("Recommended: 1.0x")
        layout.addWidget(self.recommended_leverage_label)

        self.effective_leverage_label = QLabel("Effective: 0.4x")
        layout.addWidget(self.effective_leverage_label)

        self.position_size_label = QLabel("Position Size: 0.00 units ($0.00)")
        layout.addWidget(self.position_size_label)

        self.risk_per_trade_label = QLabel("Risk per Trade: $0.00")
        layout.addWidget(self.risk_per_trade_label)

        return group
    
    def create_signal_hierarchy_panel(self):
        """Create signal hierarchy analysis panel"""
        group = QGroupBox("Signal Hierarchy Analysis")
        layout = QVBoxLayout(group)

        # Create table
        self.signal_hierarchy_table = QTableWidget(4, 4)
        self.signal_hierarchy_table.setHorizontalHeaderLabels(["Source", "Decision", "Confidence", "Weight"])
        self.signal_hierarchy_table.verticalHeader().setVisible(False)
        self.signal_hierarchy_table.setMaximumHeight(180)

        # Apply Matrix theme styling
        self.signal_hierarchy_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                padding: 5px;
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                border: none;
                font-weight: bold;
            }}
        """)
        self.signal_hierarchy_table.horizontalHeader().setStretchLastSection(False)
        self.signal_hierarchy_table.resizeColumnsToContents()

        # Add sample data
        signals_data = [
            ["ml_ensemble", "WAIT", "68.7%", "0.3"],
            ["llm_analysis", "WAIT", "75.0%", "0.3"],
            ["technical_signals", "WAIT", "83.0%", "0.2"],
            ["multi_timeframe", "SHORT", "0.0%", "0.2"]
        ]

        for row, (source, decision, confidence, weight) in enumerate(signals_data):
            self.signal_hierarchy_table.setItem(row, 0, QTableWidgetItem(source))

            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.signal_hierarchy_table.setItem(row, 1, decision_item)

            self.signal_hierarchy_table.setItem(row, 2, QTableWidgetItem(confidence))
            self.signal_hierarchy_table.setItem(row, 3, QTableWidgetItem(weight))

        self.signal_hierarchy_table.resizeColumnsToContents()
        layout.addWidget(self.signal_hierarchy_table)

        return group

    def create_market_analysis_panel(self):
        """Create market analysis panel"""
        group = QGroupBox("Market Analysis")
        layout = QVBoxLayout(group)

        self.market_regime_label = QLabel("Market Regime: STRONG_TREND")
        layout.addWidget(self.market_regime_label)

        self.trend_strength_label = QLabel("Trend Strength: 0.00")
        layout.addWidget(self.trend_strength_label)

        self.volatility_label = QLabel("Volatility: 0.00%")
        layout.addWidget(self.volatility_label)

        self.liquidity_score_label = QLabel("Liquidity Score: --")
        layout.addWidget(self.liquidity_score_label)

        return group

    def create_analysis_log_panel(self):
        """Create analysis log panel"""
        group = QGroupBox("Analysis Log")
        layout = QVBoxLayout(group)

        self.analysis_log = QTextEdit()
        self.analysis_log.setReadOnly(True)
        self.analysis_log.setMaximumHeight(200)

        # Add sample log entries
        sample_logs = [
            "[19:20:02] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:20:19] Started analysis for DOGE/USDT:USDT",
            "[19:20:43] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:20:56] Started analysis for DOGE/USDT:USDT",
            "[19:21:00] Analysis complete for DOGE/USDT:USDT: LONG",
            "[19:21:17] Started analysis for DOGE/USDT:USDT",
            "[19:21:31] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:21:45] Started analysis for DOGE/USDT:USDT",
            "[19:21:56] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:22:11] Started analysis for DOGE/USDT:USDT",
            "[19:22:31] Analysis complete for DOGE/USDT:USDT: LONG",
            "[19:22:45] Started analysis for DOGE/USDT:USDT",
            "[19:23:01] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:23:16] Started analysis for DOGE/USDT:USDT"
        ]

        for log_entry in sample_logs:
            self.analysis_log.append(log_entry)

        layout.addWidget(self.analysis_log)

        return group

    def create_risk_warnings_panel(self):
        """Create comprehensive risk warnings panel"""
        group = QGroupBox("Risk Management & Warnings")
        layout = QVBoxLayout(group)

        # Risk Metrics
        metrics_layout = QGridLayout()

        # Row 1: Portfolio Risk
        metrics_layout.addWidget(QLabel("Portfolio Risk:"), 0, 0)
        self.portfolio_risk_label = QLabel("2.5%")
        self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.portfolio_risk_label, 0, 1)

        # Row 2: Max Drawdown
        metrics_layout.addWidget(QLabel("Max Drawdown:"), 1, 0)
        self.max_drawdown_label = QLabel("5.2%")
        self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.max_drawdown_label, 1, 1)

        # Row 3: Correlation Risk
        metrics_layout.addWidget(QLabel("Correlation Risk:"), 2, 0)
        self.correlation_risk_label = QLabel("LOW")
        self.correlation_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.correlation_risk_label, 2, 1)

        # Row 4: Liquidity Risk
        metrics_layout.addWidget(QLabel("Liquidity Risk:"), 3, 0)
        self.liquidity_risk_label = QLabel("MEDIUM")
        self.liquidity_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.liquidity_risk_label, 3, 1)

        layout.addLayout(metrics_layout)

        # Active Warnings
        warnings_label = QLabel("Active Warnings:")
        warnings_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        layout.addWidget(warnings_label)

        self.risk_warnings_log = QTextEdit()
        self.risk_warnings_log.setReadOnly(True)
        self.risk_warnings_log.setMaximumHeight(100)

        # Add comprehensive sample warnings
        sample_warnings = [
            "⚠️ HIGH VOLATILITY: 24h volatility >5% - reduce position size",
            "⚠️ LOW LIQUIDITY: Order book depth <$50k - limit order size",
            "⚠️ CORRELATION ALERT: 0.85 correlation with BTC - diversify",
            "⚠️ LEVERAGE WARNING: Current 3.2x exceeds recommended 2.5x"
        ]

        for warning in sample_warnings:
            self.risk_warnings_log.append(warning)

        layout.addWidget(self.risk_warnings_log)

        return group
    
    def setup_timers(self):
        """Setup update timers"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def start_analysis(self):
        """Start trading analysis"""
        symbol = self.symbol_combo.currentText()
        use_live = self.live_data_checkbox.isChecked()

        self.is_analyzing = True
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("SYSTEM: ANALYZING")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")

        self.log_message(f"Starting analysis for {symbol} (Live: {use_live})")

        # Simulate analysis
        QTimer.singleShot(3000, self.complete_analysis)

    def stop_analysis(self):
        """Stop analysis"""
        self.is_analyzing = False

        # Stop any pending analysis timer
        if self.analysis_timer:
            self.analysis_timer.stop()
            self.analysis_timer = None

        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("SYSTEM: READY")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        self.log_message("Analysis stopped by user")
    
    def complete_analysis(self):
        """Complete analysis simulation"""
        import random

        decisions = ["LONG", "SHORT", "WAIT"]

        # Generate individual ML model decisions
        ml_decisions = [random.choice(decisions) for _ in range(3)]
        ml_confidences = [random.uniform(50, 85) for _ in range(3)]

        # Generate LLM decision
        llm_decision = random.choice(decisions)
        llm_confidence = random.uniform(65, 85)

        # Weighted decision making (ML ensemble: 30%, LLM: 30%, Technical: 20%, Multi-timeframe: 20%)
        decision_scores = {"LONG": 0, "SHORT": 0, "WAIT": 0}

        # ML ensemble contribution (30%)
        ml_ensemble_decision = max(set(ml_decisions), key=ml_decisions.count)  # Majority vote
        decision_scores[ml_ensemble_decision] += 0.3

        # LLM contribution (30%)
        decision_scores[llm_decision] += 0.3

        # Technical signals (20%)
        tech_decision = random.choice(decisions)
        decision_scores[tech_decision] += 0.2

        # Multi-timeframe (20%)
        mtf_decision = random.choice(decisions)
        decision_scores[mtf_decision] += 0.2

        # Final decision is the highest scored
        decision = max(decision_scores, key=decision_scores.get)
        confidence = (decision_scores[decision] * 100) + random.uniform(-10, 10)  # Add some noise
        confidence = max(50, min(95, confidence))  # Clamp between 50-95%

        # Update current analysis panel
        self.decision_label.setText(f"Decision: {decision}")
        self.confidence_label.setText(f"Confidence: {confidence:.1f}%")
        self.last_update_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

        # Color code decision
        if decision == "LONG":
            color = MatrixTheme.GREEN
        elif decision == "SHORT":
            color = MatrixTheme.RED
        else:
            color = MatrixTheme.YELLOW

        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {color};
            padding: 5px;
        """)

        # Update ML models with generated decisions
        models_data = [
            ["SVM", ml_decisions[0], f"{ml_confidences[0]:.1f}%"],
            ["Random Forest", ml_decisions[1], f"{ml_confidences[1]:.1f}%"],
            ["LSTM", ml_decisions[2], f"{ml_confidences[2]:.1f}%"]
        ]

        for row, (model, ml_decision, ml_confidence) in enumerate(models_data):
            decision_item = QTableWidgetItem(ml_decision)
            if ml_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif ml_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)
            self.ml_models_table.setItem(row, 2, QTableWidgetItem(ml_confidence))

        # Generate LLM analysis
        llm_decision = random.choice(decisions)
        llm_confidence = random.uniform(65, 85)
        llm_reasonings = [
            "Technical indicators show bullish divergence with strong volume support. RSI oversold suggests potential reversal.",
            "Market sentiment appears bearish with declining volume. Multiple resistance levels suggest downward pressure.",
            "Mixed signals detected. Consolidation pattern forming with unclear directional bias. Await breakout confirmation.",
            "Strong momentum indicators align with price action. Volume profile supports current trend continuation.",
            "Risk-off sentiment dominates. Correlation with broader market suggests defensive positioning appropriate."
        ]
        llm_reasoning = random.choice(llm_reasonings)

        # Update LLM panel
        self.llm_decision_label.setText(f"LLM Decision: {llm_decision}")
        if llm_decision == "LONG":
            llm_color = MatrixTheme.GREEN
        elif llm_decision == "SHORT":
            llm_color = MatrixTheme.RED
        else:
            llm_color = MatrixTheme.YELLOW

        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {llm_color};
            padding: 5px;
        """)

        self.llm_confidence_label.setText(f"LLM Confidence: {llm_confidence:.1f}%")
        self.llm_reasoning_text.setText(llm_reasoning)

        # Update signal hierarchy (now includes LLM)
        signals_data = [
            ["ml_ensemble", ml_ensemble_decision, f"{sum(ml_confidences)/3:.1f}%", "0.3"],
            ["llm_analysis", llm_decision, f"{llm_confidence:.1f}%", "0.3"],
            ["technical_signals", tech_decision, f"{random.uniform(70, 90):.1f}%", "0.2"],
            ["multi_timeframe", mtf_decision, f"{random.uniform(40, 80):.1f}%", "0.2"]
        ]

        for row, (source, sig_decision, sig_confidence, weight) in enumerate(signals_data):
            decision_item = QTableWidgetItem(sig_decision)
            if sig_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif sig_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.signal_hierarchy_table.setItem(row, 1, decision_item)
            self.signal_hierarchy_table.setItem(row, 2, QTableWidgetItem(sig_confidence))
            self.signal_hierarchy_table.setItem(row, 3, QTableWidgetItem(weight))

        # Update market analysis
        trend_strength = random.uniform(0, 1)
        volatility = random.uniform(0, 5)
        self.trend_strength_label.setText(f"Trend Strength: {trend_strength:.2f}")
        self.volatility_label.setText(f"Volatility: {volatility:.2f}%")

        # Update leverage analysis
        max_leverage = random.uniform(1, 5)
        recommended_leverage = max_leverage * 0.7
        effective_leverage = recommended_leverage * 0.6
        position_size = random.uniform(100, 1000)
        risk_per_trade = position_size * 0.02

        self.max_leverage_label.setText(f"Max Available: {max_leverage:.1f}x")
        self.recommended_leverage_label.setText(f"Recommended: {recommended_leverage:.1f}x")
        self.effective_leverage_label.setText(f"Effective: {effective_leverage:.1f}x")
        self.position_size_label.setText(f"Position Size: {position_size:.2f} units (${position_size:.2f})")
        self.risk_per_trade_label.setText(f"Risk per Trade: ${risk_per_trade:.2f}")

        # Update risk metrics
        portfolio_risk = random.uniform(1, 8)
        max_drawdown = random.uniform(2, 15)
        correlation = random.uniform(0.1, 0.9)
        liquidity_score = random.uniform(0.1, 1.0)

        # Update portfolio risk
        self.portfolio_risk_label.setText(f"{portfolio_risk:.1f}%")
        if portfolio_risk > 5:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif portfolio_risk > 3:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update max drawdown
        self.max_drawdown_label.setText(f"{max_drawdown:.1f}%")
        if max_drawdown > 10:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif max_drawdown > 5:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update correlation risk
        if correlation > 0.7:
            corr_text, corr_color = "HIGH", MatrixTheme.RED
        elif correlation > 0.4:
            corr_text, corr_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            corr_text, corr_color = "LOW", MatrixTheme.GREEN

        self.correlation_risk_label.setText(corr_text)
        self.correlation_risk_label.setStyleSheet(f"color: {corr_color}; font-weight: bold;")

        # Update liquidity risk
        if liquidity_score < 0.3:
            liq_text, liq_color = "HIGH", MatrixTheme.RED
        elif liquidity_score < 0.6:
            liq_text, liq_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            liq_text, liq_color = "LOW", MatrixTheme.GREEN

        self.liquidity_risk_label.setText(liq_text)
        self.liquidity_risk_label.setStyleSheet(f"color: {liq_color}; font-weight: bold;")

        # Generate dynamic risk warnings
        warnings = []
        if volatility > 4:
            warnings.append(f"⚠️ HIGH VOLATILITY: 24h volatility {volatility:.1f}% - reduce position size")
        if liquidity_score < 0.4:
            warnings.append(f"⚠️ LOW LIQUIDITY: Order book depth insufficient - limit order size")
        if correlation > 0.7:
            warnings.append(f"⚠️ CORRELATION ALERT: {correlation:.2f} correlation with BTC - diversify")
        if effective_leverage > recommended_leverage * 1.2:
            warnings.append(f"⚠️ LEVERAGE WARNING: Current {effective_leverage:.1f}x exceeds recommended {recommended_leverage:.1f}x")
        if portfolio_risk > 5:
            warnings.append(f"⚠️ PORTFOLIO RISK: {portfolio_risk:.1f}% exceeds 5% limit - reduce exposure")
        if max_drawdown > 10:
            warnings.append(f"⚠️ DRAWDOWN ALERT: {max_drawdown:.1f}% approaching stop-loss threshold")

        # Update warnings log
        if warnings:
            self.risk_warnings_log.clear()
            for warning in warnings:
                self.risk_warnings_log.append(warning)
        else:
            self.risk_warnings_log.clear()
            self.risk_warnings_log.append("✅ No active risk warnings - all metrics within acceptable ranges")

        # Reset buttons
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("SYSTEM: READY")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        self.log_message(f"Analysis complete for {self.symbol_combo.currentText()}: {decision}")
        self.statusBar().showMessage(f"Analysis complete: {decision} (ML: {decision}, LLM: {llm_decision})")

        # Re-enable analysis if auto-refresh is on and still analyzing
        if self.auto_refresh_checkbox.isChecked() and self.is_analyzing:
            self.analysis_timer = QTimer()
            self.analysis_timer.setSingleShot(True)
            self.analysis_timer.timeout.connect(self.start_analysis)
            self.analysis_timer.start(30000)  # Auto-refresh every 30 seconds
    
    def log_message(self, message):
        """Add message to analysis log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.analysis_log.append(log_entry)

def main():
    """Main application entry point"""
    try:
        print("Starting Epinnox v6 Trading System...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxTradingInterface()
        window.show()
        
        print("✓ Epinnox v6 GUI started successfully")
        print("✓ Ready for trading analysis")
        
        # Run application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ Error starting Epinnox v6: {e}")
        print("\nMake sure you have:")
        print("1. PyQt5 installed: pip install PyQt5")
        print("2. Running from the Epinnox_v6 directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
