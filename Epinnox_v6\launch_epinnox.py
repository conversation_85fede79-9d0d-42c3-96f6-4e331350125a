#!/usr/bin/env python3
"""
Epinnox v6 Trading System Launcher
Clean launch script for the integrated trading interface
"""

import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 imports
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    print("✓ PyQt5 loaded successfully")
except ImportError:
    print("✗ PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# PyQtGraph for charting
try:
    import pyqtgraph as pg
    print("✓ PyQtGraph loaded successfully")
    # Configure PyQtGraph
    pg.setConfigOptions(useOpenGL=False, antialias=True)
except ImportError:
    print("✗ PyQtGraph not installed. Install with: pip install pyqtgraph")
    sys.exit(1)

# Try to import ccxt for real trading functionality
try:
    import ccxt
    print("✓ CCXT library available")

    # Initialize exchange (HTX/Huobi for demo - you can modify this)
    try:
        # Try to initialize HTX exchange in sandbox mode
        exchange = ccxt.htx({
            'sandbox': True,  # Use sandbox for testing
            'enableRateLimit': True,
        })
        demo_mode = True  # Keep in demo mode for safety
        print("✓ HTX exchange initialized in sandbox mode")
    except Exception as e:
        print(f"⚠ Could not initialize exchange: {e}")
        exchange = None
        demo_mode = True

    # Real trading functions using ccxt
    def place_limit_order(symbol, side, amount, price, params={}):
        """Place limit order using ccxt"""
        if exchange and not demo_mode:
            try:
                order = exchange.create_limit_order(symbol, side, amount, price, params)
                print(f"Real: Placed {side} limit order for {amount} {symbol} at {price}")
                return order
            except Exception as e:
                print(f"Error placing limit order: {e}")
                return None
        else:
            print(f"DEMO: Placed {side} limit order for {amount} {symbol} at {price}")
            return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params={}):
        """Place market order using ccxt"""
        if exchange and not demo_mode:
            try:
                order = exchange.create_market_order(symbol, side, amount, params)
                print(f"Real: Placed {side} market order for {amount} {symbol}")
                return order
            except Exception as e:
                print(f"Error placing market order: {e}")
                return None
        else:
            print(f"DEMO: Placed {side} market order for {amount} {symbol}")
            return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_ohlcv(symbol, timeframe="1m", limit=100):
        """Fetch OHLCV data using ccxt"""
        if exchange:
            try:
                return exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            except Exception as e:
                print(f"Error fetching OHLCV: {e}")
                return []  # Return empty data instead of mock
        else:
            print("No exchange available for OHLCV data")
            return []

    def fetch_order_book(symbol):
        """Fetch order book using ccxt"""
        if exchange:
            try:
                return exchange.fetch_order_book(symbol)
            except Exception as e:
                print(f"Error fetching order book: {e}")
                return {'bids': [], 'asks': []}  # Return empty orderbook instead of mock
        else:
            print("No exchange available for orderbook data")
            return {'bids': [], 'asks': []}

    def fetch_best_bid(symbol):
        """Fetch best bid using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'bids' in ob and ob['bids']:
                return ob['bids'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best bid: {e}")
            return None

    def fetch_best_ask(symbol):
        """Fetch best ask using ccxt"""
        try:
            ob = fetch_order_book(symbol)
            if ob and 'asks' in ob and ob['asks']:
                return ob['asks'][0][0]
            return None
        except Exception as e:
            print(f"Error fetching best ask: {e}")
            return None

    # Removed mock data generation - using real data from live data manager

    def close_all_positions():
        """Close all positions"""
        print("DEMO: Closing all positions")
        return True

    def cancel_all_orders():
        """Cancel all orders"""
        print("DEMO: Cancelling all orders")
        return True

    def set_leverage(symbol, leverage):
        """Set leverage"""
        print(f"DEMO: Set leverage {leverage}x for {symbol}")
        return True

except ImportError as e:
    print(f"⚠ Could not import trading functions: {e}")
    print("⚠ Using mock implementations")

    # Fallback mock implementations
    def place_limit_order(symbol, side, amount, price, params={}):
        """Mock limit order placement"""
        print(f"MOCK: Placed {side} limit order for {amount} {symbol} at {price}")
        return {"id": f"limit_{int(datetime.now().timestamp())}", "status": "open"}

    def place_market_order(symbol, side, amount, params={}):
        """Mock market order placement"""
        print(f"MOCK: Placed {side} market order for {amount} {symbol}")
        return {"id": f"market_{int(datetime.now().timestamp())}", "status": "filled"}

    def fetch_ohlcv(symbol, timeframe="1m", limit=100):
        """Mock OHLCV data"""
        import random
        import time

        data = []
        base_price = 0.35  # DOGE price
        current_time = int(time.time() * 1000)

        for i in range(limit):
            timestamp = current_time - (limit - i) * 60000  # 1 minute intervals
            open_price = base_price + random.uniform(-0.01, 0.01)
            high_price = open_price + random.uniform(0, 0.005)
            low_price = open_price - random.uniform(0, 0.005)
            close_price = open_price + random.uniform(-0.005, 0.005)
            volume = random.uniform(1000, 10000)

            data.append([timestamp, open_price, high_price, low_price, close_price, volume])
            base_price = close_price

        return data

    def fetch_order_book(symbol):
        """Mock order book"""
        import random
        base_price = 0.35
        return {
            'bids': [[base_price - 0.001, 1000], [base_price - 0.002, 2000]],
            'asks': [[base_price + 0.001, 1000], [base_price + 0.002, 2000]]
        }

    def fetch_best_bid(symbol):
        """Mock best bid"""
        ob = fetch_order_book(symbol)
        return ob['bids'][0][0] if ob['bids'] else None

    def fetch_best_ask(symbol):
        """Mock best ask"""
        ob = fetch_order_book(symbol)
        return ob['asks'][0][0] if ob['asks'] else None

    def close_all_positions():
        """Mock close all positions"""
        print("MOCK: Closing all positions")
        return True

    def cancel_all_orders():
        """Mock cancel all orders"""
        print("MOCK: Cancelling all orders")
        return True

    def set_leverage(symbol, leverage):
        """Mock set leverage"""
        print(f"MOCK: Set leverage {leverage}x for {symbol}")
        return True

    exchange = None
    demo_mode = True

# Import GUI components
try:
    from gui.model_selector_widget import ModelSelectorWidget
    print("✓ Model selector widget loaded")
except ImportError as e:
    print(f"⚠ Could not import model selector widget: {e}")
    ModelSelectorWidget = None

# Matrix Theme
class MatrixTheme:
    """Matrix-inspired theme colors and styling"""
    
    # Colors
    BLACK = "#000000"
    BACKGROUND = "#000000"
    GREEN = "#00FF00"
    TEXT = "#00FF00"
    DARK_GREEN = "#003300"
    MID_GREEN = "#006600"
    LIGHT_GREEN = "#00CC00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    WHITE = "#FFFFFF"
    GRAY = "#666666"
    BRIGHT_GREEN = "#00FF88"

    # Font settings
    FONT_FAMILY = "Courier New"
    FONT_SIZE = 12
    FONT_SIZE_SMALL = 12
    FONT_SIZE_MEDIUM = 12
    FONT_SIZE_LARGE = 14
    FONT_SIZE_XLARGE = 16
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: 'Courier New', monospace;
            font-size: {cls.FONT_SIZE_MEDIUM}px;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {cls.DARK_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.GREEN};
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
            font-weight: bold;
        }}
        
        QGroupBox {{
            border: 2px solid {cls.DARK_GREEN};
            border-radius: 5px;
            margin: 5px;
            padding-top: 10px;
            color: {cls.GREEN};
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {cls.LIGHT_GREEN};
        }}
        
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
            padding: 8px 16px;
            font-weight: bold;
            border-radius: 3px;
        }}
        
        QPushButton:hover {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.LIGHT_GREEN};
            color: {cls.BLACK};
        }}
        
        QLabel {{
            color: {cls.GREEN};
            background-color: transparent;
        }}
        
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            padding: 5px;
            border-radius: 3px;
        }}
        
        QTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.DARK_GREEN};
            font-family: 'Courier New', monospace;
        }}
        
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.DARK_GREEN};
            color: {cls.LIGHT_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 5px;
            border: 1px solid {cls.GREEN};
            font-weight: bold;
        }}
        
        QStatusBar {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border-top: 1px solid {cls.DARK_GREEN};
        }}
        
        QCheckBox {{
            color: {cls.GREEN};
            spacing: 5px;
        }}
        
        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
        }}
        
        QCheckBox::indicator:unchecked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            border: 2px solid {cls.GREEN};
            background-color: {cls.GREEN};
        }}
        """

# Simple Trading Interface
class EpinnoxTradingInterface(QMainWindow):
    """Simplified Epinnox Trading Interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Trading System")
        self.setGeometry(50, 50, 1400, 900)  # Larger window for better layout
        self.setMinimumSize(1200, 800)  # Minimum size to prevent cramping

        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())

        # Analysis control
        self.analysis_timer = None
        self.is_analyzing = False

        self.setup_ui()
        self.setup_timers()
        self.setup_menu_bar()

        print("✓ Epinnox Trading Interface initialized")
    
    def setup_ui(self):
        """Setup the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setSpacing(5)  # Reduced spacing since header is now in menu bar
        layout.setContentsMargins(10, 5, 10, 10)  # Reduced top margin

        # Main content (header is now in menu bar)
        content_widget = self.create_content()
        layout.addWidget(content_widget)

        # Status bar
        self.statusBar().showMessage("Epinnox v6 System Ready")

    def setup_menu_bar(self):
        """Setup menu bar with layout and settings options"""
        menubar = self.menuBar()

        # Layout menu
        layout_menu = menubar.addMenu('Layout')

        # Save layout action
        save_layout_action = QAction('Save Layout', self)
        save_layout_action.setShortcut('Ctrl+S')
        save_layout_action.triggered.connect(self.save_layout)
        layout_menu.addAction(save_layout_action)

        # Load layout action
        load_layout_action = QAction('Load Layout', self)
        load_layout_action.setShortcut('Ctrl+L')
        load_layout_action.triggered.connect(self.load_layout)
        layout_menu.addAction(load_layout_action)

        layout_menu.addSeparator()

        # Reset to default layout
        reset_layout_action = QAction('Reset to Default', self)
        reset_layout_action.setShortcut('Ctrl+R')
        reset_layout_action.triggered.connect(self.reset_layout)
        layout_menu.addAction(reset_layout_action)

        # Settings menu
        settings_menu = menubar.addMenu('Settings')

        # Model selection action
        model_settings_action = QAction('Model Selection', self)
        model_settings_action.setShortcut('Ctrl+M')
        model_settings_action.triggered.connect(self.show_model_settings)
        settings_menu.addAction(model_settings_action)

        settings_menu.addSeparator()

        # Preferences action
        preferences_action = QAction('Preferences', self)
        preferences_action.triggered.connect(self.show_preferences)
        settings_menu.addAction(preferences_action)

        # About menu
        about_menu = menubar.addMenu('About')

        # About Epinnox action
        about_action = QAction('About Epinnox', self)
        about_action.triggered.connect(self.show_about)
        about_menu.addAction(about_action)

        # Add status widgets to the right side of menu bar
        from PyQt5.QtCore import QTimer
        import datetime

        # Create compact status widget container
        status_widget = QWidget()
        status_widget.setMaximumWidth(400)  # Limit width to prevent overlap
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 0, 5, 0)
        status_layout.setSpacing(10)  # Reduced spacing

        # System title (shorter)
        title_label = QLabel("EPINNOX v6")
        title_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: 11px;
        """)
        status_layout.addWidget(title_label)

        # System status
        self.system_status_label = QLabel("READY")
        self.system_status_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: 11px;
        """)
        status_layout.addWidget(self.system_status_label)

        # Current time
        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            color: {MatrixTheme.GREEN};
            font-weight: bold;
            font-size: 11px;
        """)
        status_layout.addWidget(self.time_label)

        # Add status widget to menu bar (right side)
        menubar.setCornerWidget(status_widget, Qt.TopRightCorner)

        # Setup timer to update time
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # Update every second
        self.update_time_display()  # Initial update

        # Style menu bar - ensure menu items are visible
        menubar.setStyleSheet(f"""
            QMenuBar {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                border-bottom: 1px solid {MatrixTheme.MID_GREEN};
                padding: 2px;
                height: 30px;
                font-size: 12px;
                font-weight: bold;
            }}
            QMenuBar::item {{
                background-color: transparent;
                color: {MatrixTheme.GREEN};
                padding: 6px 12px;
                margin: 0px 2px;
                border-radius: 3px;
            }}
            QMenuBar::item:selected {{
                background-color: {MatrixTheme.MID_GREEN};
                color: {MatrixTheme.GREEN};
            }}
            QMenuBar::item:pressed {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
            }}
            QMenu {{
                background-color: {MatrixTheme.BLACK};
                color: {MatrixTheme.GREEN};
                border: 1px solid {MatrixTheme.MID_GREEN};
                font-size: 11px;
            }}
            QMenu::item {{
                padding: 6px 20px;
                background-color: transparent;
            }}
            QMenu::item:selected {{
                background-color: {MatrixTheme.MID_GREEN};
                color: {MatrixTheme.GREEN};
            }}
        """)

        print("Menu bar setup complete")  # Debug

    def save_layout(self):
        """Save current layout to settings"""
        try:
            settings = QSettings('Epinnox', 'TradingInterface')

            # Save main splitter state
            if hasattr(self, 'main_splitter'):
                settings.setValue('main_splitter', self.main_splitter.saveState())

            # Save individual column splitter states
            if hasattr(self, 'left_splitter'):
                settings.setValue('left_splitter', self.left_splitter.saveState())
            if hasattr(self, 'middle_splitter'):
                settings.setValue('middle_splitter', self.middle_splitter.saveState())
            if hasattr(self, 'right_splitter'):
                settings.setValue('right_splitter', self.right_splitter.saveState())

            # Save window geometry
            settings.setValue('geometry', self.saveGeometry())
            settings.setValue('windowState', self.saveState())

            self.statusBar().showMessage("Layout saved successfully", 2000)
            print("✓ Layout saved to settings")

        except Exception as e:
            print(f"Error saving layout: {e}")
            self.statusBar().showMessage("Error saving layout", 2000)

    def load_layout(self):
        """Load layout from settings"""
        try:
            settings = QSettings('Epinnox', 'TradingInterface')

            # Restore main splitter state
            if hasattr(self, 'main_splitter'):
                state = settings.value('main_splitter')
                if state:
                    self.main_splitter.restoreState(state)

            # Restore individual column splitter states
            if hasattr(self, 'left_splitter'):
                state = settings.value('left_splitter')
                if state:
                    self.left_splitter.restoreState(state)
            if hasattr(self, 'middle_splitter'):
                state = settings.value('middle_splitter')
                if state:
                    self.middle_splitter.restoreState(state)
            if hasattr(self, 'right_splitter'):
                state = settings.value('right_splitter')
                if state:
                    self.right_splitter.restoreState(state)

            # Restore window geometry
            geometry = settings.value('geometry')
            if geometry:
                self.restoreGeometry(geometry)
            window_state = settings.value('windowState')
            if window_state:
                self.restoreState(window_state)

            self.statusBar().showMessage("Layout loaded successfully", 2000)
            print("✓ Layout loaded from settings")

        except Exception as e:
            print(f"Error loading layout: {e}")
            self.statusBar().showMessage("Error loading layout", 2000)

    def reset_layout(self):
        """Reset to default layout"""
        try:
            # Reset main splitter to default sizes
            if hasattr(self, 'main_splitter'):
                self.main_splitter.setSizes([400, 450, 600])

            # Reset column splitters to default sizes
            if hasattr(self, 'left_splitter'):
                self.left_splitter.setSizes([120, 100, 300, 150])
            if hasattr(self, 'middle_splitter'):
                self.middle_splitter.setSizes([150, 200, 150, 200])
            if hasattr(self, 'right_splitter'):
                self.right_splitter.setSizes([250, 400])

            # Reset window size
            self.setGeometry(50, 50, 1400, 900)

            self.statusBar().showMessage("Layout reset to default", 2000)
            print("✓ Layout reset to default")

        except Exception as e:
            print(f"Error resetting layout: {e}")
            self.statusBar().showMessage("Error resetting layout", 2000)

    def show_model_settings(self):
        """Show model selection settings dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

            dialog = QDialog(self)
            dialog.setWindowTitle("Model Selection Settings")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout(dialog)

            # Add model selector widget to dialog
            if hasattr(self, 'model_selector') and self.model_selector:
                # Move existing model selector to dialog temporarily
                model_widget = self.model_selector
            elif ModelSelectorWidget:
                # Create new model selector for dialog
                model_widget = ModelSelectorWidget()
                model_widget.model_switch_requested.connect(self.on_model_switch_requested)
                model_widget.refresh_requested.connect(self.on_model_refresh_requested)
            else:
                # Fallback label
                model_widget = QLabel("Model selector not available")

            layout.addWidget(model_widget)

            # Buttons
            button_layout = QHBoxLayout()
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing model settings: {e}")

    def show_preferences(self):
        """Show preferences dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QCheckBox

            dialog = QDialog(self)
            dialog.setWindowTitle("Preferences")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # Add some preference options
            layout.addWidget(QLabel("Trading Preferences:"))

            auto_trade_cb = QCheckBox("Enable Auto Trading")
            layout.addWidget(auto_trade_cb)

            debug_mode_cb = QCheckBox("Debug Mode")
            layout.addWidget(debug_mode_cb)

            layout.addStretch()

            # Buttons
            button_layout = QHBoxLayout()
            save_btn = QPushButton("Save")
            cancel_btn = QPushButton("Cancel")
            save_btn.clicked.connect(dialog.accept)
            cancel_btn.clicked.connect(dialog.reject)
            button_layout.addStretch()
            button_layout.addWidget(save_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
                QCheckBox {{
                    color: {MatrixTheme.TEXT};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing preferences: {e}")

    def show_about(self):
        """Show about dialog"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
            from PyQt5.QtCore import Qt

            dialog = QDialog(self)
            dialog.setWindowTitle("About Epinnox")
            dialog.setModal(True)
            dialog.resize(500, 350)

            layout = QVBoxLayout(dialog)

            # Title
            title = QLabel("Epinnox v6 Trading System")
            title.setAlignment(Qt.AlignCenter)
            title.setStyleSheet(f"""
                font-size: 18px;
                font-weight: bold;
                color: {MatrixTheme.GREEN};
                margin: 10px;
            """)
            layout.addWidget(title)

            # Description
            description = QLabel("""
            Advanced AI-Powered Trading System

            Features:
            • Real-time market data analysis
            • ML/LLM signal integration
            • Dynamic leverage management
            • Professional trading interface
            • Comprehensive risk management

            Combining numerical analysis with language models
            for intelligent trading decisions.
            """)
            description.setAlignment(Qt.AlignCenter)
            description.setWordWrap(True)
            layout.addWidget(description)

            layout.addStretch()

            # Close button
            button_layout = QHBoxLayout()
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            layout.addLayout(button_layout)

            # Apply Matrix theme
            dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {MatrixTheme.BACKGROUND};
                    color: {MatrixTheme.TEXT};
                }}
                QPushButton {{
                    background-color: {MatrixTheme.DARK_GREEN};
                    color: {MatrixTheme.GREEN};
                    border: 1px solid {MatrixTheme.GREEN};
                    padding: 5px 15px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {MatrixTheme.MID_GREEN};
                }}
                QLabel {{
                    color: {MatrixTheme.TEXT};
                }}
            """)

            dialog.exec_()

        except Exception as e:
            print(f"Error showing about dialog: {e}")

    def update_time_display(self):
        """Update the time display in the menu bar"""
        try:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.time_label.setText(current_time)
        except Exception as e:
            print(f"Error updating time display: {e}")
    
    def create_content(self):
        """Create main content area with resizable splitters"""
        # Main horizontal splitter
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setChildrenCollapsible(False)  # Prevent panels from collapsing completely

        # Left column - Controls and Analysis
        self.left_splitter = self.create_left_column()
        self.main_splitter.addWidget(self.left_splitter)

        # Middle column - Analysis Results
        self.middle_splitter = self.create_middle_column()
        self.main_splitter.addWidget(self.middle_splitter)

        # Right column - Chart and Trading
        self.right_splitter = self.create_right_column()
        self.main_splitter.addWidget(self.right_splitter)

        # Set initial sizes (proportional)
        self.main_splitter.setSizes([400, 450, 600])  # Left, Middle, Right widths

        # Style the splitter
        self.main_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.MID_GREEN};
                width: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.GREEN};
            }}
        """)

        # Create container widget
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.main_splitter)

        return container

    def create_left_column(self):
        """Create left column with resizable panels"""
        # Create vertical splitter for left column panels
        left_splitter = QSplitter(Qt.Vertical)
        left_splitter.setChildrenCollapsible(False)

        # Symbol selection and controls
        symbol_panel = self.create_symbol_panel()
        left_splitter.addWidget(symbol_panel)

        # Current analysis
        current_analysis_panel = self.create_current_analysis_panel()
        left_splitter.addWidget(current_analysis_panel)

        # LLM Analysis (expanded)
        llm_panel = self.create_llm_analysis_panel()
        left_splitter.addWidget(llm_panel)

        # Risk Warnings (compact)
        risk_warnings_panel = self.create_risk_warnings_panel()
        left_splitter.addWidget(risk_warnings_panel)

        # Set initial sizes for left column panels (more space for LLM analysis)
        left_splitter.setSizes([100, 80, 450, 120])  # Symbol, Current, LLM (expanded), Risk

        # Style the vertical splitter
        left_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return left_splitter

    def create_middle_column(self):
        """Create middle column with resizable panels"""
        # Create vertical splitter for middle column panels
        middle_splitter = QSplitter(Qt.Vertical)
        middle_splitter.setChildrenCollapsible(False)

        # ML Models Status (compact table)
        ml_models_panel = self.create_ml_models_panel()
        middle_splitter.addWidget(ml_models_panel)

        # Signal Hierarchy Analysis
        signal_hierarchy_panel = self.create_signal_hierarchy_panel()
        middle_splitter.addWidget(signal_hierarchy_panel)

        # Market Analysis (compact)
        market_analysis_panel = self.create_market_analysis_panel()
        middle_splitter.addWidget(market_analysis_panel)

        # Analysis Log (expandable)
        analysis_log_panel = self.create_analysis_log_panel()
        middle_splitter.addWidget(analysis_log_panel)

        # Set initial sizes for middle column panels
        middle_splitter.setSizes([150, 200, 150, 200])  # ML, Signals, Market, Log

        # Style the vertical splitter
        middle_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return middle_splitter

    def create_right_column(self):
        """Create right column with resizable panels"""
        # Create vertical splitter for right column panels
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setChildrenCollapsible(False)

        # Manual Trading Controls (compact)
        trading_panel = self.create_manual_trading_panel()
        right_splitter.addWidget(trading_panel)

        # Chart Panel (expandable)
        chart_panel = self.create_chart_panel()
        right_splitter.addWidget(chart_panel)

        # Set initial sizes for right column panels
        right_splitter.setSizes([250, 400])  # Trading, Chart

        # Style the vertical splitter
        right_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {MatrixTheme.DARK_GREEN};
                height: 3px;
                margin: 2px;
            }}
            QSplitter::handle:hover {{
                background-color: {MatrixTheme.MID_GREEN};
            }}
        """)

        return right_splitter

    def create_symbol_panel(self):
        """Create symbol selection panel"""
        group = QGroupBox("Symbol Selection")
        layout = QVBoxLayout(group)

        # Trading Symbol
        symbol_layout = QHBoxLayout()
        symbol_layout.addWidget(QLabel("Trading Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(["DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", "ADA/USDT:USDT", "SOL/USDT:USDT"])
        symbol_layout.addWidget(self.symbol_combo)
        layout.addLayout(symbol_layout)

        # Checkboxes
        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.live_data_checkbox.setChecked(True)
        layout.addWidget(self.live_data_checkbox)

        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (30s)")
        self.auto_refresh_checkbox.setChecked(True)
        layout.addWidget(self.auto_refresh_checkbox)

        # Buttons
        self.analyze_button = QPushButton("ANALYZE SYMBOL")
        self.analyze_button.clicked.connect(self.start_analysis)
        layout.addWidget(self.analyze_button)

        self.stop_button = QPushButton("STOP ANALYSIS")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_analysis)
        layout.addWidget(self.stop_button)

        return group

    def create_current_analysis_panel(self):
        """Create current analysis panel"""
        group = QGroupBox("Current Analysis")
        layout = QVBoxLayout(group)

        self.decision_label = QLabel("Decision: WAIT")
        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.decision_label)

        self.confidence_label = QLabel("Confidence: 86.0%")
        layout.addWidget(self.confidence_label)

        self.last_update_label = QLabel("Last Update: 19:32:58")
        layout.addWidget(self.last_update_label)

        return group

    def create_ml_models_panel(self):
        """Create ML models status panel"""
        group = QGroupBox("ML Models Status")
        layout = QVBoxLayout(group)

        # Create table with 4 columns (added Actual Confidence)
        self.ml_models_table = QTableWidget(3, 4)
        # ─ Stretch columns ─
        header = self.ml_models_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # ─ Transparent viewport & dark scrollbar ─
        self.ml_models_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.ml_models_table.setHorizontalHeaderLabels(["Model", "Decision", "Confidence", "Actual Confidence"])
        self.ml_models_table.verticalHeader().setVisible(False)

        # Make table fill the entire container height
        self.ml_models_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.ml_models_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.ml_models_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Add sample data with actual confidence
        models_data = [
            ["SVM", "WAIT", "58.2%", "--"],
            ["Random Forest", "LONG", "72.1%", "--"],
            ["LSTM", "WAIT", "61.4%", "--"]
        ]

        for row, (model, decision, confidence, actual_confidence) in enumerate(models_data):
            self.ml_models_table.setItem(row, 0, QTableWidgetItem(model))

            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)

            self.ml_models_table.setItem(row, 2, QTableWidgetItem(confidence))

            # Add actual confidence column with placeholder
            actual_conf_item = QTableWidgetItem(actual_confidence)
            actual_conf_item.setForeground(QColor(MatrixTheme.GRAY))
            self.ml_models_table.setItem(row, 3, actual_conf_item)

        layout.addWidget(self.ml_models_table)

        return group

    def create_llm_analysis_panel(self):
        """Create LLM analysis panel with expanded text area"""
        group = QGroupBox("LLM Analysis")
        layout = QVBoxLayout(group)
        layout.setSpacing(8)  # Reduce spacing for more text area

        # LLM Decision
        self.llm_decision_label = QLabel("LLM Decision: WAIT")
        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.YELLOW};
            padding: 5px;
        """)
        layout.addWidget(self.llm_decision_label)

        # LLM Confidence
        self.llm_confidence_label = QLabel("LLM Confidence: 75.0%")
        layout.addWidget(self.llm_confidence_label)

        # LLM Reasoning (scrollable)
        reasoning_label = QLabel("LLM Reasoning:")
        layout.addWidget(reasoning_label)

        self.llm_reasoning_text = QTextEdit()
        self.llm_reasoning_text.setReadOnly(True)
        self.llm_reasoning_text.setMaximumHeight(200)  # Increased from 100
        self.llm_reasoning_text.setMinimumHeight(120)  # Increased from 60
        self.llm_reasoning_text.setWordWrapMode(QTextOption.WordWrap)

        # Ensure it expands to fill available space
        self.llm_reasoning_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Apply Matrix theme scrollbar styling
        self.llm_reasoning_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        self.llm_reasoning_text.setText("Market shows mixed signals. Technical indicators suggest consolidation while volume patterns indicate potential breakout. Recommend WAIT for clearer direction.")
        layout.addWidget(self.llm_reasoning_text)

        # Comprehensive Analysis Panel
        comprehensive_group = QGroupBox("🎭 Comprehensive Creative Analysis")
        comprehensive_layout = QVBoxLayout(comprehensive_group)

        # Comprehensive decision display
        self.comprehensive_decision_label = QLabel("Comprehensive Decision: Analyzing...")
        self.comprehensive_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 5px;
            border: 1px solid {MatrixTheme.GREEN};
            border-radius: 5px;
            background-color: {MatrixTheme.BLACK};
        """)
        comprehensive_layout.addWidget(self.comprehensive_decision_label)

        # Creative insight display
        self.creative_insight_text = QTextEdit()
        self.creative_insight_text.setMaximumHeight(120)
        self.creative_insight_text.setMinimumHeight(80)
        self.creative_insight_text.setReadOnly(True)
        self.creative_insight_text.setWordWrapMode(QTextOption.WordWrap)
        self.creative_insight_text.setPlaceholderText("Creative market insights will appear here...")
        self.creative_insight_text.setStyleSheet(f"""
            background-color: {MatrixTheme.BLACK};
            color: {MatrixTheme.GREEN};
            border: 1px solid {MatrixTheme.DARK_GREEN};
            font-family: {MatrixTheme.FONT_FAMILY};
            font-size: {MatrixTheme.FONT_SIZE_SMALL}px;
            padding: 5px;
        """)
        comprehensive_layout.addWidget(self.creative_insight_text)

        layout.addWidget(comprehensive_group)

        return group

    def create_leverage_panel(self):
        """Create leverage analysis panel"""
        group = QGroupBox("Leverage Analysis")
        layout = QVBoxLayout(group)

        self.max_leverage_label = QLabel("Max Available: 1.0x")
        layout.addWidget(self.max_leverage_label)

        self.recommended_leverage_label = QLabel("Recommended: 1.0x")
        layout.addWidget(self.recommended_leverage_label)

        self.effective_leverage_label = QLabel("Effective: 0.4x")
        layout.addWidget(self.effective_leverage_label)

        self.position_size_label = QLabel("Position Size: 0.00 units ($0.00)")
        layout.addWidget(self.position_size_label)

        self.risk_per_trade_label = QLabel("Risk per Trade: $0.00")
        layout.addWidget(self.risk_per_trade_label)

        return group
    
    def create_signal_hierarchy_panel(self):
        """Create signal hierarchy analysis panel"""
        group = QGroupBox("Signal Hierarchy Analysis")
        layout = QVBoxLayout(group)

        # Create table
        self.signal_hierarchy_table = QTableWidget(4, 4)
        # ─ Stretch columns to fill the box ─
        header = self.signal_hierarchy_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # ─ Make viewport & scrollbars dark/transparent ─
        self.signal_hierarchy_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: transparent;
                gridline-color: {MatrixTheme.DARK_GREEN};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QTableWidget::item {{
                background-color: transparent;
                color: {MatrixTheme.TEXT};
                border-bottom: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QHeaderView::section {{
                background-color: {MatrixTheme.DARK_GREEN};
                color: {MatrixTheme.GREEN};
                padding: 5px;
                font-weight: bold;
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
        """)

        self.signal_hierarchy_table.setHorizontalHeaderLabels(["Source", "Decision", "Confidence", "Weight"])
        self.signal_hierarchy_table.verticalHeader().setVisible(False)

        # Make table fill the entire container height
        self.signal_hierarchy_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.signal_hierarchy_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.signal_hierarchy_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Add sample data
        signals_data = [
            ["ml_ensemble", "WAIT", "68.7%", "0.3"],
            ["llm_analysis", "WAIT", "75.0%", "0.3"],
            ["technical_signals", "WAIT", "83.0%", "0.2"],
            ["multi_timeframe", "SHORT", "0.0%", "0.2"]
        ]

        for row, (source, decision, confidence, weight) in enumerate(signals_data):
            self.signal_hierarchy_table.setItem(row, 0, QTableWidgetItem(source))

            decision_item = QTableWidgetItem(decision)
            if decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.signal_hierarchy_table.setItem(row, 1, decision_item)

            self.signal_hierarchy_table.setItem(row, 2, QTableWidgetItem(confidence))
            self.signal_hierarchy_table.setItem(row, 3, QTableWidgetItem(weight))

        layout.addWidget(self.signal_hierarchy_table)

        return group

    def create_market_analysis_panel(self):
        """Create market analysis panel"""
        group = QGroupBox("Market Analysis")
        layout = QVBoxLayout(group)

        self.market_regime_label = QLabel("Market Regime: STRONG_TREND")
        layout.addWidget(self.market_regime_label)

        self.trend_strength_label = QLabel("Trend Strength: 0.00")
        layout.addWidget(self.trend_strength_label)

        self.volatility_label = QLabel("Volatility: 0.00%")
        layout.addWidget(self.volatility_label)

        self.liquidity_score_label = QLabel("Liquidity Score: --")
        layout.addWidget(self.liquidity_score_label)

        return group

    def create_analysis_log_panel(self):
        """Create analysis log panel that fills entire container"""
        group = QGroupBox("Analysis Log")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(2, 2, 2, 2)  # Minimal margins
        layout.setSpacing(2)  # Minimal spacing

        self.analysis_log = QTextEdit()
        self.analysis_log.setReadOnly(True)
        # Remove height constraints to allow full expansion
        # self.analysis_log.setMaximumHeight(180)  # Removed
        # self.analysis_log.setMinimumHeight(120)  # Removed
        self.analysis_log.setWordWrapMode(QTextOption.WordWrap)

        # Ensure it fills the entire container
        self.analysis_log.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Apply Matrix theme scrollbar styling
        self.analysis_log.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        # Add sample log entries
        sample_logs = [
            "[19:20:02] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:20:19] Started analysis for DOGE/USDT:USDT",
            "[19:20:43] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:20:56] Started analysis for DOGE/USDT:USDT",
            "[19:21:00] Analysis complete for DOGE/USDT:USDT: LONG",
            "[19:21:17] Started analysis for DOGE/USDT:USDT",
            "[19:21:31] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:21:45] Started analysis for DOGE/USDT:USDT",
            "[19:21:56] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:22:11] Started analysis for DOGE/USDT:USDT",
            "[19:22:31] Analysis complete for DOGE/USDT:USDT: LONG",
            "[19:22:45] Started analysis for DOGE/USDT:USDT",
            "[19:23:01] Analysis complete for DOGE/USDT:USDT: WAIT",
            "[19:23:16] Started analysis for DOGE/USDT:USDT"
        ]

        for log_entry in sample_logs:
            self.analysis_log.append(log_entry)

        layout.addWidget(self.analysis_log)

        return group

    def create_risk_warnings_panel(self):
        """Create comprehensive risk warnings panel"""
        group = QGroupBox("Risk Management & Warnings")
        layout = QVBoxLayout(group)

        # Risk Metrics
        metrics_layout = QGridLayout()

        # Row 1: Portfolio Risk
        metrics_layout.addWidget(QLabel("Portfolio Risk:"), 0, 0)
        self.portfolio_risk_label = QLabel("2.5%")
        self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.portfolio_risk_label, 0, 1)

        # Row 2: Max Drawdown
        metrics_layout.addWidget(QLabel("Max Drawdown:"), 1, 0)
        self.max_drawdown_label = QLabel("5.2%")
        self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.max_drawdown_label, 1, 1)

        # Row 3: Correlation Risk
        metrics_layout.addWidget(QLabel("Correlation Risk:"), 2, 0)
        self.correlation_risk_label = QLabel("LOW")
        self.correlation_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        metrics_layout.addWidget(self.correlation_risk_label, 2, 1)

        # Row 4: Liquidity Risk
        metrics_layout.addWidget(QLabel("Liquidity Risk:"), 3, 0)
        self.liquidity_risk_label = QLabel("MEDIUM")
        self.liquidity_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        metrics_layout.addWidget(self.liquidity_risk_label, 3, 1)

        layout.addLayout(metrics_layout)

        # Active Warnings
        warnings_label = QLabel("Active Warnings:")
        warnings_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold; font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;")
        layout.addWidget(warnings_label)

        self.risk_warnings_log = QTextEdit()
        self.risk_warnings_log.setReadOnly(True)
        self.risk_warnings_log.setMaximumHeight(100)

        # Apply Matrix theme scrollbar styling
        self.risk_warnings_log.setStyleSheet(f"""
            QTextEdit {{
                background-color: {MatrixTheme.BACKGROUND};
                color: {MatrixTheme.TEXT};
                border: 1px solid {MatrixTheme.DARK_GREEN};
            }}
            QScrollBar:vertical {{
                background: transparent;
                width: 12px;
            }}
            QScrollBar::handle:vertical {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
            }}
            QScrollBar:horizontal {{
                background: transparent;
                height: 12px;
            }}
            QScrollBar::handle:horizontal {{
                background: {MatrixTheme.DARK_GREEN};
                border-radius: 6px;
            }}
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                background: none;
                width: 0px;
            }}
        """)

        # Add comprehensive sample warnings
        sample_warnings = [
            "⚠️ HIGH VOLATILITY: 24h volatility >5% - reduce position size",
            "⚠️ LOW LIQUIDITY: Order book depth <$50k - limit order size",
            "⚠️ CORRELATION ALERT: 0.85 correlation with BTC - diversify",
            "⚠️ LEVERAGE WARNING: Current 3.2x exceeds recommended 2.5x"
        ]

        for warning in sample_warnings:
            self.risk_warnings_log.append(warning)

        layout.addWidget(self.risk_warnings_log)

        return group

    def create_manual_trading_panel(self):
        """Create compact manual trading controls panel"""
        group = QGroupBox("Manual Trading")
        layout = QVBoxLayout(group)
        layout.setSpacing(4)  # Reduce spacing
        layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins

        # Trading parameters - more compact
        params_layout = QGridLayout()
        params_layout.setSpacing(3)  # Reduce grid spacing

        # Quantity - smaller spinbox
        params_layout.addWidget(QLabel("Quantity:"), 0, 0)
        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setRange(0.0001, 100000)
        self.quantity_spinbox.setDecimals(4)
        self.quantity_spinbox.setValue(50.0)
        self.quantity_spinbox.setMaximumHeight(25)  # Make more compact
        params_layout.addWidget(self.quantity_spinbox, 0, 1)

        # Leverage - smaller spinbox
        params_layout.addWidget(QLabel("Leverage:"), 1, 0)
        self.leverage_spinbox = QSpinBox()
        self.leverage_spinbox.setRange(1, 125)
        self.leverage_spinbox.setValue(20)
        self.leverage_spinbox.setMaximumHeight(25)  # Make more compact
        params_layout.addWidget(self.leverage_spinbox, 1, 1)

        # Compact Bid/Ask display
        params_layout.addWidget(QLabel("Best Bid:"), 2, 0)
        self.best_bid_label = QLabel("--")
        self.best_bid_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.GREEN};
                font-weight: bold;
                font-size: 12px;
                padding: 2px;
                background-color: rgba(0, 255, 68, 0.1);
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 2px;
                max-height: 20px;
            }}
        """)
        params_layout.addWidget(self.best_bid_label, 2, 1)

        params_layout.addWidget(QLabel("Best Ask:"), 3, 0)
        self.best_ask_label = QLabel("--")
        self.best_ask_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.RED};
                font-weight: bold;
                font-size: 12px;
                padding: 2px;
                background-color: rgba(255, 0, 0, 0.1);
                border: 1px solid {MatrixTheme.RED};
                border-radius: 2px;
                max-height: 20px;
            }}
        """)
        params_layout.addWidget(self.best_ask_label, 3, 1)

        # Compact spread display
        params_layout.addWidget(QLabel("Spread:"), 4, 0)
        self.spread_label = QLabel("--")
        self.spread_label.setStyleSheet(f"""
            QLabel {{
                color: {MatrixTheme.YELLOW};
                font-weight: bold;
                padding: 2px;
                font-size: 11px;
                max-height: 18px;
            }}
        """)
        params_layout.addWidget(self.spread_label, 4, 1)

        # Price input for limit orders
        params_layout.addWidget(QLabel("Limit Price:"), 5, 0)
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.000001, 999999)
        self.price_spinbox.setDecimals(6)
        self.price_spinbox.setValue(0.175)
        self.price_spinbox.setToolTip("Price for limit orders (auto-filled from bid/ask)")
        params_layout.addWidget(self.price_spinbox, 5, 1)

        # Auto-fill price buttons
        price_buttons_layout = QHBoxLayout()

        fill_bid_btn = QPushButton("Use Bid")
        fill_bid_btn.setMaximumWidth(60)
        fill_bid_btn.clicked.connect(self.fill_bid_price)
        fill_bid_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.GREEN};
                color: {MatrixTheme.BLACK};
                font-size: 10px;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)

        fill_ask_btn = QPushButton("Use Ask")
        fill_ask_btn.setMaximumWidth(60)
        fill_ask_btn.clicked.connect(self.fill_ask_price)
        fill_ask_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {MatrixTheme.RED};
                color: {MatrixTheme.BLACK};
                font-size: 10px;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: bold;
            }}
        """)

        price_buttons_layout.addWidget(fill_bid_btn)
        price_buttons_layout.addWidget(fill_ask_btn)
        price_buttons_layout.addStretch()

        params_layout.addLayout(price_buttons_layout, 6, 0, 1, 2)

        # Trading mode status
        params_layout.addWidget(QLabel("Mode:"), 7, 0)
        mode_text = "DEMO" if demo_mode else "LIVE"
        mode_color = MatrixTheme.YELLOW if demo_mode else MatrixTheme.RED
        self.mode_label = QLabel(mode_text)
        self.mode_label.setStyleSheet(f"color: {mode_color}; font-weight: bold; font-size: 12px;")
        params_layout.addWidget(self.mode_label, 7, 1)

        # Initialize bid/ask tracking
        self.current_bid = None
        self.current_ask = None
        self.last_bid = None
        self.last_ask = None

        layout.addLayout(params_layout)

        # Compact trading buttons
        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(2)  # Reduce button spacing

        # Long buttons (green) - more compact
        self.limit_long_btn = QPushButton("LIMIT LONG")
        self.limit_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #006600;
                color: white;
                font-weight: bold;
                padding: 4px;
                border: 1px solid {MatrixTheme.GREEN};
                border-radius: 2px;
                font-size: 11px;
                max-height: 28px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.limit_long_btn.clicked.connect(self.place_limit_long)
        buttons_layout.addWidget(self.limit_long_btn, 0, 0)

        self.market_long_btn = QPushButton("MARKET LONG")
        self.market_long_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #009900;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GREEN};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GREEN};
                color: black;
            }}
        """)
        self.market_long_btn.clicked.connect(self.place_market_long)
        buttons_layout.addWidget(self.market_long_btn, 0, 1)

        # Short buttons (red)
        self.limit_short_btn = QPushButton("LIMIT SHORT")
        self.limit_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #660000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.limit_short_btn.clicked.connect(self.place_limit_short)
        buttons_layout.addWidget(self.limit_short_btn, 1, 0)

        self.market_short_btn = QPushButton("MARKET SHORT")
        self.market_short_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #990000;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.RED};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.RED};
                color: white;
            }}
        """)
        self.market_short_btn.clicked.connect(self.place_market_short)
        buttons_layout.addWidget(self.market_short_btn, 1, 1)

        # Control buttons
        self.close_all_btn = QPushButton("CLOSE ALL")
        self.close_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #ff6600;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.YELLOW};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.YELLOW};
                color: black;
            }}
        """)
        self.close_all_btn.clicked.connect(self.close_all_positions)
        buttons_layout.addWidget(self.close_all_btn, 2, 0)

        self.cancel_all_btn = QPushButton("CANCEL ALL")
        self.cancel_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #666666;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: 2px solid {MatrixTheme.GRAY};
                border-radius: 3px;
            }}
            QPushButton:hover {{
                background-color: {MatrixTheme.GRAY};
                color: white;
            }}
        """)
        self.cancel_all_btn.clicked.connect(self.cancel_all_orders)
        buttons_layout.addWidget(self.cancel_all_btn, 2, 1)

        layout.addLayout(buttons_layout)

        return group

    def create_chart_panel(self):
        """Create interactive chart panel with live data integration"""
        group = QGroupBox("Live Chart")
        layout = QVBoxLayout(group)

        # Import and create live chart widget
        try:
            from gui.charts.live_chart_widget import LiveChartWidget

            # Create live chart widget
            self.live_chart = LiveChartWidget()
            self.live_chart.setMinimumHeight(400)

            # Connect chart signals
            self.live_chart.chart_clicked.connect(self.on_live_chart_click)
            self.live_chart.symbol_changed.connect(self.on_chart_symbol_changed)
            self.live_chart.timeframe_changed.connect(self.on_chart_timeframe_changed)

            layout.addWidget(self.live_chart)

            # Initialize live data manager
            self.setup_live_data_manager()

            # Initialize real trading interface
            self.setup_real_trading_interface()

            # Initialize LMStudio runner for dynamic model switching
            self.setup_lmstudio_runner()

            # Initialize signal trading engine
            self.setup_signal_trading_engine()

            # Initialize session management
            self.setup_session_management()

        except ImportError as e:
            print(f"Could not import LiveChartWidget: {e}")
            # Fallback to simple chart
            layout.addWidget(self.create_fallback_chart())

        return group

    def create_fallback_chart(self):
        """Create fallback chart if live chart widget is not available"""
        # Chart controls
        controls_layout = QHBoxLayout()

        # Timeframe selector
        controls_layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText("1m")
        self.timeframe_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.timeframe_combo)

        # Chart type selector
        controls_layout.addWidget(QLabel("Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Line", "Candlestick"])
        self.chart_type_combo.setCurrentText("Line")
        self.chart_type_combo.currentTextChanged.connect(self.update_chart)
        controls_layout.addWidget(self.chart_type_combo)

        controls_layout.addStretch()

        fallback_widget = QWidget()
        fallback_layout = QVBoxLayout(fallback_widget)
        fallback_layout.addLayout(controls_layout)

        # Create PyQtGraph chart
        self.chart_widget = pg.PlotWidget(
            background='#000000',
            enableMenu=False
        )
        self.chart_widget.setMinimumHeight(300)
        self.chart_widget.showGrid(x=True, y=True, alpha=0.3)
        self.chart_widget.setLabel('left', 'Price', color=MatrixTheme.GREEN)
        self.chart_widget.setLabel('bottom', 'Time', color=MatrixTheme.GREEN)

        # Connect chart click event for order placement
        self.chart_widget.scene().sigMouseClicked.connect(self.on_chart_click)

        fallback_layout.addWidget(self.chart_widget)

        # Chart instructions
        instructions = QLabel("💡 Left-click: BUY order | Right-click: SELL order")
        instructions.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: 10px; padding: 5px;")
        fallback_layout.addWidget(instructions)

        # Initialize chart with sample data
        self.update_chart()

        return fallback_widget

    def setup_live_data_manager(self):
        """Setup live data manager for real-time chart updates"""
        try:
            # Check if already initialized to prevent multiple instances
            if hasattr(self, 'live_data_manager') and self.live_data_manager is not None:
                print("✓ Live data manager already initialized")
                return

            from data.live_data_manager import LiveDataManager

            # Create live data manager
            self.live_data_manager = LiveDataManager("htx")

            # Connect signals
            self.live_data_manager.chart_data_updated.connect(self.on_live_chart_data_updated)
            self.live_data_manager.price_updated.connect(self.on_live_price_updated)
            self.live_data_manager.orderbook_updated.connect(self.on_live_orderbook_updated)
            self.live_data_manager.connection_status_changed.connect(self.on_live_connection_status)

            # Connect WebSocket trade updates directly to chart
            self.live_data_manager.ws_client.trade_update.connect(self.on_trade_update)

            # Subscribe to current symbol
            current_symbol = self.symbol_combo.currentText()
            self.live_data_manager.subscribe_symbol(current_symbol, ["1m", "5m", "15m"])

            # Connect to live data
            self.live_data_manager.connect()

            print("✓ Live data manager initialized")

        except ImportError as e:
            print(f"Could not import LiveDataManager: {e}")
            self.live_data_manager = None

    def setup_real_trading_interface(self):
        """Setup real trading interface for actual order execution"""
        try:
            from trading.real_trading_interface import RealTradingInterface
            from ml.prediction_accuracy_tracker import PredictionAccuracyTracker

            # Create real trading interface with LIVE trading enabled
            self.real_trading = RealTradingInterface("htx", demo_mode=False)  # Use REAL trading with API keys

            # Create prediction accuracy tracker
            self.prediction_tracker = PredictionAccuracyTracker(evaluation_window_minutes=5)

            # Connect signals
            self.real_trading.order_status_updated.connect(self.on_order_status_updated)
            self.real_trading.position_status_updated.connect(self.on_position_status_updated)
            self.real_trading.balance_status_updated.connect(self.on_balance_status_updated)
            self.real_trading.trading_error.connect(self.on_trading_error)
            self.real_trading.trading_status.connect(self.on_trading_status)
            self.real_trading.pnl_updated.connect(self.on_pnl_updated)
            self.real_trading.risk_warning.connect(self.on_risk_warning)

            # Set current symbol
            current_symbol = self.symbol_combo.currentText()
            self.real_trading.set_current_symbol(current_symbol)

            print("✓ Real trading interface initialized")

        except ImportError as e:
            print(f"Could not import RealTradingInterface: {e}")
            self.real_trading = None

    def setup_signal_trading_engine(self):
        """Setup signal trading engine for automated trading"""
        try:
            from trading.signal_trading_engine import SignalTradingEngine

            if hasattr(self, 'real_trading') and self.real_trading:
                # Create signal trading engine
                self.signal_trading = SignalTradingEngine(self.real_trading)

                # Connect signals
                self.signal_trading.signal_received.connect(self.on_signal_received)
                self.signal_trading.trade_decision_made.connect(self.on_trade_decision_made)
                self.signal_trading.automated_trade_executed.connect(self.on_automated_trade_executed)
                self.signal_trading.risk_limit_triggered.connect(self.on_risk_limit_triggered)
                self.signal_trading.engine_status_changed.connect(self.on_engine_status_changed)

                print("✓ Signal trading engine initialized")
            else:
                print("⚠ Real trading interface not available for signal trading")
                self.signal_trading = None

        except ImportError as e:
            print(f"Could not import SignalTradingEngine: {e}")
            self.signal_trading = None

    def setup_session_management(self):
        """Setup session management and persistence"""
        try:
            from storage.database_manager import DatabaseManager
            from storage.session_manager import SessionManager, TradeRecorder

            # Create database manager
            self.db_manager = DatabaseManager()

            # Create session manager
            self.session_manager = SessionManager(self.db_manager)

            # Create trade recorder
            self.trade_recorder = TradeRecorder(self.session_manager)

            # Connect signals
            self.session_manager.session_started.connect(self.on_session_started)
            self.session_manager.session_ended.connect(self.on_session_ended)
            self.session_manager.trade_recorded.connect(self.on_trade_recorded_to_db)
            self.session_manager.signal_recorded.connect(self.on_signal_recorded_to_db)

            # Start a session automatically
            current_symbol = self.symbol_combo.currentText()
            session_id = self.session_manager.start_session(
                mode="demo",
                symbol=current_symbol,
                initial_balance=1000.0,
                configuration={
                    "leverage": self.leverage_spinbox.value(),
                    "base_position_size": self.quantity_spinbox.value(),
                    "auto_trading": False
                }
            )

            print("✓ Session management initialized")
            print(f"✓ Started session: {session_id}")

        except ImportError as e:
            print(f"Could not import session management: {e}")
            self.session_manager = None
            self.trade_recorder = None

    def setup_timers(self):
        """Setup update timers"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

        # Chart update timer
        self.chart_timer = QTimer()
        self.chart_timer.timeout.connect(self.update_chart)
        self.chart_timer.start(5000)  # Update chart every 5 seconds

        # Bid/Ask update timer (more frequent for real-time trading)
        self.bid_ask_timer = QTimer()
        self.bid_ask_timer.timeout.connect(self.update_bid_ask_display)
        self.bid_ask_timer.start(1000)  # Update bid/ask every 1 second
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def start_analysis(self):
        """Start trading analysis"""
        symbol = self.symbol_combo.currentText()
        use_live = self.live_data_checkbox.isChecked()

        self.is_analyzing = True
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("SYSTEM: ANALYZING")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")

        self.log_message(f"Starting analysis for {symbol} (Live: {use_live})")

        # Simulate analysis
        QTimer.singleShot(3000, self.complete_analysis)

    def stop_analysis(self):
        """Stop analysis"""
        self.is_analyzing = False

        # Stop any pending analysis timer
        if self.analysis_timer:
            self.analysis_timer.stop()
            self.analysis_timer = None

        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("SYSTEM: READY")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        self.log_message("Analysis stopped by user")

    def fill_bid_price(self):
        """Fill limit price with current best bid"""
        if self.current_bid is not None:
            self.price_spinbox.setValue(self.current_bid)
            self.log_message(f"Price set to best bid: {self.current_bid:.6f}")
        else:
            self.log_message("No bid price available")

    def fill_ask_price(self):
        """Fill limit price with current best ask"""
        if self.current_ask is not None:
            self.price_spinbox.setValue(self.current_ask)
            self.log_message(f"Price set to best ask: {self.current_ask:.6f}")
        else:
            self.log_message("No ask price available")

    def validate_order_inputs(self) -> tuple[bool, str]:
        """Validate order inputs before placement"""
        try:
            # Check quantity
            quantity = self.quantity_spinbox.value()
            if quantity <= 0:
                return False, "Quantity must be greater than 0"

            # Check leverage
            leverage = self.leverage_spinbox.value()
            if leverage < 1 or leverage > 125:
                return False, "Leverage must be between 1 and 125"

            # Check if bid/ask data is available for market orders
            if self.current_bid is None or self.current_ask is None:
                return False, "Market data not available. Please wait for price updates."

            return True, "Validation passed"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    def show_order_confirmation(self, side: str, order_type: str) -> bool:
        """Show order confirmation dialog"""
        try:
            symbol = self.symbol_combo.currentText()
            quantity = self.quantity_spinbox.value()
            leverage = self.leverage_spinbox.value()

            side_color = "🟢" if side == "BUY" else "🔴"
            type_text = "LIMIT" if order_type == "LIMIT" else "MARKET"

            msg = f"""
{side_color} {side} {type_text} ORDER

Symbol: {symbol}
Quantity: {quantity:.4f}
"""

            if order_type == "LIMIT":
                price = self.price_spinbox.value()
                msg += f"Price: {price:.6f}\n"
            else:
                msg += f"Price: MARKET (Best {'Ask' if side == 'BUY' else 'Bid'})\n"

            msg += f"Leverage: {leverage}x\n"

            # Add current market data
            if self.current_bid and self.current_ask:
                spread = self.current_ask - self.current_bid
                spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                msg += f"\nCurrent Market:\nBid: {self.current_bid:.6f}\nAsk: {self.current_ask:.6f}\nSpread: {spread:.6f} ({spread_pct:.3f}%)\n"

            msg += "\nDo you want to place this order?"

            reply = QMessageBox.question(
                self,
                "Confirm Order",
                msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            return reply == QMessageBox.Yes

        except Exception as e:
            self.log_message(f"Error in order confirmation: {e}")
            return False

    def complete_analysis(self):
        """Complete analysis using real system data"""
        decisions = ["LONG", "SHORT", "WAIT"]

        # Get real ML model decisions (placeholder for actual ML integration)
        ml_decisions = ["WAIT", "WAIT", "WAIT"]  # Default to WAIT until ML models are integrated
        ml_confidences = [60.0, 60.0, 60.0]  # Default confidence levels

        # Get real LLM decision from last analysis
        llm_decision = getattr(self, 'last_llm_decision', 'WAIT')
        llm_confidence = getattr(self, 'last_llm_confidence', 60.0)

        # Weighted decision making (ML ensemble: 30%, LLM: 30%, Technical: 20%, Multi-timeframe: 20%)
        decision_scores = {"LONG": 0, "SHORT": 0, "WAIT": 0}

        # ML ensemble contribution (30%)
        ml_ensemble_decision = max(set(ml_decisions), key=ml_decisions.count)  # Majority vote
        decision_scores[ml_ensemble_decision] += 0.3

        # LLM contribution (30%)
        decision_scores[llm_decision] += 0.3

        # Technical signals (20%) - get from actual technical analysis
        tech_decision = self.get_technical_signal()
        decision_scores[tech_decision] += 0.2

        # Multi-timeframe (20%) - get from actual multi-timeframe analysis
        mtf_decision = self.get_multi_timeframe_signal()
        decision_scores[mtf_decision] += 0.2

        # Final decision is the highest scored
        decision = max(decision_scores, key=decision_scores.get)
        confidence = decision_scores[decision] * 100  # Convert to percentage
        confidence = max(50, min(95, confidence))  # Clamp between 50-95%

        # Update current analysis panel
        self.decision_label.setText(f"Decision: {decision}")
        self.confidence_label.setText(f"Confidence: {confidence:.1f}%")
        self.last_update_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

        # Color code decision
        if decision == "LONG":
            color = MatrixTheme.GREEN
        elif decision == "SHORT":
            color = MatrixTheme.RED
        else:
            color = MatrixTheme.YELLOW

        self.decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {color};
            padding: 5px;
        """)

        # Update ML models with generated decisions and record predictions
        models_data = [
            ["SVM", ml_decisions[0], f"{ml_confidences[0]:.1f}%"],
            ["Random Forest", ml_decisions[1], f"{ml_confidences[1]:.1f}%"],
            ["LSTM", ml_decisions[2], f"{ml_confidences[2]:.1f}%"]
        ]

        # Get current price for prediction tracking
        current_symbol = self.symbol_combo.currentText()
        current_price = getattr(self, 'current_bid', None) or getattr(self, 'current_ask', None)
        if current_price is None:
            current_price = 0.35  # Fallback price

        for row, (model, ml_decision, ml_confidence) in enumerate(models_data):
            # Record prediction for accuracy tracking
            if hasattr(self, 'prediction_tracker'):
                confidence_float = ml_confidences[row] / 100.0  # Convert percentage to float
                self.prediction_tracker.record_prediction(
                    model_name=model,
                    prediction=ml_decision,
                    confidence=confidence_float,
                    price=current_price,
                    symbol=current_symbol
                )

            # Update table display
            decision_item = QTableWidgetItem(ml_decision)
            if ml_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif ml_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.ml_models_table.setItem(row, 1, decision_item)
            self.ml_models_table.setItem(row, 2, QTableWidgetItem(ml_confidence))

            # Update actual confidence column
            if hasattr(self, 'prediction_tracker'):
                actual_accuracy = self.prediction_tracker.get_model_accuracy(model)
                if actual_accuracy is not None:
                    actual_conf_text = f"{actual_accuracy:.1f}%"
                    actual_conf_item = QTableWidgetItem(actual_conf_text)

                    # Color code based on accuracy
                    if actual_accuracy >= 70:
                        actual_conf_item.setForeground(QColor(MatrixTheme.GREEN))
                    elif actual_accuracy >= 50:
                        actual_conf_item.setForeground(QColor(MatrixTheme.YELLOW))
                    else:
                        actual_conf_item.setForeground(QColor(MatrixTheme.RED))
                else:
                    actual_conf_item = QTableWidgetItem("--")
                    actual_conf_item.setForeground(QColor(MatrixTheme.GRAY))

                self.ml_models_table.setItem(row, 3, actual_conf_item)

        # Generate LLM analysis using dynamic LMStudio runner
        llm_decision = "WAIT"  # Default
        llm_confidence = 50.0  # Default
        llm_reasoning = "LLM analysis not available"

        # Try to use LMStudio runner if available
        if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
            try:
                current_model = self.lmstudio_runner.get_current_model()
                if current_model:
                    # Get current symbol
                    symbol = self.symbol_combo.currentText()

                    # Create market analysis prompt
                    prompt = f"""Analyze the current market conditions for {symbol}:

Current Price: {current_price:.6f}
ML Predictions: {ml_decisions}
ML Confidences: {[f'{c:.1f}%' for c in ml_confidences]}

Provide a trading recommendation (LONG/SHORT/WAIT) with reasoning.
Format: DECISION: [LONG/SHORT/WAIT] | CONFIDENCE: [0-100] | REASONING: [brief explanation]"""

                    # Log the analysis context for debugging
                    self.log_message(f"🔍 Starting LLM Analysis for {symbol}")
                    self.log_message(f"📊 Current Price: {current_price:.6f}")
                    self.log_message(f"🤖 ML Predictions: {ml_decisions}")
                    self.log_message(f"📈 ML Confidences: {[f'{c:.1f}%' for c in ml_confidences]}")
                    self.log_message(f"🎯 Using Model: {current_model}")

                    # Get LLM response
                    response = self.lmstudio_runner.run_inference(prompt, temperature=0.7, max_tokens=200)

                    # Log the raw response for debugging
                    self.log_message(f"📝 Raw LLM Response: {response[:300]}..." if response and len(response) > 300 else f"📝 Raw LLM Response: {response}")

                    # Parse response
                    if response and "DECISION:" in response:
                        parts = response.split("|")
                        if len(parts) >= 3:
                            decision_part = parts[0].split("DECISION:")[1].strip()
                            confidence_part = parts[1].split("CONFIDENCE:")[1].strip()
                            reasoning_part = parts[2].split("REASONING:")[1].strip()

                            self.log_message(f"🎯 Parsed Decision: {decision_part}")
                            self.log_message(f"📊 Parsed Confidence: {confidence_part}")
                            self.log_message(f"💭 Parsed Reasoning: {reasoning_part[:100]}...")

                            if decision_part in ["LONG", "SHORT", "WAIT"]:
                                llm_decision = decision_part

                            try:
                                llm_confidence = float(confidence_part)
                            except:
                                llm_confidence = 60.0  # Default confidence
                                self.log_message(f"⚠️ Could not parse confidence, using default: {llm_confidence:.1f}%")

                            llm_reasoning = reasoning_part[:200] + "..." if len(reasoning_part) > 200 else reasoning_part
                        else:
                            self.log_message(f"⚠️ Could not parse structured response, using raw response")
                            llm_reasoning = response[:200] + "..." if len(response) > 200 else response
                    else:
                        self.log_message(f"⚠️ No DECISION found in response, using fallback")
                        llm_reasoning = f"Model {current_model} response: " + (response[:100] + "..." if response and len(response) > 100 else response or "No response")

                    self.log_message(f"✅ Final LLM Analysis ({current_model}): {llm_decision} ({llm_confidence:.1f}%)")

                    # Now run the comprehensive second analysis with all data
                    self.run_comprehensive_analysis(symbol, current_price, ml_decisions, ml_confidences,
                                                   llm_decision, llm_confidence, llm_reasoning, current_model)
                else:
                    llm_reasoning = "No LMStudio model selected"

            except Exception as e:
                self.log_message(f"Error in LLM analysis: {e}")
                llm_reasoning = f"LLM analysis error: {str(e)[:100]}"
        else:
            # Fallback when no LLM available
            llm_decision = "WAIT"
            llm_confidence = 60.0
            llm_reasoning = "LLM analysis not available - using default WAIT signal"

        # Update LLM panel
        self.llm_decision_label.setText(f"LLM Decision: {llm_decision}")
        if llm_decision == "LONG":
            llm_color = MatrixTheme.GREEN
        elif llm_decision == "SHORT":
            llm_color = MatrixTheme.RED
        else:
            llm_color = MatrixTheme.YELLOW

        self.llm_decision_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_MEDIUM}px;
            font-weight: bold;
            color: {llm_color};
            padding: 5px;
        """)

        self.llm_confidence_label.setText(f"LLM Confidence: {llm_confidence:.1f}%")
        self.llm_reasoning_text.setText(llm_reasoning)

        # Update signal hierarchy (now includes LLM)
        signals_data = [
            ["ml_ensemble", ml_ensemble_decision, f"{sum(ml_confidences)/3:.1f}%", "0.3"],
            ["llm_analysis", llm_decision, f"{llm_confidence:.1f}%", "0.3"],
            ["technical_signals", tech_decision, "75.0%", "0.2"],  # Default confidence
            ["multi_timeframe", mtf_decision, "60.0%", "0.2"]  # Default confidence
        ]

        for row, (source, sig_decision, sig_confidence, weight) in enumerate(signals_data):
            decision_item = QTableWidgetItem(sig_decision)
            if sig_decision == "LONG":
                decision_item.setForeground(QColor(MatrixTheme.GREEN))
            elif sig_decision == "SHORT":
                decision_item.setForeground(QColor(MatrixTheme.RED))
            else:
                decision_item.setForeground(QColor(MatrixTheme.YELLOW))
            self.signal_hierarchy_table.setItem(row, 1, decision_item)
            self.signal_hierarchy_table.setItem(row, 2, QTableWidgetItem(sig_confidence))
            self.signal_hierarchy_table.setItem(row, 3, QTableWidgetItem(weight))

        # Update market analysis with real calculations
        trend_strength = 0.5  # Default trend strength (neutral)
        volatility = 2.0  # Default volatility percentage
        self.trend_strength_label.setText(f"Trend Strength: {trend_strength:.2f}")
        self.volatility_label.setText(f"Volatility: {volatility:.2f}%")

        # Update leverage analysis with real data
        max_leverage = 5.0  # Default max leverage
        recommended_leverage = 2.0  # Conservative recommendation
        effective_leverage = 1.5  # Even more conservative
        position_size = 500.0  # Default position size
        risk_per_trade = position_size * 0.02  # 2% risk

        self.max_leverage_label.setText(f"Max Available: {max_leverage:.1f}x")
        self.recommended_leverage_label.setText(f"Recommended: {recommended_leverage:.1f}x")
        self.effective_leverage_label.setText(f"Effective: {effective_leverage:.1f}x")
        self.position_size_label.setText(f"Position Size: {position_size:.2f} units (${position_size:.2f})")
        self.risk_per_trade_label.setText(f"Risk per Trade: ${risk_per_trade:.2f}")

        # Update risk metrics with real calculations
        portfolio_risk = 3.0  # Default portfolio risk
        max_drawdown = 5.0  # Default max drawdown
        correlation = 0.5  # Default correlation
        liquidity_score = 0.8  # Default liquidity score

        # Update portfolio risk
        self.portfolio_risk_label.setText(f"{portfolio_risk:.1f}%")
        if portfolio_risk > 5:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif portfolio_risk > 3:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.portfolio_risk_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update max drawdown
        self.max_drawdown_label.setText(f"{max_drawdown:.1f}%")
        if max_drawdown > 10:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        elif max_drawdown > 5:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-weight: bold;")
        else:
            self.max_drawdown_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        # Update correlation risk
        if correlation > 0.7:
            corr_text, corr_color = "HIGH", MatrixTheme.RED
        elif correlation > 0.4:
            corr_text, corr_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            corr_text, corr_color = "LOW", MatrixTheme.GREEN

        self.correlation_risk_label.setText(corr_text)
        self.correlation_risk_label.setStyleSheet(f"color: {corr_color}; font-weight: bold;")

        # Update liquidity risk
        if liquidity_score < 0.3:
            liq_text, liq_color = "HIGH", MatrixTheme.RED
        elif liquidity_score < 0.6:
            liq_text, liq_color = "MEDIUM", MatrixTheme.YELLOW
        else:
            liq_text, liq_color = "LOW", MatrixTheme.GREEN

        self.liquidity_risk_label.setText(liq_text)
        self.liquidity_risk_label.setStyleSheet(f"color: {liq_color}; font-weight: bold;")

        # Generate dynamic risk warnings
        warnings = []
        if volatility > 4:
            warnings.append(f"⚠️ HIGH VOLATILITY: 24h volatility {volatility:.1f}% - reduce position size")
        if liquidity_score < 0.4:
            warnings.append(f"⚠️ LOW LIQUIDITY: Order book depth insufficient - limit order size")
        if correlation > 0.7:
            warnings.append(f"⚠️ CORRELATION ALERT: {correlation:.2f} correlation with BTC - diversify")
        if effective_leverage > recommended_leverage * 1.2:
            warnings.append(f"⚠️ LEVERAGE WARNING: Current {effective_leverage:.1f}x exceeds recommended {recommended_leverage:.1f}x")
        if portfolio_risk > 5:
            warnings.append(f"⚠️ PORTFOLIO RISK: {portfolio_risk:.1f}% exceeds 5% limit - reduce exposure")
        if max_drawdown > 10:
            warnings.append(f"⚠️ DRAWDOWN ALERT: {max_drawdown:.1f}% approaching stop-loss threshold")

        # Update warnings log
        if warnings:
            self.risk_warnings_log.clear()
            for warning in warnings:
                self.risk_warnings_log.append(warning)
        else:
            self.risk_warnings_log.clear()
            self.risk_warnings_log.append("✅ No active risk warnings - all metrics within acceptable ranges")

        # Reset buttons
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("SYSTEM: READY")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")

        self.log_message(f"Analysis complete for {self.symbol_combo.currentText()}: {decision}")
        self.statusBar().showMessage(f"Analysis complete: {decision} (ML: {decision}, LLM: {llm_decision})")

        # Re-enable analysis if auto-refresh is on and still analyzing
        if self.auto_refresh_checkbox.isChecked() and self.is_analyzing:
            self.analysis_timer = QTimer()
            self.analysis_timer.setSingleShot(True)
            self.analysis_timer.timeout.connect(self.start_analysis)
            self.analysis_timer.start(30000)  # Auto-refresh every 30 seconds

    def get_technical_signal(self):
        """Get technical analysis signal from real indicators"""
        # TODO: Implement real technical analysis
        # For now, return WAIT until technical indicators are integrated
        return "WAIT"

    def get_multi_timeframe_signal(self):
        """Get multi-timeframe analysis signal"""
        # TODO: Implement real multi-timeframe analysis
        # For now, return WAIT until multi-timeframe analysis is integrated
        return "WAIT"

    def log_message(self, message):
        """Add message to analysis log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.analysis_log.append(log_entry)

    # Comprehensive Analysis Methods
    def run_comprehensive_analysis(self, symbol, current_price, ml_decisions, ml_confidences,
                                  llm_decision, llm_confidence, llm_reasoning, current_model):
        """Run comprehensive second-stage analysis with all available data"""
        try:
            if not hasattr(self, 'lmstudio_runner') or not self.lmstudio_runner:
                return

            # Get additional market data
            current_bid = getattr(self, 'current_bid', current_price)
            current_ask = getattr(self, 'current_ask', current_price)
            spread = abs(current_ask - current_bid) if current_ask and current_bid else 0.0

            # Get technical analysis data (mock for now)
            tech_decision = "WAIT"  # This would come from technical analysis
            tech_confidence = 75.0
            mtf_decision = "LONG"   # This would come from multi-timeframe analysis
            mtf_confidence = 68.0

            # Create comprehensive analysis prompt
            comprehensive_prompt = f"""🎯 EPINNOX TRADING SYSTEM - COMPREHENSIVE MARKET INTELLIGENCE REPORT 🎯

═══════════════════════════════════════════════════════════════════════════════
📊 MARKET OVERVIEW: {symbol}
═══════════════════════════════════════════════════════════════════════════════
💰 Current Price: ${current_price:.6f}
📈 Bid: ${current_bid:.6f} | Ask: ${current_ask:.6f} | Spread: ${spread:.6f}
⏰ Analysis Time: {self.get_current_timestamp()}

═══════════════════════════════════════════════════════════════════════════════
🤖 MACHINE LEARNING ENSEMBLE ANALYSIS
═══════════════════════════════════════════════════════════════════════════════
🔹 SVM Model: {ml_decisions[0]} ({ml_confidences[0]:.1f}% confidence)
🔹 Random Forest: {ml_decisions[1]} ({ml_confidences[1]:.1f}% confidence)
🔹 LSTM Neural Network: {ml_decisions[2]} ({ml_confidences[2]:.1f}% confidence)
📊 ML Ensemble Average: {sum(ml_confidences)/3:.1f}% confidence

═══════════════════════════════════════════════════════════════════════════════
🧠 INITIAL LLM ANALYSIS ({current_model})
═══════════════════════════════════════════════════════════════════════════════
🎯 Decision: {llm_decision} ({llm_confidence:.1f}% confidence)
💭 Reasoning: {llm_reasoning[:200]}...

═══════════════════════════════════════════════════════════════════════════════
📈 TECHNICAL & MULTI-TIMEFRAME SIGNALS
═══════════════════════════════════════════════════════════════════════════════
🔧 Technical Analysis: {tech_decision} ({tech_confidence:.1f}% confidence)
⏱️ Multi-Timeframe: {mtf_decision} ({mtf_confidence:.1f}% confidence)

═══════════════════════════════════════════════════════════════════════════════
🎭 CREATIVE SYNTHESIS CHALLENGE
═══════════════════════════════════════════════════════════════════════════════

You are the MASTER TRADING STRATEGIST for the Epinnox AI Trading System.

Your mission: Synthesize ALL the above intelligence into a CREATIVE, COMPREHENSIVE trading strategy that considers:

1. 🎪 CONSENSUS ANALYSIS: How do all signals align or conflict?
2. 🎨 CREATIVE RISK ASSESSMENT: What unique risks/opportunities do you see?
3. 🎯 STRATEGIC POSITIONING: What's the optimal position size and timing?
4. 🎲 SCENARIO PLANNING: Best/worst case scenarios and contingencies
5. 🎪 MARKET PSYCHOLOGY: What emotions are driving this market?

Respond in this EXACT format:
FINAL_DECISION: [LONG/SHORT/WAIT]
CONFIDENCE: [0-100]
POSITION_SIZE: [SMALL/MEDIUM/LARGE/NONE]
ENTRY_STRATEGY: [IMMEDIATE/GRADUAL/WAIT_FOR_DIP/WAIT_FOR_BREAKOUT]
RISK_LEVEL: [LOW/MEDIUM/HIGH/EXTREME]
CREATIVE_INSIGHT: [Your most creative market insight in 1-2 sentences]
SYNTHESIS: [Comprehensive reasoning combining all signals and your creative analysis]"""

            self.log_message("🎭 Starting Comprehensive Creative Analysis...")
            self.log_message("═" * 80)

            # Get comprehensive response
            comprehensive_response = self.lmstudio_runner.run_inference(
                prompt=comprehensive_prompt,
                temperature=0.8,  # Higher creativity
                max_tokens=500    # More detailed response
            )

            # Parse and display comprehensive analysis
            self.display_comprehensive_analysis(comprehensive_response, symbol, current_model)

        except Exception as e:
            self.log_message(f"❌ Error in comprehensive analysis: {e}")

    def get_current_timestamp(self):
        """Get formatted current timestamp"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

    def display_comprehensive_analysis(self, response, symbol, model):
        """Display comprehensive analysis in interface and terminal"""
        try:
            self.log_message("🎭 COMPREHENSIVE ANALYSIS COMPLETE")
            self.log_message("═" * 80)
            self.log_message(f"📝 Full Creative Response:")
            self.log_message(response)
            self.log_message("═" * 80)

            # Parse structured response
            parsed_data = self.parse_comprehensive_response(response)

            # Display in terminal with creative formatting
            self.display_creative_terminal_output(parsed_data, symbol, model)

            # Update GUI with comprehensive data
            self.update_comprehensive_gui(parsed_data)

        except Exception as e:
            self.log_message(f"❌ Error displaying comprehensive analysis: {e}")

    def parse_comprehensive_response(self, response):
        """Parse the structured comprehensive response"""
        parsed = {
            'final_decision': 'WAIT',
            'confidence': 50.0,
            'position_size': 'NONE',
            'entry_strategy': 'WAIT_FOR_SIGNAL',
            'risk_level': 'MEDIUM',
            'creative_insight': 'Analysis in progress...',
            'synthesis': 'Comprehensive analysis completed.'
        }

        if not response:
            return parsed

        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('FINAL_DECISION:'):
                parsed['final_decision'] = line.split(':', 1)[1].strip()
            elif line.startswith('CONFIDENCE:'):
                try:
                    parsed['confidence'] = float(line.split(':', 1)[1].strip())
                except:
                    pass
            elif line.startswith('POSITION_SIZE:'):
                parsed['position_size'] = line.split(':', 1)[1].strip()
            elif line.startswith('ENTRY_STRATEGY:'):
                parsed['entry_strategy'] = line.split(':', 1)[1].strip()
            elif line.startswith('RISK_LEVEL:'):
                parsed['risk_level'] = line.split(':', 1)[1].strip()
            elif line.startswith('CREATIVE_INSIGHT:'):
                parsed['creative_insight'] = line.split(':', 1)[1].strip()
            elif line.startswith('SYNTHESIS:'):
                parsed['synthesis'] = line.split(':', 1)[1].strip()

        return parsed

    def display_creative_terminal_output(self, parsed_data, symbol, model):
        """Display creative formatted output in terminal"""
        try:
            # Creative ASCII art header
            self.log_message("🎭" + "═" * 78 + "🎭")
            self.log_message("🎪           EPINNOX CREATIVE TRADING INTELLIGENCE REPORT           🎪")
            self.log_message("🎭" + "═" * 78 + "🎭")
            self.log_message(f"🎯 Symbol: {symbol} | Model: {model} | Time: {self.get_current_timestamp()}")
            self.log_message("🎭" + "─" * 78 + "🎭")

            # Decision with creative formatting
            decision = parsed_data['final_decision']
            confidence = parsed_data['confidence']

            if decision == "LONG":
                decision_emoji = "🚀📈"
                decision_color = "GREEN"
            elif decision == "SHORT":
                decision_emoji = "🔻📉"
                decision_color = "RED"
            else:
                decision_emoji = "⏸️⚖️"
                decision_color = "YELLOW"

            self.log_message(f"🎯 FINAL DECISION: {decision_emoji} {decision} ({confidence:.1f}% confidence)")
            self.log_message(f"📊 POSITION SIZE: {parsed_data['position_size']}")
            self.log_message(f"⚡ ENTRY STRATEGY: {parsed_data['entry_strategy']}")
            self.log_message(f"⚠️ RISK LEVEL: {parsed_data['risk_level']}")
            self.log_message("🎭" + "─" * 78 + "🎭")
            self.log_message(f"💡 CREATIVE INSIGHT: {parsed_data['creative_insight']}")
            self.log_message("🎭" + "─" * 78 + "🎭")
            self.log_message(f"🧠 SYNTHESIS: {parsed_data['synthesis']}")
            self.log_message("🎭" + "═" * 78 + "🎭")

        except Exception as e:
            self.log_message(f"❌ Error in creative terminal display: {e}")

    def update_comprehensive_gui(self, parsed_data):
        """Update GUI with comprehensive analysis data"""
        try:
            # Update status bar with comprehensive info
            decision = parsed_data['final_decision']
            confidence = parsed_data['confidence']
            risk = parsed_data['risk_level']

            status_msg = f"🎭 COMPREHENSIVE: {decision} ({confidence:.1f}%) | Risk: {risk} | Strategy: {parsed_data['entry_strategy']}"
            self.statusBar().showMessage(status_msg)

            # Add comprehensive analysis to the analysis log
            comprehensive_summary = f"""
🎭 COMPREHENSIVE ANALYSIS COMPLETE:
• Final Decision: {decision} ({confidence:.1f}% confidence)
• Position Size: {parsed_data['position_size']}
• Entry Strategy: {parsed_data['entry_strategy']}
• Risk Level: {parsed_data['risk_level']}
• Creative Insight: {parsed_data['creative_insight']}
"""
            self.analysis_log.append(comprehensive_summary)

            # Update comprehensive analysis panel
            if hasattr(self, 'comprehensive_decision_label'):
                decision_text = f"🎭 {decision} ({confidence:.1f}%) | {parsed_data['position_size']} | {parsed_data['risk_level']} Risk"
                self.comprehensive_decision_label.setText(decision_text)

            if hasattr(self, 'creative_insight_text'):
                insight_text = f"💡 CREATIVE INSIGHT:\n{parsed_data['creative_insight']}\n\n🧠 SYNTHESIS:\n{parsed_data['synthesis']}"
                self.creative_insight_text.setText(insight_text)

        except Exception as e:
            self.log_message(f"❌ Error updating comprehensive GUI: {e}")

    # LMStudio Model Management Methods
    def on_model_switch_requested(self, model_name: str):
        """Handle model switch request from model selector"""
        try:
            # Initialize LMStudio runner if not already done
            if not hasattr(self, 'lmstudio_runner'):
                self.setup_lmstudio_runner()

            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                success = self.lmstudio_runner.switch_model(model_name)
                if success:
                    self.log_message(f"🔄 Switched to model: {model_name}")
                    self.statusBar().showMessage(f"Model switched to: {model_name}")
                else:
                    self.log_message(f"❌ Failed to switch to model: {model_name}")
            else:
                self.log_message("❌ LMStudio runner not available")

        except Exception as e:
            self.log_message(f"Error switching model: {e}")

    def on_model_refresh_requested(self):
        """Handle model refresh request from model selector"""
        try:
            if hasattr(self, 'lmstudio_runner') and self.lmstudio_runner:
                success = self.lmstudio_runner.refresh_models()
                if success:
                    models = self.lmstudio_runner.get_available_models()
                    if hasattr(self, 'model_selector'):
                        self.model_selector.update_models(models)
                        self.model_selector.set_connection_status(True)
                    self.log_message(f"🔄 Refreshed models: {len(models)} found")
                else:
                    if hasattr(self, 'model_selector'):
                        self.model_selector.set_connection_status(False)
                    self.log_message("❌ Failed to refresh models")
            else:
                self.setup_lmstudio_runner()

        except Exception as e:
            self.log_message(f"Error refreshing models: {e}")

    def setup_lmstudio_runner(self):
        """Setup LMStudio runner with model discovery"""
        try:
            from llama.lmstudio_runner import LMStudioRunner

            self.lmstudio_runner = LMStudioRunner()

            # Connect signals
            self.lmstudio_runner.model_changed.connect(self.on_lmstudio_model_changed)
            self.lmstudio_runner.models_discovered.connect(self.on_lmstudio_models_discovered)

            # Initial model discovery
            models = self.lmstudio_runner.get_available_models()
            current_model = self.lmstudio_runner.get_current_model()

            if hasattr(self, 'model_selector'):
                self.model_selector.update_models(models)
                if current_model:
                    self.model_selector.set_current_model(current_model)
                self.model_selector.set_connection_status(len(models) > 0)

            self.log_message(f"✓ LMStudio runner initialized with {len(models)} models")

        except Exception as e:
            self.log_message(f"Error setting up LMStudio runner: {e}")

    def on_lmstudio_model_changed(self, model_name: str):
        """Handle LMStudio model change signal"""
        try:
            if hasattr(self, 'model_selector'):
                self.model_selector.set_current_model(model_name)
            self.log_message(f"✓ LMStudio model changed to: {model_name}")

        except Exception as e:
            self.log_message(f"Error handling model change: {e}")

    def on_lmstudio_models_discovered(self, models: list):
        """Handle LMStudio models discovery signal"""
        try:
            if hasattr(self, 'model_selector'):
                self.model_selector.update_models(models)
                self.model_selector.set_connection_status(len(models) > 0)
            self.log_message(f"✓ Discovered {len(models)} LMStudio models")

        except Exception as e:
            self.log_message(f"Error handling models discovery: {e}")

    # Manual Trading Methods
    def place_limit_long(self):
        """Place a limit long order using best bid price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ Limit LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit LONG order placed", 3000)
                else:
                    self.log_message(f"❌ Failed to place limit LONG order")
                    self.statusBar().showMessage("Failed to place limit LONG order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'bids' not in ob or not ob['bids']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['bids'][0][0]  # Use best bid price
            set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'buy', quantity, price, params)

            if result:
                self.log_message(f"Limit LONG: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit LONG placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit LONG order")
                self.statusBar().showMessage("Failed to place limit LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_limit_short(self):
        """Place a limit short order using best ask price"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_limit_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"✅ Limit SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Limit SHORT order placed", 3000)
                else:
                    self.log_message(f"❌ Failed to place limit SHORT order")
                    self.statusBar().showMessage("Failed to place limit SHORT order", 3000)
                return

            # Fallback to original implementation
            ob = fetch_order_book(symbol)
            if not ob or 'asks' not in ob or not ob['asks']:
                self.log_message(f"Error: Could not fetch order book for {symbol}")
                self.statusBar().showMessage("Error: Could not fetch order book", 3000)
                return

            price = ob['asks'][0][0]  # Use best ask price
            set_leverage(symbol, leverage)

            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_limit_order(symbol, 'sell', quantity, price, params)

            if result:
                self.log_message(f"Limit SHORT: {quantity} {symbol} @ {price} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Limit SHORT placed @ {price}", 3000)
            else:
                self.log_message(f"Failed to place limit SHORT order")
                self.statusBar().showMessage("Failed to place limit SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_limit_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_long(self):
        """Place a market long order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_long(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market LONG executed", 3000)
                else:
                    self.log_message(f"Failed to place market LONG order")
                    self.statusBar().showMessage("Failed to place market LONG order", 3000)
                return

            # Fallback to original implementation
            set_leverage(symbol, leverage)
            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'buy', quantity, params)

            if result:
                self.log_message(f"Market LONG: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market LONG executed", 3000)
            else:
                self.log_message(f"Failed to place market LONG order")
                self.statusBar().showMessage("Failed to place market LONG order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_long: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def place_market_short(self):
        """Place a market short order"""
        symbol = self.symbol_combo.currentText()
        quantity = self.quantity_spinbox.value()
        leverage = self.leverage_spinbox.value()

        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                success = self.real_trading.place_market_short(symbol, quantity, leverage)
                if success:
                    self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                    self.statusBar().showMessage(f"Market SHORT executed", 3000)
                else:
                    self.log_message(f"Failed to place market SHORT order")
                    self.statusBar().showMessage("Failed to place market SHORT order", 3000)
                return

            # Fallback to original implementation
            set_leverage(symbol, leverage)
            params = {'offset': 'open', 'lever_rate': leverage}
            result = place_market_order(symbol, 'sell', quantity, params)

            if result:
                self.log_message(f"Market SHORT: {quantity} {symbol} (Leverage: {leverage}x)")
                self.statusBar().showMessage(f"Market SHORT executed", 3000)
            else:
                self.log_message(f"Failed to place market SHORT order")
                self.statusBar().showMessage("Failed to place market SHORT order", 3000)

        except Exception as e:
            self.log_message(f"Error in place_market_short: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def close_all_positions(self):
        """Close all open positions"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.close_all_positions()
                if count > 0:
                    self.log_message(f"Closed {count} positions")
                    self.statusBar().showMessage(f"Closed {count} positions", 3000)
                else:
                    self.log_message("No positions to close")
                    self.statusBar().showMessage("No positions to close", 3000)
                return

            # Fallback to original implementation
            result = close_all_positions()
            if result:
                self.log_message("All positions closed")
                self.statusBar().showMessage("All positions closed", 3000)
            else:
                self.log_message("Failed to close positions")
                self.statusBar().showMessage("Failed to close positions", 3000)
        except Exception as e:
            self.log_message(f"Error closing positions: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            # Use real trading interface if available
            if hasattr(self, 'real_trading') and self.real_trading:
                count = self.real_trading.cancel_all_orders()
                if count > 0:
                    self.log_message(f"Cancelled {count} orders")
                    self.statusBar().showMessage(f"Cancelled {count} orders", 3000)
                else:
                    self.log_message("No orders to cancel")
                    self.statusBar().showMessage("No orders to cancel", 3000)
                return

            # Fallback to original implementation
            result = cancel_all_orders()
            if result:
                self.log_message("All orders cancelled")
                self.statusBar().showMessage("All orders cancelled", 3000)
            else:
                self.log_message("Failed to cancel orders")
                self.statusBar().showMessage("Failed to cancel orders", 3000)
        except Exception as e:
            self.log_message(f"Error cancelling orders: {str(e)}")
            self.statusBar().showMessage(f"Error: {str(e)}", 3000)

    def update_chart(self):
        """Update chart with latest data (fallback chart only)"""
        try:
            # Only update if using fallback chart
            if not hasattr(self, 'chart_widget'):
                return

            symbol = self.symbol_combo.currentText()

            # Check if we have timeframe combo (fallback chart)
            if hasattr(self, 'timeframe_combo'):
                timeframe = self.timeframe_combo.currentText()
            else:
                timeframe = "1m"  # Default

            # Check if we have chart type combo (fallback chart)
            if hasattr(self, 'chart_type_combo'):
                chart_type = self.chart_type_combo.currentText()
            else:
                chart_type = "Line"  # Default

            # Fetch OHLCV data
            ohlcv_data = fetch_ohlcv(symbol, timeframe, 100)

            if not ohlcv_data:
                return

            # Clear previous data
            self.chart_widget.clear()

            # Extract data
            timestamps = [item[0] / 1000 for item in ohlcv_data]  # Convert to seconds
            prices = [item[4] for item in ohlcv_data]  # Close prices

            if chart_type == "Line":
                # Plot line chart
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=2)
                self.chart_widget.plot(timestamps, prices, pen=pen)
            else:
                # Plot candlestick chart (simplified as line for now)
                pen = pg.mkPen(color=MatrixTheme.GREEN, width=1)
                self.chart_widget.plot(timestamps, prices, pen=pen)

            # Update best bid/ask display
            self.update_bid_ask_display()

        except Exception as e:
            print(f"Error updating chart: {e}")

    def update_bid_ask_display(self):
        """Update best bid/ask display - ONLY called for fallback chart, WebSocket handles live updates"""
        try:
            # This method is now only used for fallback scenarios
            # Live WebSocket data is handled directly in on_live_orderbook_updated()
            # Only update if we don't have current bid/ask from WebSocket
            if not hasattr(self, 'current_bid') or self.current_bid is None:
                symbol = self.symbol_combo.currentText()

                # Use real trading interface if available
                if hasattr(self, 'real_trading') and self.real_trading:
                    best_bid, best_ask = self.real_trading.get_best_bid_ask(symbol)

                    if best_bid is not None:
                        self.current_bid = best_bid
                    if best_ask is not None:
                        self.current_ask = best_ask

            # Update display with current values (from WebSocket or fallback)
            if hasattr(self, 'best_bid_label') and hasattr(self, 'best_ask_label'):
                if self.current_bid is not None:
                    bid_text = f"{self.current_bid:.6f}"
                    if hasattr(self, 'last_bid') and self.last_bid is not None:
                        if self.current_bid > self.last_bid:
                            bid_text += " ↑"
                        elif self.current_bid < self.last_bid:
                            bid_text += " ↓"
                    self.best_bid_label.setText(bid_text)
                else:
                    self.best_bid_label.setText("--")

                if self.current_ask is not None:
                    ask_text = f"{self.current_ask:.6f}"
                    if hasattr(self, 'last_ask') and self.last_ask is not None:
                        if self.current_ask > self.last_ask:
                            ask_text += " ↑"
                        elif self.current_ask < self.last_ask:
                            ask_text += " ↓"
                    self.best_ask_label.setText(ask_text)
                else:
                    self.best_ask_label.setText("--")

                # Calculate and display spread
                if self.current_bid is not None and self.current_ask is not None:
                    spread = self.current_ask - self.current_bid
                    spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                    self.spread_label.setText(f"{spread:.6f} ({spread_pct:.3f}%)")
                else:
                    self.spread_label.setText("--")

        except Exception as e:
            print(f"Error updating bid/ask display: {e}")
            if hasattr(self, 'best_bid_label'):
                self.best_bid_label.setText("--")
            if hasattr(self, 'best_ask_label'):
                self.best_ask_label.setText("--")
            if hasattr(self, 'spread_label'):
                self.spread_label.setText("--")

    def _set_trading_buttons_enabled(self, enabled: bool):
        """Enable/disable trading buttons (for loading states)"""
        try:
            self.limit_long_btn.setEnabled(enabled)
            self.market_long_btn.setEnabled(enabled)
            self.limit_short_btn.setEnabled(enabled)
            self.market_short_btn.setEnabled(enabled)
            self.close_all_btn.setEnabled(enabled)
            self.cancel_all_btn.setEnabled(enabled)

            # Update button text to show loading state
            if not enabled:
                buttons = [
                    (self.limit_long_btn, "PLACING..."),
                    (self.market_long_btn, "PLACING..."),
                    (self.limit_short_btn, "PLACING..."),
                    (self.market_short_btn, "PLACING..."),
                    (self.close_all_btn, "CLOSING..."),
                    (self.cancel_all_btn, "CANCELLING...")
                ]
                for btn, text in buttons:
                    btn.setText(text)
            else:
                # Restore original button text
                self.limit_long_btn.setText("LIMIT LONG")
                self.market_long_btn.setText("MARKET LONG")
                self.limit_short_btn.setText("LIMIT SHORT")
                self.market_short_btn.setText("MARKET SHORT")
                self.close_all_btn.setText("CLOSE ALL")
                self.cancel_all_btn.setText("CANCEL ALL")

        except Exception as e:
            print(f"Error setting button states: {e}")

    def on_chart_click(self, event):
        """Handle chart click events for order placement using best bid/ask"""
        try:
            if event.button() == 1:  # Left click - BUY (use best bid)
                self.place_limit_long()
            elif event.button() == 2:  # Right click - SELL (use best ask)
                self.place_limit_short()
            else:
                return

        except Exception as e:
            print(f"Error handling chart click: {e}")

    def on_live_chart_click(self, price: float, timestamp: float):
        """Handle live chart click events"""
        try:
            # For now, just trigger the same order placement logic
            # In the future, could use the clicked price for limit orders
            print(f"Live chart clicked at price: {price:.6f}, time: {timestamp}")

            # Use current best bid/ask instead of clicked price for safety
            self.place_limit_long()  # Default to long, could add right-click detection

        except Exception as e:
            print(f"Error handling live chart click: {e}")

    def on_chart_symbol_changed(self, symbol: str):
        """Handle symbol change from chart"""
        try:
            # Update main symbol selector
            self.symbol_combo.setCurrentText(symbol)

            # Subscribe to new symbol in live data manager
            if hasattr(self, 'live_data_manager') and self.live_data_manager:
                self.live_data_manager.subscribe_symbol(symbol, ["1m", "5m", "15m"])

            self.log_message(f"Chart symbol changed to: {symbol}")

        except Exception as e:
            print(f"Error handling chart symbol change: {e}")

    def on_chart_timeframe_changed(self, timeframe: str):
        """Handle timeframe change from chart"""
        try:
            self.log_message(f"Chart timeframe changed to: {timeframe}")

        except Exception as e:
            print(f"Error handling chart timeframe change: {e}")

    def on_live_chart_data_updated(self, symbol: str, chart_data: dict):
        """Handle live chart data updates"""
        try:
            if hasattr(self, 'live_chart'):
                # Update the live chart widget with new data
                ohlcv_data = chart_data.get("ohlcv", [])
                if ohlcv_data:
                    self.live_chart.update_ohlcv_data(ohlcv_data)

        except Exception as e:
            print(f"Error handling live chart data update: {e}")

    def on_live_price_updated(self, symbol: str, price: float):
        """Handle live price updates"""
        try:
            # Update current price display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                # Update any price displays
                pass

        except Exception as e:
            print(f"Error handling live price update: {e}")

    def on_live_orderbook_updated(self, symbol: str, orderbook_data: dict):
        """Handle live order book updates with enhanced manual trading panel integration"""
        try:
            # Update bid/ask display if it's the active symbol
            current_symbol = self.symbol_combo.currentText()
            if symbol == current_symbol:
                bids = orderbook_data.get("bids", [])
                asks = orderbook_data.get("asks", [])

                best_bid = bids[0][0] if bids else None
                best_ask = asks[0][0] if asks else None

                # Store the bid/ask values for manual trading
                if best_bid is not None:
                    self.last_bid = getattr(self, 'current_bid', None)
                    self.current_bid = best_bid
                if best_ask is not None:
                    self.last_ask = getattr(self, 'current_ask', None)
                    self.current_ask = best_ask

                # Update live chart bid/ask display
                if hasattr(self, 'live_chart'):
                    self.live_chart.update_bid_ask_data(best_bid, best_ask)

                # Update real trading interface with live orderbook data
                if hasattr(self, 'real_trading') and self.real_trading:
                    self.real_trading.update_live_orderbook(symbol, orderbook_data)

                # Update prediction tracker with current price
                if hasattr(self, 'prediction_tracker') and best_bid is not None:
                    self.prediction_tracker.update_current_price(symbol, best_bid)

                # Update manual trading panel with live WebSocket data ONLY
                if hasattr(self, 'best_bid_label') and hasattr(self, 'best_ask_label'):
                    # Update stored values for movement tracking
                    if best_bid is not None:
                        self.last_bid = getattr(self, 'current_bid', None)
                        self.current_bid = best_bid
                    if best_ask is not None:
                        self.last_ask = getattr(self, 'current_ask', None)
                        self.current_ask = best_ask

                    # Update bid display with movement indicator
                    if self.current_bid is not None:
                        bid_text = f"{self.current_bid:.6f}"
                        if self.last_bid is not None:
                            if self.current_bid > self.last_bid:
                                bid_text += " ↑"
                            elif self.current_bid < self.last_bid:
                                bid_text += " ↓"
                        self.best_bid_label.setText(bid_text)

                    # Update ask display with movement indicator
                    if self.current_ask is not None:
                        ask_text = f"{self.current_ask:.6f}"
                        if self.last_ask is not None:
                            if self.current_ask > self.last_ask:
                                ask_text += " ↑"
                            elif self.current_ask < self.last_ask:
                                ask_text += " ↓"
                        self.best_ask_label.setText(ask_text)

                    # Calculate and display spread
                    if self.current_bid is not None and self.current_ask is not None:
                        spread = self.current_ask - self.current_bid
                        spread_pct = (spread / self.current_bid) * 100 if self.current_bid > 0 else 0
                        self.spread_label.setText(f"{spread:.6f} ({spread_pct:.3f}%)")

                # Auto-update price spinbox if it's currently at default or old values
                if hasattr(self, 'price_spinbox') and best_bid and best_ask:
                    current_price = self.price_spinbox.value()
                    # If price is at default (0.175) or very different from market, suggest update
                    if current_price == 0.175 or abs(current_price - best_bid) / best_bid > 0.1:
                        # Set to mid-price as a reasonable default
                        mid_price = (best_bid + best_ask) / 2
                        self.price_spinbox.setValue(mid_price)

        except Exception as e:
            print(f"Error handling live orderbook update: {e}")

    def on_live_connection_status(self, connected: bool):
        """Handle live data connection status changes"""
        try:
            # Update live chart connection status
            if hasattr(self, 'live_chart'):
                self.live_chart.update_connection_status(connected)

            # Update status in log
            status = "Connected to live data" if connected else "Disconnected from live data"
            self.log_message(status)

        except Exception as e:
            print(f"Error handling live connection status: {e}")

    def on_trade_update(self, symbol: str, trade_data: dict):
        """Handle individual trade updates from WebSocket"""
        try:
            # Only process trades for the active chart symbol
            if hasattr(self, 'live_chart') and symbol == self.live_chart.current_symbol:
                trades = trade_data.get('trades', [])

                for trade in trades:
                    price = float(trade.get('price', 0))
                    amount = float(trade.get('amount', 0))
                    direction = trade.get('direction', 'buy')  # HTX uses 'buy'/'sell'
                    timestamp = float(trade.get('ts', 0)) / 1000  # Convert to seconds

                    # Update chart with individual trade
                    self.live_chart.update_trade_data(price, amount, direction, timestamp)

        except Exception as e:
            print(f"Error handling trade update: {e}")

    # Real Trading Interface Signal Handlers
    def on_order_status_updated(self, order_info: dict):
        """Handle order status updates from real trading interface"""
        try:
            order_type = order_info.get('type', '')
            order = order_info.get('order', {})

            if order_type == 'order_placed':
                self.log_message(f"Order placed: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')}")
            elif order_type == 'order_filled':
                self.log_message(f"Order filled: {order.get('id', 'N/A')} - {order.get('side', '')} {order.get('amount', 0)} {order.get('symbol', '')} @ {order.get('price', 0)}")
            elif order_type == 'order_cancelled':
                self.log_message(f"Order cancelled: {order.get('id', 'N/A')}")

        except Exception as e:
            print(f"Error handling order status update: {e}")

    def on_position_status_updated(self, position_info: dict):
        """Handle position status updates from real trading interface"""
        try:
            symbol = position_info.get('symbol', '')
            size = position_info.get('size', 0)
            side = position_info.get('side', '')
            pnl = position_info.get('unrealized_pnl', 0)

            if size != 0:
                self.log_message(f"Position update: {side} {abs(size)} {symbol} - PnL: ${pnl:.2f}")

        except Exception as e:
            print(f"Error handling position status update: {e}")

    def on_balance_status_updated(self, balance_info: dict):
        """Handle balance status updates from real trading interface"""
        try:
            usdt_balance = balance_info.get('USDT', {}).get('free', 0)
            self.log_message(f"Balance update: ${usdt_balance:.2f} USDT available")

        except Exception as e:
            print(f"Error handling balance status update: {e}")

    def on_trading_error(self, error_message: str):
        """Handle trading errors from real trading interface"""
        try:
            self.log_message(f"Trading Error: {error_message}")
            self.statusBar().showMessage(f"Trading Error: {error_message}", 5000)

        except Exception as e:
            print(f"Error handling trading error: {e}")

    def on_trading_status(self, status_message: str):
        """Handle trading status updates from real trading interface"""
        try:
            self.log_message(f"Trading Status: {status_message}")

        except Exception as e:
            print(f"Error handling trading status: {e}")

    def on_pnl_updated(self, pnl_summary: dict):
        """Handle PnL updates from real trading interface"""
        try:
            unrealized = pnl_summary.get('unrealized_pnl', 0)
            realized = pnl_summary.get('realized_pnl', 0)
            total = pnl_summary.get('total_pnl', 0)

            # Update PnL display (could add to UI later)
            if total != 0:
                self.log_message(f"PnL Update: Total ${total:.2f} (Unrealized: ${unrealized:.2f}, Realized: ${realized:.2f})")

        except Exception as e:
            print(f"Error handling PnL update: {e}")

    def on_risk_warning(self, warning_type: str, warning_message: str):
        """Handle risk warnings from real trading interface"""
        try:
            self.log_message(f"RISK WARNING [{warning_type.upper()}]: {warning_message}")
            self.statusBar().showMessage(f"RISK WARNING: {warning_message}", 10000)

        except Exception as e:
            print(f"Error handling risk warning: {e}")

    # Signal Trading Engine Signal Handlers
    def on_signal_received(self, signal_data: dict):
        """Handle signal received from signal trading engine"""
        try:
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            confidence = signal_data.get('confidence', 0)

            self.log_message(f"Signal received: {source} -> {decision} ({confidence:.1%})")

            # Record signal in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_signal(signal_data)

        except Exception as e:
            print(f"Error handling signal received: {e}")

    def on_trade_decision_made(self, decision_data: dict):
        """Handle trade decision from signal trading engine"""
        try:
            symbol = decision_data.get('symbol', '')
            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0)
            reasoning = decision_data.get('reasoning', '')

            self.log_message(f"Trade decision: {decision} {symbol} ({confidence:.1%}) - {reasoning}")

        except Exception as e:
            print(f"Error handling trade decision: {e}")

    def on_automated_trade_executed(self, trade_data: dict):
        """Handle automated trade execution"""
        try:
            symbol = trade_data.get('symbol', '')
            decision = trade_data.get('decision', '')
            position_size = trade_data.get('position_size', 0)
            confidence = trade_data.get('confidence', 0)

            self.log_message(f"🤖 Automated trade: {decision} {position_size} {symbol} (confidence: {confidence:.1%})")
            self.statusBar().showMessage(f"Automated {decision} executed", 5000)

            # Record trade in session
            if hasattr(self, 'session_manager') and self.session_manager:
                self.session_manager.record_trade(trade_data)

        except Exception as e:
            print(f"Error handling automated trade: {e}")

    def on_risk_limit_triggered(self, limit_type: str, details: dict):
        """Handle risk limit triggered"""
        try:
            self.log_message(f"🚨 RISK LIMIT TRIGGERED: {limit_type} - {details}")
            self.statusBar().showMessage(f"RISK LIMIT: {limit_type}", 10000)

        except Exception as e:
            print(f"Error handling risk limit: {e}")

    def on_engine_status_changed(self, status: str):
        """Handle signal trading engine status changes"""
        try:
            self.log_message(f"Engine status: {status}")

        except Exception as e:
            print(f"Error handling engine status: {e}")

    # Session Management Signal Handlers
    def on_session_started(self, session_id: str):
        """Handle session started"""
        try:
            self.log_message(f"📊 Session started: {session_id}")

        except Exception as e:
            print(f"Error handling session started: {e}")

    def on_session_ended(self, session_id: str, summary: dict):
        """Handle session ended"""
        try:
            trade_stats = summary.get('trade_stats', {})
            total_trades = trade_stats.get('total_trades', 0)
            win_rate = trade_stats.get('win_rate', 0)
            total_pnl = trade_stats.get('total_pnl', 0)

            self.log_message(f"📊 Session ended: {session_id}")
            self.log_message(f"📈 Summary: {total_trades} trades, {win_rate:.1f}% win rate, ${total_pnl:.2f} PnL")

        except Exception as e:
            print(f"Error handling session ended: {e}")

    def on_trade_recorded_to_db(self, trade_data: dict):
        """Handle trade recorded to database"""
        try:
            trade_id = trade_data.get('trade_id', 'N/A')
            pnl = trade_data.get('pnl', 0)
            self.log_message(f"💾 Trade recorded: {trade_id} (PnL: ${pnl:.2f})")

        except Exception as e:
            print(f"Error handling trade recorded: {e}")

    def on_signal_recorded_to_db(self, signal_data: dict):
        """Handle signal recorded to database"""
        try:
            source = signal_data.get('source', 'unknown')
            decision = signal_data.get('decision', 'WAIT')
            # Log at debug level to avoid spam
            # self.log_message(f"💾 Signal recorded: {source} -> {decision}")

        except Exception as e:
            print(f"Error handling signal recorded: {e}")

def main():
    """Main application entry point"""
    try:
        print("Starting Epinnox v6 Trading System...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Trading System")
        app.setApplicationVersion("6.0")
        
        # Create and show main window
        window = EpinnoxTradingInterface()
        window.show()
        
        print("✓ Epinnox v6 GUI started successfully")
        print("✓ Ready for trading analysis")
        
        # Run application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"✗ Error starting Epinnox v6: {e}")
        print("\nMake sure you have:")
        print("1. PyQt5 installed: pip install PyQt5")
        print("2. Running from the Epinnox_v6 directory")
        sys.exit(1)

if __name__ == "__main__":
    main()