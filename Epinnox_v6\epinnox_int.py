#!/usr/bin/env python3
"""
Epinnox v6 Integrated Trading System GUI

Comprehensive PyQt5 interface integrating all optimized features:
- Dynamic Leverage Management
- ML Model Predictions
- Signal Hierarchy Resolution
- Smart Position Sizing
- Real-time Market Data
- Matrix Theme Design
"""

import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import traceback

# PyQt5 imports
try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
except ImportError:
    print("PyQt5 not installed. Install with: pip install PyQt5")
    sys.exit(1)

# Epinnox system imports
try:
    from main import run_trading_system
    from core.leverage_manager import DynamicLeverageManager
    from ml.models import MLModelManager
    from core.signal_hierarchy import IntelligentSignalHierarchy
    from ml.position_sizing import SmartPositionSizer
    from data.exchange import ExchangeDataFetcher
except ImportError as e:
    print(f"Error importing Epinnox modules: {e}")
    print("Make sure you're running from the Epinnox_v6 directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MatrixTheme:
    """Matrix theme color and style definitions"""
    
    # Colors
    BLACK = "#000000"
    GREEN = "#00FF00"
    DARK_GREEN = "#003300"
    MID_GREEN = "#005500"
    LIGHT_GREEN = "#00AA00"
    RED = "#FF0000"
    YELLOW = "#FFFF00"
    BLUE = "#0088FF"
    
    # Fonts
    FONT_FAMILY = "Courier New"
    FONT_SIZE = 10
    FONT_SIZE_LARGE = 12
    FONT_SIZE_SMALL = 12
    
    @classmethod
    def get_stylesheet(cls):
        """Get the complete Matrix theme stylesheet"""
        return f"""
        /* Main Application Style */
        QMainWindow {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            font-family: {cls.FONT_FAMILY};
            font-size: {cls.FONT_SIZE}px;
        }}
        
        /* Tab Widget */
        QTabWidget::pane {{
            border: 1px solid {cls.MID_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QTabBar::tab {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 8px 16px;
            margin: 2px;
            border: 1px solid {cls.MID_GREEN};
            font-family: {cls.FONT_FAMILY};
            font-weight: bold;
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.MID_GREEN};
            color: {cls.GREEN};
            border: 2px solid {cls.GREEN};
        }}
        
        QTabBar::tab:hover {{
            background-color: {cls.MID_GREEN};
        }}
        
        /* Labels */
        QLabel {{
            color: {cls.GREEN};
            font-family: {cls.FONT_FAMILY};
            background-color: transparent;
        }}
        
        /* Buttons */
        QPushButton {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border: 1px solid {cls.MID_GREEN};
            padding: 6px 12px;
            font-family: {cls.FONT_FAMILY};
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {cls.MID_GREEN};
            border: 1px solid {cls.GREEN};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.GREEN};
            color: {cls.BLACK};
        }}
        
        QPushButton:disabled {{
            background-color: {cls.BLACK};
            color: {cls.DARK_GREEN};
            border: 1px solid {cls.DARK_GREEN};
        }}
        
        /* Input Fields */
        QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.MID_GREEN};
            padding: 4px;
            font-family: {cls.FONT_FAMILY};
        }}
        
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border: 2px solid {cls.GREEN};
        }}
        
        /* Text Areas */
        QTextEdit, QPlainTextEdit {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            border: 1px solid {cls.MID_GREEN};
            font-family: {cls.FONT_FAMILY};
            font-size: {cls.FONT_SIZE_SMALL}px;
        }}
        
        /* Tables */
        QTableWidget {{
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            gridline-color: {cls.DARK_GREEN};
            border: 1px solid {cls.MID_GREEN};
            font-family: {cls.FONT_FAMILY};
        }}
        
        QTableWidget::item {{
            padding: 4px;
            border-bottom: 1px solid {cls.DARK_GREEN};
        }}
        
        QTableWidget::item:selected {{
            background-color: {cls.MID_GREEN};
        }}
        
        QHeaderView::section {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            padding: 6px;
            border: 1px solid {cls.MID_GREEN};
            font-weight: bold;
        }}
        
        /* Group Boxes */
        QGroupBox {{
            color: {cls.GREEN};
            border: 2px solid {cls.MID_GREEN};
            border-radius: 5px;
            margin: 10px 0px;
            padding-top: 10px;
            font-weight: bold;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        
        /* Progress Bars */
        QProgressBar {{
            border: 1px solid {cls.MID_GREEN};
            background-color: {cls.BLACK};
            color: {cls.GREEN};
            text-align: center;
            font-family: {cls.FONT_FAMILY};
        }}
        
        QProgressBar::chunk {{
            background-color: {cls.GREEN};
        }}
        
        /* Scroll Bars */
        QScrollBar:vertical {{
            background-color: {cls.BLACK};
            width: 15px;
            border: 1px solid {cls.MID_GREEN};
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {cls.MID_GREEN};
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {cls.GREEN};
        }}
        
        /* Status Bar */
        QStatusBar {{
            background-color: {cls.DARK_GREEN};
            color: {cls.GREEN};
            border-top: 1px solid {cls.MID_GREEN};
        }}
        
        /* Splitter */
        QSplitter::handle {{
            background-color: {cls.MID_GREEN};
        }}
        
        /* Check Boxes */
        QCheckBox {{
            color: {cls.GREEN};
            font-family: {cls.FONT_FAMILY};
        }}
        
        QCheckBox::indicator {{
            width: 13px;
            height: 13px;
            border: 1px solid {cls.MID_GREEN};
            background-color: {cls.BLACK};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {cls.GREEN};
        }}
        """

class TradingSystemWorker(QThread):
    """Worker thread for running trading system operations"""
    
    # Signals
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    status_update = pyqtSignal(str)
    
    def __init__(self, symbol: str, use_live_data: bool = False):
        super().__init__()
        self.symbol = symbol
        self.use_live_data = use_live_data
        self.running = False
    
    def run(self):
        """Execute trading system analysis"""
        try:
            self.running = True
            self.status_update.emit(f"Analyzing {self.symbol}...")
            
            # Run the trading system
            decision, explanation, parsed_response = run_trading_system(
                symbol=self.symbol,
                use_live_data=self.use_live_data
            )
            
            # Prepare result data
            result = {
                'symbol': self.symbol,
                'decision': decision,
                'explanation': explanation,
                'timestamp': datetime.now().isoformat(),
                'data': parsed_response
            }
            
            self.result_ready.emit(result)
            self.status_update.emit(f"Analysis complete for {self.symbol}")
            
        except Exception as e:
            error_msg = f"Error analyzing {self.symbol}: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
        finally:
            self.running = False
    
    def stop(self):
        """Stop the worker thread"""
        self.running = False
        self.quit()
        self.wait()

class EpinnoxMainWindow(QMainWindow):
    """Main window for Epinnox v6 Integrated Trading System"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Epinnox v6 Integrated Trading System")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize components
        self.trading_worker = None
        self.settings = self.load_settings()
        self.current_data = {}
        
        # Setup UI
        self.setup_ui()
        self.setup_timers()
        
        # Apply Matrix theme
        self.setStyleSheet(MatrixTheme.get_stylesheet())
        
        # Initialize status
        self.statusBar().showMessage("Epinnox v6 System Ready")
        
        logger.info("Epinnox v6 GUI initialized successfully")
    
    def setup_ui(self):
        """Setup the main user interface"""
        # Central widget with tab layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_live_trading_tab()
        self.create_scalping_scanner_tab()
        self.create_manual_trader_tab()
        self.create_auto_trader_tab()
        self.create_microstructure_tab()
        self.create_performance_dashboard_tab()
        self.create_settings_tab()
        
        # Status bar
        status_bar = self.statusBar()

        # Add connection status
        self.connection_label = QLabel("Disconnected")
        self.connection_label.setStyleSheet(f"color: {MatrixTheme.RED};")
        status_bar.addPermanentWidget(self.connection_label)
    
    def create_header(self):
        """Create the header section"""
        header_layout = QHBoxLayout()
        
        # Title
        title_label = QLabel("EPINNOX INVESTMENT SYSTEM")
        title_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE + 4}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 10px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # System status
        self.system_status_label = QLabel("SYSTEM: READY")
        self.system_status_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            font-weight: bold;
            color: {MatrixTheme.GREEN};
            padding: 5px;
        """)
        
        # Time display
        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            font-size: {MatrixTheme.FONT_SIZE_LARGE}px;
            color: {MatrixTheme.GREEN};
            padding: 5px;
        """)
        
        header_layout.addWidget(title_label, 2)
        header_layout.addWidget(self.system_status_label, 1)
        header_layout.addWidget(self.time_label, 1)
        
        return header_layout

    def create_live_trading_tab(self):
        """Create the Live Trading tab with real-time system integration"""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # Left panel - Controls and Symbol Selection
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setMaximumWidth(350)

        # Symbol selection group
        symbol_group = QGroupBox("Symbol Selection")
        symbol_layout = QVBoxLayout(symbol_group)

        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            "DOGE/USDT", "DOGE/USDT:USDT", "BTC/USDT", "BTC/USDT:USDT",
            "ETH/USDT", "ETH/USDT:USDT", "ADA/USDT", "SOL/USDT"
        ])
        self.symbol_combo.setCurrentText("DOGE/USDT")

        self.live_data_checkbox = QCheckBox("Use Live Data")
        self.auto_refresh_checkbox = QCheckBox("Auto Refresh (30s)")
        self.auto_refresh_checkbox.setChecked(True)

        self.analyze_button = QPushButton("ANALYZE SYMBOL")
        self.analyze_button.clicked.connect(self.analyze_symbol)

        self.stop_button = QPushButton("STOP ANALYSIS")
        self.stop_button.clicked.connect(self.stop_analysis)
        self.stop_button.setEnabled(False)

        symbol_layout.addWidget(QLabel("Trading Symbol:"))
        symbol_layout.addWidget(self.symbol_combo)
        symbol_layout.addWidget(self.live_data_checkbox)
        symbol_layout.addWidget(self.auto_refresh_checkbox)
        symbol_layout.addWidget(self.analyze_button)
        symbol_layout.addWidget(self.stop_button)

        # Current Analysis Results
        results_group = QGroupBox("Current Analysis")
        results_layout = QVBoxLayout(results_group)

        self.decision_label = QLabel("Decision: WAITING...")
        self.decision_label.setStyleSheet(f"font-size: {MatrixTheme.FONT_SIZE_LARGE}px; font-weight: bold;")

        self.confidence_label = QLabel("Confidence: --")
        self.timestamp_label = QLabel("Last Update: --")

        results_layout.addWidget(self.decision_label)
        results_layout.addWidget(self.confidence_label)
        results_layout.addWidget(self.timestamp_label)

        # ML Models Status
        ml_group = QGroupBox("ML Models Status")
        ml_layout = QVBoxLayout(ml_group)

        self.ml_status_table = QTableWidget(3, 3)
        self.ml_status_table.setHorizontalHeaderLabels(["Model", "Decision", "Confidence"])
        self.ml_status_table.setMaximumHeight(120)

        # Apply Matrix theme styling to ML status table
        self.ml_status_table.verticalHeader().setVisible(False)
        self.ml_status_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        ml_header = self.ml_status_table.horizontalHeader()
        ml_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        ml_header.setStretchLastSection(False)

        ml_layout.addWidget(self.ml_status_table)

        # Leverage Analysis
        leverage_group = QGroupBox("Leverage Analysis")
        leverage_layout = QVBoxLayout(leverage_group)

        self.max_leverage_label = QLabel("Max Available: --")
        self.recommended_leverage_label = QLabel("Recommended: --")
        self.effective_leverage_label = QLabel("Effective: --")
        self.position_size_label = QLabel("Position Size: --")
        self.risk_label = QLabel("Risk per Trade: --")

        leverage_layout.addWidget(self.max_leverage_label)
        leverage_layout.addWidget(self.recommended_leverage_label)
        leverage_layout.addWidget(self.effective_leverage_label)
        leverage_layout.addWidget(self.position_size_label)
        leverage_layout.addWidget(self.risk_label)

        # Add groups to left panel
        left_layout.addWidget(symbol_group)
        left_layout.addWidget(results_group)
        left_layout.addWidget(ml_group)
        left_layout.addWidget(leverage_group)
        left_layout.addStretch()

        # Right panel - Detailed Analysis
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Signal Hierarchy Display
        hierarchy_group = QGroupBox("Signal Hierarchy Analysis")
        hierarchy_layout = QVBoxLayout(hierarchy_group)

        self.hierarchy_table = QTableWidget(5, 4)
        self.hierarchy_table.setHorizontalHeaderLabels(["Source", "Decision", "Confidence", "Weight"])
        self.hierarchy_table.setMaximumHeight(180)

        # Apply Matrix theme styling to hierarchy table
        self.hierarchy_table.verticalHeader().setVisible(False)
        self.hierarchy_table.setCornerButtonEnabled(False)

        self.hierarchy_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        header = self.hierarchy_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setStretchLastSection(True)

        hierarchy_layout.addWidget(self.hierarchy_table)

        # Market Analysis
        market_group = QGroupBox("Market Analysis")
        market_layout = QVBoxLayout(market_group)

        self.market_regime_label = QLabel("Market Regime: --")
        self.trend_strength_label = QLabel("Trend Strength: --")
        self.volatility_label = QLabel("Volatility: --")
        self.liquidity_label = QLabel("Liquidity Score: --")

        market_layout.addWidget(self.market_regime_label)
        market_layout.addWidget(self.trend_strength_label)
        market_layout.addWidget(self.volatility_label)
        market_layout.addWidget(self.liquidity_label)

        # Analysis Log
        log_group = QGroupBox("Analysis Log")
        log_layout = QVBoxLayout(log_group)

        self.analysis_log = QTextEdit()
        self.analysis_log.setMaximumHeight(200)
        self.analysis_log.setReadOnly(True)

        log_layout.addWidget(self.analysis_log)

        # Risk Warnings
        warnings_group = QGroupBox("Risk Warnings")
        warnings_layout = QVBoxLayout(warnings_group)

        self.warnings_list = QTextEdit()
        self.warnings_list.setMaximumHeight(100)
        self.warnings_list.setReadOnly(True)
        self.warnings_list.setStyleSheet(f"color: {MatrixTheme.YELLOW};")

        warnings_layout.addWidget(self.warnings_list)

        # Add groups to right panel
        right_layout.addWidget(hierarchy_group)
        right_layout.addWidget(market_group)
        right_layout.addWidget(log_group)
        right_layout.addWidget(warnings_group)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel, 2)

        self.tab_widget.addTab(tab, "Live Trading")

    def create_scalping_scanner_tab(self):
        """Create the Scalper"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Scanner controls
        controls_layout = QHBoxLayout()

        self.scanner_symbols = QLineEdit("DOGE/USDT,BTC/USDT,ETH/USDT,ADA/USDT")
        self.scanner_symbols.setPlaceholderText("Enter symbols separated by commas")

        self.scan_button = QPushButton("START SCAN")
        self.scan_button.clicked.connect(self.start_scanner)

        self.stop_scan_button = QPushButton("STOP SCAN")
        self.stop_scan_button.clicked.connect(self.stop_scanner)
        self.stop_scan_button.setEnabled(False)

        controls_layout.addWidget(QLabel("Symbols:"))
        controls_layout.addWidget(self.scanner_symbols)
        controls_layout.addWidget(self.scan_button)
        controls_layout.addWidget(self.stop_scan_button)

        # Scanner results table
        self.scanner_table = QTableWidget(0, 8)
        self.scanner_table.setHorizontalHeaderLabels([
            "Symbol", "Decision", "Confidence", "ML Consensus",
            "Leverage", "Position Size", "Risk", "Last Update"
        ])

        # Apply Matrix theme styling to scanner table
        self.scanner_table.verticalHeader().setVisible(False)
        self.scanner_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        scanner_header = self.scanner_table.horizontalHeader()
        scanner_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        scanner_header.setStretchLastSection(False)

        layout.addLayout(controls_layout)
        layout.addWidget(self.scanner_table)

        self.tab_widget.addTab(tab, "Scalpem Scan")

    def create_manual_trader_tab(self):
        """Create the Manual Trader tab"""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # Order entry panel
        order_panel = QGroupBox("Trade")
        order_layout = QVBoxLayout(order_panel)

        # Symbol and side
        symbol_side_layout = QHBoxLayout()
        self.manual_symbol = QComboBox()
        self.manual_symbol.addItems(["DOGE/USDT", "BTC/USDT", "ETH/USDT"])

        self.order_side = QComboBox()
        self.order_side.addItems(["BUY", "SELL"])

        symbol_side_layout.addWidget(QLabel("Symbol:"))
        symbol_side_layout.addWidget(self.manual_symbol)
        symbol_side_layout.addWidget(QLabel("Side:"))
        symbol_side_layout.addWidget(self.order_side)

        # Quantity and price
        qty_price_layout = QHBoxLayout()
        self.order_quantity = QDoubleSpinBox()
        self.order_quantity.setDecimals(4)
        self.order_quantity.setMaximum(999999)

        self.order_price = QDoubleSpinBox()
        self.order_price.setDecimals(6)
        self.order_price.setMaximum(999999)

        qty_price_layout.addWidget(QLabel("Quantity:"))
        qty_price_layout.addWidget(self.order_quantity)
        qty_price_layout.addWidget(QLabel("Price:"))
        qty_price_layout.addWidget(self.order_price)

        # Order type and leverage
        type_leverage_layout = QHBoxLayout()
        self.order_type = QComboBox()
        self.order_type.addItems(["MARKET", "LIMIT", "STOP"])

        self.manual_leverage = QSpinBox()
        self.manual_leverage.setRange(1, 100)
        self.manual_leverage.setValue(1)

        type_leverage_layout.addWidget(QLabel("Type:"))
        type_leverage_layout.addWidget(self.order_type)
        type_leverage_layout.addWidget(QLabel("Leverage:"))
        type_leverage_layout.addWidget(self.manual_leverage)

        # Buttons
        button_layout = QHBoxLayout()
        self.place_order_button = QPushButton("PLACE ORDER")
        self.cancel_orders_button = QPushButton("CANCEL ALL")

        button_layout.addWidget(self.place_order_button)
        button_layout.addWidget(self.cancel_orders_button)

        order_layout.addLayout(symbol_side_layout)
        order_layout.addLayout(qty_price_layout)
        order_layout.addLayout(type_leverage_layout)
        order_layout.addLayout(button_layout)
        order_layout.addStretch()

        # Positions and orders panel
        positions_panel = QWidget()
        positions_layout = QVBoxLayout(positions_panel)

        # Open positions
        positions_group = QGroupBox("Open Positions")
        positions_group_layout = QVBoxLayout(positions_group)

        self.positions_table = QTableWidget(0, 6)
        self.positions_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Size", "Entry Price", "PnL", "Actions"
        ])

        # Apply Matrix theme styling to positions table
        self.positions_table.verticalHeader().setVisible(False)
        self.positions_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        positions_header = self.positions_table.horizontalHeader()
        positions_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        positions_header.setStretchLastSection(False)

        positions_group_layout.addWidget(self.positions_table)

        # Open orders
        orders_group = QGroupBox("Open Orders")
        orders_group_layout = QVBoxLayout(orders_group)

        self.orders_table = QTableWidget(0, 7)
        self.orders_table.setHorizontalHeaderLabels([
            "Symbol", "Side", "Type", "Size", "Price", "Status", "Actions"
        ])

        # Apply Matrix theme styling to orders table
        self.orders_table.verticalHeader().setVisible(False)
        self.orders_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        orders_header = self.orders_table.horizontalHeader()
        orders_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        orders_header.setStretchLastSection(False)

        orders_group_layout.addWidget(self.orders_table)

        positions_layout.addWidget(positions_group)
        positions_layout.addWidget(orders_group)

        layout.addWidget(order_panel)
        layout.addWidget(positions_panel, 2)

        self.tab_widget.addTab(tab, "Manual Trader")

    def create_auto_trader_tab(self):
        """Create the Auto Trader tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Auto trading controls
        controls_group = QGroupBox("Automated Trading Controls")
        controls_layout = QHBoxLayout(controls_group)

        self.auto_trading_enabled = QCheckBox("Enable Auto Trading")
        self.auto_trading_symbols = QLineEdit("DOGE/USDT")
        self.auto_trading_interval = QSpinBox()
        self.auto_trading_interval.setRange(10, 3600)
        self.auto_trading_interval.setValue(30)
        self.auto_trading_interval.setSuffix(" seconds")

        self.start_auto_button = QPushButton("START AUTO TRADING")
        self.stop_auto_button = QPushButton("STOP AUTO TRADING")
        self.stop_auto_button.setEnabled(False)

        controls_layout.addWidget(self.auto_trading_enabled)
        controls_layout.addWidget(QLabel("Symbols:"))
        controls_layout.addWidget(self.auto_trading_symbols)
        controls_layout.addWidget(QLabel("Interval:"))
        controls_layout.addWidget(self.auto_trading_interval)
        controls_layout.addWidget(self.start_auto_button)
        controls_layout.addWidget(self.stop_auto_button)

        # Strategy queue
        queue_group = QGroupBox("Strategy Queue")
        queue_layout = QVBoxLayout(queue_group)

        self.strategy_queue_table = QTableWidget(0, 6)
        self.strategy_queue_table.setHorizontalHeaderLabels([
            "Symbol", "Strategy", "Status", "Next Execution", "Last Result", "Actions"
        ])

        # Apply Matrix theme styling to strategy queue table
        self.strategy_queue_table.verticalHeader().setVisible(False)
        self.strategy_queue_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        strategy_header = self.strategy_queue_table.horizontalHeader()
        strategy_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        strategy_header.setStretchLastSection(False)

        queue_layout.addWidget(self.strategy_queue_table)

        # Auto trading log
        auto_log_group = QGroupBox("Auto Trading Log")
        auto_log_layout = QVBoxLayout(auto_log_group)

        self.auto_trading_log = QTextEdit()
        self.auto_trading_log.setReadOnly(True)
        self.auto_trading_log.setMaximumHeight(150)

        auto_log_layout.addWidget(self.auto_trading_log)

        layout.addWidget(controls_group)
        layout.addWidget(queue_group)
        layout.addWidget(auto_log_group)

        self.tab_widget.addTab(tab, "Auto Trader")

    def create_microstructure_tab(self):
        """Create the Microstructure tab"""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # Order book panel
        orderbook_panel = QGroupBox("Order Book")
        orderbook_layout = QVBoxLayout(orderbook_panel)

        self.orderbook_symbol = QComboBox()
        self.orderbook_symbol.addItems(["DOGE/USDT", "BTC/USDT", "ETH/USDT"])

        self.orderbook_table = QTableWidget(20, 3)
        self.orderbook_table.setHorizontalHeaderLabels(["Price", "Size", "Side"])

        # Apply Matrix theme styling to orderbook table
        self.orderbook_table.verticalHeader().setVisible(False)
        self.orderbook_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        orderbook_header = self.orderbook_table.horizontalHeader()
        orderbook_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        orderbook_header.setStretchLastSection(False)

        orderbook_layout.addWidget(QLabel("Symbol:"))
        orderbook_layout.addWidget(self.orderbook_symbol)
        orderbook_layout.addWidget(self.orderbook_table)

        # Market depth analysis
        depth_panel = QGroupBox("Market Depth Analysis")
        depth_layout = QVBoxLayout(depth_panel)

        self.bid_ask_spread_label = QLabel("Bid-Ask Spread: --")
        self.market_depth_label = QLabel("Market Depth: --")
        self.order_flow_label = QLabel("Order Flow: --")
        self.liquidity_score_label = QLabel("Liquidity Score: --")

        depth_layout.addWidget(self.bid_ask_spread_label)
        depth_layout.addWidget(self.market_depth_label)
        depth_layout.addWidget(self.order_flow_label)
        depth_layout.addWidget(self.liquidity_score_label)
        depth_layout.addStretch()

        # Recent trades
        trades_panel = QGroupBox("Recent Trades")
        trades_layout = QVBoxLayout(trades_panel)

        self.trades_table = QTableWidget(15, 4)
        self.trades_table.setHorizontalHeaderLabels(["Time", "Price", "Size", "Side"])

        # Apply Matrix theme styling to trades table
        self.trades_table.verticalHeader().setVisible(False)
        self.trades_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        trades_header = self.trades_table.horizontalHeader()
        trades_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        trades_header.setStretchLastSection(False)

        trades_layout.addWidget(self.trades_table)

        layout.addWidget(orderbook_panel)
        layout.addWidget(depth_panel)
        layout.addWidget(trades_panel)

        self.tab_widget.addTab(tab, "Microstructure")

    def create_performance_dashboard_tab(self):
        """Create the Performance Dashboard tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Performance metrics
        metrics_layout = QHBoxLayout()

        # Overall performance
        overall_group = QGroupBox("Overall Performance")
        overall_layout = QVBoxLayout(overall_group)

        self.total_pnl_label = QLabel("Total PnL: $0.00")
        self.total_pnl_label.setStyleSheet(f"font-size: {MatrixTheme.FONT_SIZE_LARGE}px; font-weight: bold;")

        self.win_rate_label = QLabel("Win Rate: --%")
        self.total_trades_label = QLabel("Total Trades: 0")
        self.avg_trade_label = QLabel("Avg Trade: $0.00")
        self.max_drawdown_label = QLabel("Max Drawdown: --%")

        overall_layout.addWidget(self.total_pnl_label)
        overall_layout.addWidget(self.win_rate_label)
        overall_layout.addWidget(self.total_trades_label)
        overall_layout.addWidget(self.avg_trade_label)
        overall_layout.addWidget(self.max_drawdown_label)

        # Daily performance
        daily_group = QGroupBox("Daily Performance")
        daily_layout = QVBoxLayout(daily_group)

        self.daily_pnl_label = QLabel("Today's PnL: $0.00")
        self.daily_trades_label = QLabel("Today's Trades: 0")
        self.daily_win_rate_label = QLabel("Today's Win Rate: --%")

        daily_layout.addWidget(self.daily_pnl_label)
        daily_layout.addWidget(self.daily_trades_label)
        daily_layout.addWidget(self.daily_win_rate_label)
        daily_layout.addStretch()

        # ML Model performance
        ml_perf_group = QGroupBox("ML Model Performance")
        ml_perf_layout = QVBoxLayout(ml_perf_group)

        self.ml_accuracy_table = QTableWidget(3, 3)
        self.ml_accuracy_table.setHorizontalHeaderLabels(["Model", "Accuracy", "Last Trained"])
        self.ml_accuracy_table.setMaximumHeight(120)

        # Apply Matrix theme styling to ML accuracy table
        self.ml_accuracy_table.verticalHeader().setVisible(False)
        self.ml_accuracy_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        ml_accuracy_header = self.ml_accuracy_table.horizontalHeader()
        ml_accuracy_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        ml_accuracy_header.setStretchLastSection(False)

        ml_perf_layout.addWidget(self.ml_accuracy_table)

        metrics_layout.addWidget(overall_group)
        metrics_layout.addWidget(daily_group)
        metrics_layout.addWidget(ml_perf_group)

        # Trade history
        history_group = QGroupBox("Recent Trade History")
        history_layout = QVBoxLayout(history_group)

        self.trade_history_table = QTableWidget(0, 8)
        self.trade_history_table.setHorizontalHeaderLabels([
            "Time", "Symbol", "Side", "Size", "Entry", "Exit", "PnL", "Strategy"
        ])

        # Apply Matrix theme styling to trade history table
        self.trade_history_table.verticalHeader().setVisible(False)
        self.trade_history_table.setStyleSheet("""
            QTableWidget {
                background-color: #000000;
                gridline-color: #003300;
            }
            QTableWidget::item {
                background-color: #001100;
                color: #00FF00;
            }
            QHeaderView::section {
                background-color: #003300;
                color: #00FF00;
            }
            QTableWidget QTableCornerButton::section {
                background-color: #000000;
            }
            QTableWidget::viewport {
                background-color: #000000;
            }
        """)
        trade_history_header = self.trade_history_table.horizontalHeader()
        trade_history_header.setSectionResizeMode(QHeaderView.ResizeToContents)
        trade_history_header.setStretchLastSection(False)

        history_layout.addWidget(self.trade_history_table)

        layout.addLayout(metrics_layout)
        layout.addWidget(history_group)

        self.tab_widget.addTab(tab, "Performance Dashboard")

    def create_settings_tab(self):
        """Create the Settings tab with dynamic configuration"""
        tab = QWidget()
        layout = QHBoxLayout(tab)

        # Left panel - Trading Settings
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setMaximumWidth(400)

        # Timeframe weights
        timeframe_group = QGroupBox("Timeframe Weights")
        timeframe_layout = QVBoxLayout(timeframe_group)

        self.timeframe_1m_weight = QDoubleSpinBox()
        self.timeframe_1m_weight.setRange(0.0, 1.0)
        self.timeframe_1m_weight.setSingleStep(0.1)
        self.timeframe_1m_weight.setValue(0.2)
        self.timeframe_1m_weight.setDecimals(1)

        self.timeframe_5m_weight = QDoubleSpinBox()
        self.timeframe_5m_weight.setRange(0.0, 1.0)
        self.timeframe_5m_weight.setSingleStep(0.1)
        self.timeframe_5m_weight.setValue(0.3)
        self.timeframe_5m_weight.setDecimals(1)

        self.timeframe_15m_weight = QDoubleSpinBox()
        self.timeframe_15m_weight.setRange(0.0, 1.0)
        self.timeframe_15m_weight.setSingleStep(0.1)
        self.timeframe_15m_weight.setValue(0.5)
        self.timeframe_15m_weight.setDecimals(1)

        timeframe_layout.addWidget(QLabel("1m Weight:"))
        timeframe_layout.addWidget(self.timeframe_1m_weight)
        timeframe_layout.addWidget(QLabel("5m Weight:"))
        timeframe_layout.addWidget(self.timeframe_5m_weight)
        timeframe_layout.addWidget(QLabel("15m Weight:"))
        timeframe_layout.addWidget(self.timeframe_15m_weight)

        # Leverage settings
        leverage_group = QGroupBox("Leverage Management")
        leverage_layout = QVBoxLayout(leverage_group)

        self.base_balance = QDoubleSpinBox()
        self.base_balance.setRange(1.0, 100000.0)
        self.base_balance.setValue(50.0)
        self.base_balance.setPrefix("$")

        self.max_risk_per_trade = QDoubleSpinBox()
        self.max_risk_per_trade.setRange(0.1, 20.0)
        self.max_risk_per_trade.setValue(5.0)
        self.max_risk_per_trade.setSuffix("%")

        self.conservative_mode = QCheckBox("Conservative Position Sizing")

        leverage_layout.addWidget(QLabel("Base Balance:"))
        leverage_layout.addWidget(self.base_balance)
        leverage_layout.addWidget(QLabel("Max Risk per Trade:"))
        leverage_layout.addWidget(self.max_risk_per_trade)
        leverage_layout.addWidget(self.conservative_mode)

        # ML Model settings
        ml_group = QGroupBox("ML Model Settings")
        ml_layout = QVBoxLayout(ml_group)

        self.confidence_threshold = QDoubleSpinBox()
        self.confidence_threshold.setRange(0.1, 0.9)
        self.confidence_threshold.setValue(0.6)
        self.confidence_threshold.setSingleStep(0.1)
        self.confidence_threshold.setDecimals(1)

        self.retrain_interval = QSpinBox()
        self.retrain_interval.setRange(1, 168)
        self.retrain_interval.setValue(24)
        self.retrain_interval.setSuffix(" hours")

        ml_layout.addWidget(QLabel("Confidence Threshold:"))
        ml_layout.addWidget(self.confidence_threshold)
        ml_layout.addWidget(QLabel("Retrain Interval:"))
        ml_layout.addWidget(self.retrain_interval)

        # Signal hierarchy weights
        hierarchy_group = QGroupBox("Signal Hierarchy Weights")
        hierarchy_layout = QVBoxLayout(hierarchy_group)

        self.ml_weight = QDoubleSpinBox()
        self.ml_weight.setRange(0.0, 1.0)
        self.ml_weight.setValue(0.35)
        self.ml_weight.setSingleStep(0.05)
        self.ml_weight.setDecimals(2)

        self.technical_weight = QDoubleSpinBox()
        self.technical_weight.setRange(0.0, 1.0)
        self.technical_weight.setValue(0.25)
        self.technical_weight.setSingleStep(0.05)
        self.technical_weight.setDecimals(2)

        self.mtf_weight = QDoubleSpinBox()
        self.mtf_weight.setRange(0.0, 1.0)
        self.mtf_weight.setValue(0.20)
        self.mtf_weight.setSingleStep(0.05)
        self.mtf_weight.setDecimals(2)

        hierarchy_layout.addWidget(QLabel("ML Ensemble Weight:"))
        hierarchy_layout.addWidget(self.ml_weight)
        hierarchy_layout.addWidget(QLabel("Technical Signals Weight:"))
        hierarchy_layout.addWidget(self.technical_weight)
        hierarchy_layout.addWidget(QLabel("Multi-Timeframe Weight:"))
        hierarchy_layout.addWidget(self.mtf_weight)

        # Buttons
        button_layout = QHBoxLayout()
        self.save_settings_button = QPushButton("SAVE SETTINGS")
        self.save_settings_button.clicked.connect(self.save_settings)

        self.load_settings_button = QPushButton("LOAD SETTINGS")
        self.load_settings_button.clicked.connect(self.load_settings)

        self.reset_settings_button = QPushButton("RESET TO DEFAULTS")
        self.reset_settings_button.clicked.connect(self.reset_settings)

        button_layout.addWidget(self.save_settings_button)
        button_layout.addWidget(self.load_settings_button)
        button_layout.addWidget(self.reset_settings_button)

        left_layout.addWidget(timeframe_group)
        left_layout.addWidget(leverage_group)
        left_layout.addWidget(ml_group)
        left_layout.addWidget(hierarchy_group)
        left_layout.addLayout(button_layout)
        left_layout.addStretch()

        # Right panel - System Information
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # System status
        system_group = QGroupBox("System Information")
        system_layout = QVBoxLayout(system_group)

        self.system_info_text = QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setMaximumHeight(200)

        system_layout.addWidget(self.system_info_text)

        # Configuration log
        config_group = QGroupBox("Configuration Log")
        config_layout = QVBoxLayout(config_group)

        self.config_log = QTextEdit()
        self.config_log.setReadOnly(True)

        config_layout.addWidget(self.config_log)

        right_layout.addWidget(system_group)
        right_layout.addWidget(config_group)

        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

        self.tab_widget.addTab(tab, "Settings")

    def setup_timers(self):
        """Setup timers for real-time updates"""
        # Time display timer
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # Update every second

        # Auto refresh timer
        self.auto_refresh_timer = QTimer()
        self.auto_refresh_timer.timeout.connect(self.auto_refresh_analysis)

        # Update system info after UI is created
        QTimer.singleShot(100, self.update_system_info)

    def update_time_display(self):
        """Update the time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def analyze_symbol(self):
        """Analyze the selected symbol"""
        if self.trading_worker and self.trading_worker.running:
            self.statusBar().showMessage("Analysis already in progress...")
            return

        symbol = self.symbol_combo.currentText()
        use_live_data = self.live_data_checkbox.isChecked()

        # Update UI state
        self.analyze_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.system_status_label.setText("SYSTEM: ANALYZING")
        self.system_status_label.setStyleSheet(f"color: {MatrixTheme.YELLOW};")

        # Start analysis worker
        self.trading_worker = TradingSystemWorker(symbol, use_live_data)
        self.trading_worker.result_ready.connect(self.on_analysis_complete)
        self.trading_worker.error_occurred.connect(self.on_analysis_error)
        self.trading_worker.status_update.connect(self.statusBar().showMessage)
        self.trading_worker.start()

        # Start auto refresh if enabled
        if self.auto_refresh_checkbox.isChecked():
            self.auto_refresh_timer.start(30000)  # 30 seconds

        self.log_message(f"Started analysis for {symbol}")

    def stop_analysis(self):
        """Stop the current analysis"""
        if self.trading_worker:
            self.trading_worker.stop()

        self.auto_refresh_timer.stop()

        # Update UI state
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.system_status_label.setText("SYSTEM: READY")
        self.system_status_label.setStyleSheet(f"color: {MatrixTheme.GREEN};")

        self.statusBar().showMessage("Analysis stopped")
        self.log_message("Analysis stopped by user")

    def auto_refresh_analysis(self):
        """Auto refresh analysis if enabled"""
        if self.auto_refresh_checkbox.isChecked() and not (self.trading_worker and self.trading_worker.running):
            self.analyze_symbol()

    def on_analysis_complete(self, result: dict):
        """Handle completed analysis"""
        try:
            self.current_data = result
            self.update_analysis_display(result)

            # Update UI state
            self.analyze_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.system_status_label.setText("SYSTEM: READY")
            self.system_status_label.setStyleSheet(f"color: {MatrixTheme.GREEN};")

            self.statusBar().showMessage(f"Analysis complete: {result['decision']}")
            self.log_message(f"Analysis complete for {result['symbol']}: {result['decision']}")

        except Exception as e:
            self.on_analysis_error(f"Error processing results: {e}")

    def on_analysis_error(self, error_msg: str):
        """Handle analysis errors"""
        self.analyze_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.system_status_label.setText("SYSTEM: ERROR")
        self.system_status_label.setStyleSheet(f"color: {MatrixTheme.RED};")

        self.statusBar().showMessage(f"Error: {error_msg}")
        self.log_message(f"ERROR: {error_msg}")

        # Show error in warnings
        self.warnings_list.append(f"[{datetime.now().strftime('%H:%M:%S')}] {error_msg}")

    def update_analysis_display(self, result: dict):
        """Update the display with analysis results"""
        try:
            data = result.get('data', {})

            # Update basic decision info
            decision = result.get('decision', 'UNKNOWN')
            self.decision_label.setText(f"Decision: {decision}")

            # Color code the decision
            if decision == 'LONG':
                color = MatrixTheme.GREEN
            elif decision == 'SHORT':
                color = MatrixTheme.RED
            else:
                color = MatrixTheme.YELLOW

            self.decision_label.setStyleSheet(f"color: {color}; font-size: {MatrixTheme.FONT_SIZE_LARGE}px; font-weight: bold;")

            # Update confidence and timestamp
            confidence = "Unknown"
            if 'signal_hierarchy' in data:
                confidence = f"{data['signal_hierarchy'].get('confidence', 0) * 100:.1f}%"

            self.confidence_label.setText(f"Confidence: {confidence}")
            self.timestamp_label.setText(f"Last Update: {datetime.now().strftime('%H:%M:%S')}")

            # Update ML models table
            self.update_ml_models_table(data)

            # Update leverage analysis
            self.update_leverage_display(data)

            # Update signal hierarchy table
            self.update_hierarchy_table(data)

            # Update market analysis
            self.update_market_analysis(data)

            # Update warnings
            self.update_warnings_display(data)

        except Exception as e:
            logger.error(f"Error updating display: {e}")
            self.log_message(f"Display update error: {e}")

    def update_ml_models_table(self, data: dict):
        """Update the ML models status table"""
        try:
            ml_predictions = data.get('ml_predictions', {})

            self.ml_status_table.setRowCount(0)
            row = 0

            for model_name, prediction in ml_predictions.items():
                if isinstance(prediction, dict):
                    self.ml_status_table.insertRow(row)

                    # Model name
                    self.ml_status_table.setItem(row, 0, QTableWidgetItem(model_name.upper()))

                    # Decision
                    decision = prediction.get('direction', 'UNKNOWN')
                    decision_item = QTableWidgetItem(decision)
                    if decision == 'LONG':
                        decision_item.setForeground(QColor(MatrixTheme.GREEN))
                    elif decision == 'SHORT':
                        decision_item.setForeground(QColor(MatrixTheme.RED))
                    else:
                        decision_item.setForeground(QColor(MatrixTheme.YELLOW))

                    self.ml_status_table.setItem(row, 1, decision_item)

                    # Confidence
                    confidence = prediction.get('direction_confidence', 0)
                    confidence_item = QTableWidgetItem(f"{confidence:.1%}")
                    self.ml_status_table.setItem(row, 2, confidence_item)

                    row += 1

            self.ml_status_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error updating ML models table: {e}")

    def update_leverage_display(self, data: dict):
        """Update leverage analysis display"""
        try:
            leverage_data = data.get('leverage_position_sizing', {})

            if leverage_data:
                self.max_leverage_label.setText(f"Max Available: {leverage_data.get('max_leverage', 0):.1f}x")
                self.recommended_leverage_label.setText(f"Recommended: {leverage_data.get('recommended_leverage', 0):.1f}x")
                self.effective_leverage_label.setText(f"Effective: {leverage_data.get('effective_leverage', 0):.1f}x")

                position_units = leverage_data.get('position_units', 0)
                position_usd = leverage_data.get('position_usd', 0)
                self.position_size_label.setText(f"Position Size: {position_units:.2f} units (${position_usd:.2f})")

                risk_usd = leverage_data.get('risk_per_trade_usd', 0)
                self.risk_label.setText(f"Risk per Trade: ${risk_usd:.2f}")
            else:
                self.max_leverage_label.setText("Max Available: --")
                self.recommended_leverage_label.setText("Recommended: --")
                self.effective_leverage_label.setText("Effective: --")
                self.position_size_label.setText("Position Size: --")
                self.risk_label.setText("Risk per Trade: --")

        except Exception as e:
            logger.error(f"Error updating leverage display: {e}")

    def update_hierarchy_table(self, data: dict):
        """Update signal hierarchy table"""
        try:
            hierarchy_data = data.get('signal_hierarchy', {})

            if 'signals_analysis' in hierarchy_data:
                # Parse signals analysis string
                signals_text = hierarchy_data['signals_analysis']
                signals = signals_text.split(';')

                self.hierarchy_table.setRowCount(len(signals))

                for i, signal in enumerate(signals):
                    if ':' in signal:
                        parts = signal.strip().split(':')
                        if len(parts) >= 2:
                            source = parts[0].strip()
                            decision_conf = parts[1].strip()

                            # Extract decision and confidence
                            if '(' in decision_conf and ')' in decision_conf:
                                decision = decision_conf.split('(')[0].strip()
                                confidence = decision_conf.split('(')[1].split(')')[0].strip()
                            else:
                                decision = decision_conf
                                confidence = "--"

                            self.hierarchy_table.setItem(i, 0, QTableWidgetItem(source))

                            decision_item = QTableWidgetItem(decision)
                            if decision == 'LONG':
                                decision_item.setForeground(QColor(MatrixTheme.GREEN))
                            elif decision == 'SHORT':
                                decision_item.setForeground(QColor(MatrixTheme.RED))
                            else:
                                decision_item.setForeground(QColor(MatrixTheme.YELLOW))

                            self.hierarchy_table.setItem(i, 1, decision_item)
                            self.hierarchy_table.setItem(i, 2, QTableWidgetItem(confidence))
                            self.hierarchy_table.setItem(i, 3, QTableWidgetItem("--"))  # Weight placeholder

                self.hierarchy_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"Error updating hierarchy table: {e}")

    def update_market_analysis(self, data: dict):
        """Update market analysis display"""
        try:
            # Market regime
            regime = data.get('market_regime', 'unknown')
            self.market_regime_label.setText(f"Market Regime: {regime.upper()}")

            # Multi-timeframe analysis
            mtf_data = data.get('multi_timeframe_analysis', {})
            if mtf_data:
                trend_strength = mtf_data.get('trend_strength', 0)
                self.trend_strength_label.setText(f"Trend Strength: {trend_strength:.2f}")

                # Volatility (if available)
                volatility = mtf_data.get('volatility', 0)
                self.volatility_label.setText(f"Volatility: {volatility:.2%}")

            # Liquidity score from position sizing
            leverage_data = data.get('leverage_position_sizing', {})
            if leverage_data and 'liquidity_score' in leverage_data:
                liquidity = leverage_data['liquidity_score']
                self.liquidity_label.setText(f"Liquidity Score: {liquidity:.3f}")

        except Exception as e:
            logger.error(f"Error updating market analysis: {e}")

    def update_warnings_display(self, data: dict):
        """Update risk warnings display"""
        try:
            warnings = []

            # Get warnings from leverage position sizing
            leverage_data = data.get('leverage_position_sizing', {})
            if leverage_data and 'risk_warnings' in leverage_data:
                warnings.extend(leverage_data['risk_warnings'])

            # Display warnings
            if warnings:
                warning_text = f"[{datetime.now().strftime('%H:%M:%S')}] WARNINGS:\n"
                for warning in warnings:
                    warning_text += f"• {warning}\n"

                self.warnings_list.append(warning_text)

        except Exception as e:
            logger.error(f"Error updating warnings: {e}")

    def log_message(self, message: str):
        """Add message to analysis log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.analysis_log.append(log_entry)

        # Keep log size manageable
        if self.analysis_log.document().blockCount() > 100:
            cursor = self.analysis_log.textCursor()
            cursor.movePosition(QTextCursor.Start)
            cursor.select(QTextCursor.LineUnderCursor)
            cursor.removeSelectedText()

    def start_scanner(self):
        """Start the multi-symbol scanner"""
        self.scan_button.setEnabled(False)
        self.stop_scan_button.setEnabled(True)
        self.statusBar().showMessage("Scanner started")
        # TODO: Implement scanner logic

    def stop_scanner(self):
        """Stop the multi-symbol scanner"""
        self.scan_button.setEnabled(True)
        self.stop_scan_button.setEnabled(False)
        self.statusBar().showMessage("Scanner stopped")
        # TODO: Implement scanner stop logic

    def save_settings(self):
        """Save current settings to file"""
        try:
            settings = {
                'timeframe_weights': {
                    '1m': self.timeframe_1m_weight.value(),
                    '5m': self.timeframe_5m_weight.value(),
                    '15m': self.timeframe_15m_weight.value()
                },
                'leverage_settings': {
                    'base_balance': self.base_balance.value(),
                    'max_risk_per_trade': self.max_risk_per_trade.value(),
                    'conservative_mode': self.conservative_mode.isChecked()
                },
                'ml_settings': {
                    'confidence_threshold': self.confidence_threshold.value(),
                    'retrain_interval': self.retrain_interval.value()
                },
                'hierarchy_weights': {
                    'ml_weight': self.ml_weight.value(),
                    'technical_weight': self.technical_weight.value(),
                    'mtf_weight': self.mtf_weight.value()
                },
                'ui_settings': {
                    'auto_refresh': self.auto_refresh_checkbox.isChecked(),
                    'default_symbol': self.symbol_combo.currentText()
                }
            }

            with open('epinnox_settings.json', 'w') as f:
                json.dump(settings, f, indent=2)

            self.statusBar().showMessage("Settings saved successfully")
            self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] Settings saved to epinnox_settings.json")

        except Exception as e:
            error_msg = f"Error saving settings: {e}"
            self.statusBar().showMessage(error_msg)
            self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] ERROR: {error_msg}")

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists('epinnox_settings.json'):
                with open('epinnox_settings.json', 'r') as f:
                    settings = json.load(f)

                # Apply timeframe weights
                if 'timeframe_weights' in settings:
                    tw = settings['timeframe_weights']
                    self.timeframe_1m_weight.setValue(tw.get('1m', 0.2))
                    self.timeframe_5m_weight.setValue(tw.get('5m', 0.3))
                    self.timeframe_15m_weight.setValue(tw.get('15m', 0.5))

                # Apply leverage settings
                if 'leverage_settings' in settings:
                    ls = settings['leverage_settings']
                    self.base_balance.setValue(ls.get('base_balance', 50.0))
                    self.max_risk_per_trade.setValue(ls.get('max_risk_per_trade', 5.0))
                    self.conservative_mode.setChecked(ls.get('conservative_mode', False))

                # Apply ML settings
                if 'ml_settings' in settings:
                    ms = settings['ml_settings']
                    self.confidence_threshold.setValue(ms.get('confidence_threshold', 0.6))
                    self.retrain_interval.setValue(ms.get('retrain_interval', 24))

                # Apply hierarchy weights
                if 'hierarchy_weights' in settings:
                    hw = settings['hierarchy_weights']
                    self.ml_weight.setValue(hw.get('ml_weight', 0.35))
                    self.technical_weight.setValue(hw.get('technical_weight', 0.25))
                    self.mtf_weight.setValue(hw.get('mtf_weight', 0.20))

                # Apply UI settings
                if 'ui_settings' in settings:
                    us = settings['ui_settings']
                    self.auto_refresh_checkbox.setChecked(us.get('auto_refresh', True))
                    default_symbol = us.get('default_symbol', 'DOGE/USDT')
                    index = self.symbol_combo.findText(default_symbol)
                    if index >= 0:
                        self.symbol_combo.setCurrentIndex(index)

                self.statusBar().showMessage("Settings loaded successfully")
                if hasattr(self, 'config_log'):
                    self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] Settings loaded from epinnox_settings.json")

            else:
                self.statusBar().showMessage("No settings file found")
                if hasattr(self, 'config_log'):
                    self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] No settings file found, using defaults")

        except Exception as e:
            error_msg = f"Error loading settings: {e}"
            self.statusBar().showMessage(error_msg)
            if hasattr(self, 'config_log'):
                self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] ERROR: {error_msg}")

    def reset_settings(self):
        """Reset settings to defaults"""
        try:
            # Reset timeframe weights
            self.timeframe_1m_weight.setValue(0.2)
            self.timeframe_5m_weight.setValue(0.3)
            self.timeframe_15m_weight.setValue(0.5)

            # Reset leverage settings
            self.base_balance.setValue(50.0)
            self.max_risk_per_trade.setValue(5.0)
            self.conservative_mode.setChecked(False)

            # Reset ML settings
            self.confidence_threshold.setValue(0.6)
            self.retrain_interval.setValue(24)

            # Reset hierarchy weights
            self.ml_weight.setValue(0.35)
            self.technical_weight.setValue(0.25)
            self.mtf_weight.setValue(0.20)

            # Reset UI settings
            self.auto_refresh_checkbox.setChecked(True)
            self.symbol_combo.setCurrentText("DOGE/USDT")

            self.statusBar().showMessage("Settings reset to defaults")
            self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] Settings reset to defaults")

        except Exception as e:
            error_msg = f"Error resetting settings: {e}"
            self.statusBar().showMessage(error_msg)
            self.config_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] ERROR: {error_msg}")

    def update_system_info(self):
        """Update system information display"""
        try:
            # Check if UI components are available
            if not hasattr(self, 'system_info_text'):
                return

            info_text = f"""EPINNOX v6 INTEGRATED TRADING SYSTEM
{'='*50}

System Status: OPERATIONAL
Python Version: {sys.version.split()[0]}
PyQt5 Version: Available
Exchange: HTX (Huobi)
ML Models: SVM, Random Forest, LSTM (if TensorFlow available)

Features Enabled:
• Dynamic Leverage Management
• Smart Position Sizing
• ML Model Predictions
• Signal Hierarchy Resolution
• Multi-Timeframe Analysis
• Market Regime Detection
• Real-Time Risk Management

Current Configuration:
• Base Balance: ${getattr(self, 'base_balance', type('obj', (object,), {'value': lambda: 50.0})).value():.2f}
• Max Risk per Trade: {getattr(self, 'max_risk_per_trade', type('obj', (object,), {'value': lambda: 5.0})).value():.1f}%
• Conservative Mode: {'Enabled' if getattr(self, 'conservative_mode', type('obj', (object,), {'isChecked': lambda: False})).isChecked() else 'Disabled'}
• Auto Refresh: {'Enabled' if getattr(self, 'auto_refresh_checkbox', type('obj', (object,), {'isChecked': lambda: True})).isChecked() else 'Disabled'}

Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

            self.system_info_text.setPlainText(info_text)

        except Exception as e:
            logger.error(f"Error updating system info: {e}")

    def closeEvent(self, event):
        """Handle application close event"""
        try:
            # Stop any running workers
            if self.trading_worker and self.trading_worker.running:
                self.trading_worker.stop()

            # Stop timers
            self.time_timer.stop()
            self.auto_refresh_timer.stop()

            # Save settings on exit
            self.save_settings()

            logger.info("Epinnox v6 GUI closed successfully")
            event.accept()

        except Exception as e:
            logger.error(f"Error during close: {e}")
            event.accept()

def main():
    """Main application entry point"""
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("Epinnox v6 Integrated Trading System")
        app.setApplicationVersion("6.0")

        # Set application icon (if available)
        try:
            app.setWindowIcon(QIcon("icon.png"))
        except:
            pass  # Icon file not found, continue without it

        # Create and show main window
        window = EpinnoxMainWindow()
        window.show()

        # Load settings on startup
        window.load_settings()

        logger.info("Epinnox v6 GUI started successfully")

        # Run application
        sys.exit(app.exec_())

    except Exception as e:
        logger.error(f"Error starting application: {e}")
        print(f"Error starting Epinnox v6 GUI: {e}")
        print("\nMake sure you have:")
        print("1. PyQt5 installed: pip install PyQt5")
        print("2. All Epinnox modules available")
        print("3. Running from the Epinnox_v6 directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
