#!/usr/bin/env python3
"""
Test script to verify ML Prediction Accuracy GUI integration
This script tests the new prediction accuracy tracking feature
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_ml_prediction_accuracy_gui():
    """Test the ML Prediction Accuracy feature in the GUI"""
    print("🧪 Testing ML Prediction Accuracy GUI Integration")
    print("=" * 60)
    
    try:
        # Import the main interface
        from launch_epinnox import EpinnoxTradingInterface
        
        # Create QApplication
        app = QApplication(sys.argv)
        
        # Create the main interface
        interface = EpinnoxTradingInterface()
        
        # Check if prediction tracker is initialized
        if hasattr(interface, 'prediction_tracker'):
            print("✅ Prediction tracker initialized successfully")
            
            # Check if ML models table has 4 columns
            if hasattr(interface, 'ml_models_table'):
                column_count = interface.ml_models_table.columnCount()
                if column_count == 4:
                    print("✅ ML Models table has 4 columns (including Actual Confidence)")
                    
                    # Check column headers
                    headers = []
                    for i in range(column_count):
                        header = interface.ml_models_table.horizontalHeaderItem(i)
                        if header:
                            headers.append(header.text())
                    
                    print(f"📊 Column headers: {headers}")
                    
                    if "Actual Confidence" in headers:
                        print("✅ 'Actual Confidence' column found")
                    else:
                        print("❌ 'Actual Confidence' column missing")
                        
                else:
                    print(f"❌ ML Models table has {column_count} columns, expected 4")
            else:
                print("❌ ML Models table not found")
                
            # Test prediction recording
            print("\n🔮 Testing prediction recording...")
            
            # Simulate a prediction
            test_symbol = "DOGE/USDT:USDT"
            test_price = 0.170000
            
            interface.prediction_tracker.record_prediction(
                model_name="SVM",
                prediction="LONG",
                confidence=0.75,
                price=test_price,
                symbol=test_symbol
            )
            
            interface.prediction_tracker.record_prediction(
                model_name="Random Forest", 
                prediction="SHORT",
                confidence=0.68,
                price=test_price,
                symbol=test_symbol
            )
            
            interface.prediction_tracker.record_prediction(
                model_name="LSTM",
                prediction="WAIT", 
                confidence=0.82,
                price=test_price,
                symbol=test_symbol
            )
            
            print("✅ Test predictions recorded")
            
            # Test price update
            print("\n📈 Testing price updates...")
            
            # Simulate price changes
            new_price = 0.171000  # 0.6% increase
            interface.prediction_tracker.update_current_price(test_symbol, new_price)
            
            print(f"✅ Price updated from {test_price:.6f} to {new_price:.6f}")
            
            # Check prediction stats
            print("\n📊 Checking prediction statistics...")
            
            for model in ["SVM", "Random Forest", "LSTM"]:
                stats = interface.prediction_tracker.get_prediction_stats(model)
                print(f"  {model}: {stats.get('total_predictions', 0)} predictions")
            
            print("\n✅ GUI Integration test completed successfully!")
            print("\n📋 Next steps:")
            print("  1. Launch the GUI: python launch_epinnox.py")
            print("  2. Click 'ANALYZE SYMBOL' to generate ML predictions")
            print("  3. Wait 5 minutes to see accuracy percentages")
            print("  4. Verify color coding: Green (≥70%), Yellow (50-69%), Red (<50%)")
            
        else:
            print("❌ Prediction tracker not initialized")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ml_prediction_accuracy_gui()
