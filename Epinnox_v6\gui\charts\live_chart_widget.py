"""
Live Chart Widget for Epinnox v6
Enhanced PyQtGraph chart with real-time data integration and professional features
"""

import pyqtgraph as pg
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QComboBox, QLabel, QCheckBox
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QColor
import numpy as np
from datetime import datetime
from typing import List, Dict, Optional

from ..matrix_theme import MatrixTheme
try:
    from .candlestick_chart import CandlestickChart
except ImportError:
    # Fallback if candlestick chart not available
    CandlestickChart = None


class LiveChartWidget(QWidget):
    """
    Enhanced chart widget with live data integration
    Supports candlestick charts, real-time updates, and trading overlays
    """
    
    # Signals
    chart_clicked = pyqtSignal(float, float)  # price, timestamp
    symbol_changed = pyqtSignal(str)  # new symbol
    timeframe_changed = pyqtSignal(str)  # new timeframe
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Configuration
        self.current_symbol = "DOGE/USDT:USDT"
        self.current_timeframe = "1m"
        self.chart_type = "Candlestick"
        self.show_volume = True
        self.show_bid_ask = True
        
        # Data storage
        self.ohlcv_data = []
        self.volume_data = []
        self.bid_ask_data = {"bid": None, "ask": None}
        self.trade_data = []  # Store individual trades
        self.latest_price = None
        self.latest_volume = None
        
        # Chart items
        self.candlestick_item = None
        self.volume_bars = []
        self.bid_line = None
        self.ask_line = None
        self.price_line = None
        self.trade_scatter = None  # For individual trade points
        self.buy_trades = []  # Green dots for buys
        self.sell_trades = []  # Red dots for sells
        
        # Initialize UI
        self.init_ui()
        
        # Setup update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(100)  # Update every 100ms for smooth animation
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # Chart controls
        controls_layout = self.create_controls()
        layout.addLayout(controls_layout)
        
        # Main chart area
        self.chart_widget = self.create_chart()
        layout.addWidget(self.chart_widget)
        
        # Volume chart (optional)
        self.volume_widget = self.create_volume_chart()
        layout.addWidget(self.volume_widget)
        
        # Chart info panel
        info_layout = self.create_info_panel()
        layout.addLayout(info_layout)
    
    def create_controls(self) -> QHBoxLayout:
        """Create chart control panel"""
        layout = QHBoxLayout()
        
        # Symbol selector
        layout.addWidget(QLabel("Symbol:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            "DOGE/USDT:USDT", "BTC/USDT:USDT", "ETH/USDT:USDT", 
            "SOL/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT"
        ])
        self.symbol_combo.setCurrentText(self.current_symbol)
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        layout.addWidget(self.symbol_combo)
        
        # Timeframe selector
        layout.addWidget(QLabel("Timeframe:"))
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText(self.current_timeframe)
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        layout.addWidget(self.timeframe_combo)
        
        # Chart type selector
        layout.addWidget(QLabel("Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["Candlestick", "Line", "OHLC"])
        self.chart_type_combo.setCurrentText(self.chart_type)
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        layout.addWidget(self.chart_type_combo)
        
        # Display options
        self.volume_checkbox = QCheckBox("Volume")
        self.volume_checkbox.setChecked(self.show_volume)
        self.volume_checkbox.stateChanged.connect(self.on_volume_toggle)
        layout.addWidget(self.volume_checkbox)
        
        self.bid_ask_checkbox = QCheckBox("Bid/Ask")
        self.bid_ask_checkbox.setChecked(self.show_bid_ask)
        self.bid_ask_checkbox.stateChanged.connect(self.on_bid_ask_toggle)
        layout.addWidget(self.bid_ask_checkbox)
        
        layout.addStretch()
        
        # Connection status
        self.status_label = QLabel("Disconnected")
        self.status_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        return layout
    
    def create_chart(self) -> pg.PlotWidget:
        """Create main price chart"""
        # Create chart with Matrix theme
        chart = pg.PlotWidget(
            background=MatrixTheme.BLACK,
            enableMenu=False
        )
        
        # Configure chart appearance
        chart.setLabel('left', 'Price', color=MatrixTheme.GREEN)
        chart.setLabel('bottom', 'Time', color=MatrixTheme.GREEN)
        chart.showGrid(x=True, y=True, alpha=0.3)
        
        # Set Matrix theme colors for axes
        chart.getAxis('left').setPen(MatrixTheme.GREEN)
        chart.getAxis('bottom').setPen(MatrixTheme.GREEN)
        chart.getAxis('left').setTextPen(MatrixTheme.GREEN)
        chart.getAxis('bottom').setTextPen(MatrixTheme.GREEN)
        
        # Connect click events
        chart.scene().sigMouseClicked.connect(self.on_chart_clicked)
        
        # Enable crosshair cursor
        self.crosshair = pg.CrosshairROI(pos=(0, 0), size=(1, 1), pen=pg.mkPen(MatrixTheme.YELLOW, width=1))
        chart.addItem(self.crosshair)
        
        return chart
    
    def create_volume_chart(self) -> pg.PlotWidget:
        """Create volume chart below main chart"""
        volume_chart = pg.PlotWidget(
            background=MatrixTheme.BLACK,
            enableMenu=False
        )
        
        volume_chart.setLabel('left', 'Volume', color=MatrixTheme.GREEN)
        volume_chart.setFixedHeight(100)
        volume_chart.showGrid(x=True, y=True, alpha=0.3)
        
        # Set Matrix theme colors
        volume_chart.getAxis('left').setPen(MatrixTheme.GREEN)
        volume_chart.getAxis('bottom').setPen(MatrixTheme.GREEN)
        volume_chart.getAxis('left').setTextPen(MatrixTheme.GREEN)
        volume_chart.getAxis('bottom').setTextPen(MatrixTheme.GREEN)
        
        # Hide initially if volume is disabled
        if not self.show_volume:
            volume_chart.hide()
        
        return volume_chart
    
    def create_info_panel(self) -> QHBoxLayout:
        """Create chart information panel"""
        layout = QHBoxLayout()
        
        # Price info
        self.price_info = QLabel("Price: --")
        self.price_info.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        layout.addWidget(self.price_info)
        
        # Volume info
        self.volume_info = QLabel("Volume: --")
        self.volume_info.setStyleSheet(f"color: {MatrixTheme.GREEN};")
        layout.addWidget(self.volume_info)
        
        # Bid/Ask info
        self.bid_info = QLabel("Bid: --")
        self.bid_info.setStyleSheet(f"color: {MatrixTheme.GREEN};")
        layout.addWidget(self.bid_info)
        
        self.ask_info = QLabel("Ask: --")
        self.ask_info.setStyleSheet(f"color: {MatrixTheme.RED};")
        layout.addWidget(self.ask_info)
        
        layout.addStretch()
        
        # Chart instructions
        instructions = QLabel("💡 Left-click: BUY | Right-click: SELL")
        instructions.setStyleSheet(f"color: {MatrixTheme.YELLOW}; font-size: 10px;")
        layout.addWidget(instructions)
        
        return layout
    
    def update_ohlcv_data(self, ohlcv_data: List[List]):
        """Update chart with new OHLCV data"""
        try:
            if not ohlcv_data:
                return
            
            self.ohlcv_data = ohlcv_data
            
            # Extract data for plotting
            timestamps = [item[0] / 1000 for item in ohlcv_data]  # Convert to seconds
            opens = [item[1] for item in ohlcv_data]
            highs = [item[2] for item in ohlcv_data]
            lows = [item[3] for item in ohlcv_data]
            closes = [item[4] for item in ohlcv_data]
            volumes = [item[5] for item in ohlcv_data]
            
            # Update main chart based on type
            self.chart_widget.clear()
            
            if self.chart_type == "Candlestick":
                self.plot_candlesticks(timestamps, opens, highs, lows, closes)
            elif self.chart_type == "Line":
                self.plot_line_chart(timestamps, closes)
            elif self.chart_type == "OHLC":
                self.plot_ohlc_bars(timestamps, opens, highs, lows, closes)
            
            # Update volume chart
            if self.show_volume:
                self.plot_volume(timestamps, volumes)
            
            # Update bid/ask lines
            if self.show_bid_ask:
                self.update_bid_ask_lines()
            
            # Update info panel
            if closes:
                self.update_info_panel(closes[-1], volumes[-1] if volumes else 0)
                
        except Exception as e:
            print(f"Error updating OHLCV data: {e}")
    
    def plot_candlesticks(self, timestamps, opens, highs, lows, closes):
        """Plot candlestick chart"""
        try:
            # Use custom candlestick implementation
            self.candlestick_item = CandlestickChart()
            self.candlestick_item.set_data(timestamps, opens, highs, lows, closes)
            self.chart_widget.addItem(self.candlestick_item)
        except Exception as e:
            print(f"Error plotting candlesticks: {e}")
            # Fallback to line chart
            self.plot_line_chart(timestamps, closes)
    
    def plot_line_chart(self, timestamps, prices):
        """Plot line chart"""
        try:
            pen = pg.mkPen(color=MatrixTheme.GREEN, width=2)
            self.chart_widget.plot(timestamps, prices, pen=pen)
        except Exception as e:
            print(f"Error plotting line chart: {e}")
    
    def plot_ohlc_bars(self, timestamps, opens, highs, lows, closes):
        """Plot OHLC bar chart"""
        try:
            # Simple OHLC implementation using lines
            for i, (ts, o, h, l, c) in enumerate(zip(timestamps, opens, highs, lows, closes)):
                color = MatrixTheme.GREEN if c >= o else MatrixTheme.RED
                
                # High-low line
                self.chart_widget.plot([ts, ts], [l, h], pen=pg.mkPen(color, width=1))
                
                # Open tick
                self.chart_widget.plot([ts - 30, ts], [o, o], pen=pg.mkPen(color, width=1))
                
                # Close tick
                self.chart_widget.plot([ts, ts + 30], [c, c], pen=pg.mkPen(color, width=1))
                
        except Exception as e:
            print(f"Error plotting OHLC bars: {e}")
    
    def plot_volume(self, timestamps, volumes):
        """Plot volume bars"""
        try:
            self.volume_widget.clear()
            
            # Create volume bars
            width = (timestamps[1] - timestamps[0]) * 0.8 if len(timestamps) > 1 else 60
            
            for ts, vol in zip(timestamps, volumes):
                bar = pg.BarGraphItem(
                    x=[ts], height=[vol], width=width,
                    brush=pg.mkBrush(MatrixTheme.GREEN, alpha=100)
                )
                self.volume_widget.addItem(bar)
                
        except Exception as e:
            print(f"Error plotting volume: {e}")
    
    def update_bid_ask_lines(self):
        """Update bid/ask price lines"""
        try:
            # Remove existing lines
            if self.bid_line:
                self.chart_widget.removeItem(self.bid_line)
            if self.ask_line:
                self.chart_widget.removeItem(self.ask_line)
            
            # Add new lines if data available
            if self.bid_ask_data["bid"]:
                self.bid_line = pg.InfiniteLine(
                    pos=self.bid_ask_data["bid"],
                    angle=0,
                    pen=pg.mkPen(MatrixTheme.GREEN, width=1, style=pg.QtCore.Qt.DashLine)
                )
                self.chart_widget.addItem(self.bid_line)
            
            if self.bid_ask_data["ask"]:
                self.ask_line = pg.InfiniteLine(
                    pos=self.bid_ask_data["ask"],
                    angle=0,
                    pen=pg.mkPen(MatrixTheme.RED, width=1, style=pg.QtCore.Qt.DashLine)
                )
                self.chart_widget.addItem(self.ask_line)
                
        except Exception as e:
            print(f"Error updating bid/ask lines: {e}")
    
    def update_bid_ask_data(self, bid: float, ask: float):
        """Update bid/ask data"""
        self.bid_ask_data = {"bid": bid, "ask": ask}

        # Update info panel
        self.bid_info.setText(f"Bid: {bid:.6f}" if bid else "Bid: --")
        self.ask_info.setText(f"Ask: {ask:.6f}" if ask else "Ask: --")

    def update_trade_data(self, price: float, volume: float, side: str, timestamp: float):
        """Update with individual trade data"""
        try:
            # Store the trade
            trade = {
                'price': price,
                'volume': volume,
                'side': side,  # 'buy' or 'sell'
                'timestamp': timestamp
            }
            self.trade_data.append(trade)

            # Keep only recent trades (last 1000)
            if len(self.trade_data) > 1000:
                self.trade_data = self.trade_data[-1000:]

            # Update latest price and volume
            self.latest_price = price
            self.latest_volume = volume

            # Update info panel with latest trade
            self.update_info_panel(price, volume)

            # Add trade point to chart
            self.add_trade_point(price, timestamp, side)

        except Exception as e:
            print(f"Error updating trade data: {e}")

    def add_trade_point(self, price: float, timestamp: float, side: str):
        """Add a trade point to the chart"""
        try:
            # Choose color based on trade side
            color = MatrixTheme.GREEN if side == 'buy' else MatrixTheme.RED

            # Create scatter plot item for the trade
            scatter = pg.ScatterPlotItem(
                pos=[[timestamp, price]],
                size=8,
                brush=pg.mkBrush(color),
                pen=pg.mkPen(color, width=1),
                symbol='o'
            )

            # Add to chart
            self.chart_widget.addItem(scatter)

            # Store reference for cleanup
            if side == 'buy':
                self.buy_trades.append(scatter)
            else:
                self.sell_trades.append(scatter)

            # Limit number of trade points displayed (keep last 100)
            max_trades = 100
            if len(self.buy_trades) > max_trades:
                old_item = self.buy_trades.pop(0)
                self.chart_widget.removeItem(old_item)
            if len(self.sell_trades) > max_trades:
                old_item = self.sell_trades.pop(0)
                self.chart_widget.removeItem(old_item)

        except Exception as e:
            print(f"Error adding trade point: {e}")
    
    def update_info_panel(self, price: float, volume: float):
        """Update information panel"""
        self.price_info.setText(f"Price: {price:.6f}")
        self.volume_info.setText(f"Volume: {volume:,.0f}")
    
    def update_connection_status(self, connected: bool):
        """Update connection status display"""
        if connected:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.GREEN}; font-weight: bold;")
        else:
            self.status_label.setText("Disconnected")
            self.status_label.setStyleSheet(f"color: {MatrixTheme.RED}; font-weight: bold;")
    
    def update_display(self):
        """Update display elements (called by timer)"""
        # Update crosshair and other dynamic elements
        pass
    
    # Event handlers
    def on_symbol_changed(self, symbol: str):
        """Handle symbol change"""
        self.current_symbol = symbol
        self.symbol_changed.emit(symbol)
    
    def on_timeframe_changed(self, timeframe: str):
        """Handle timeframe change"""
        self.current_timeframe = timeframe
        self.timeframe_changed.emit(timeframe)
    
    def on_chart_type_changed(self, chart_type: str):
        """Handle chart type change"""
        self.chart_type = chart_type
        # Refresh chart with current data
        if self.ohlcv_data:
            self.update_ohlcv_data(self.ohlcv_data)
    
    def on_volume_toggle(self, state):
        """Handle volume display toggle"""
        self.show_volume = bool(state)
        if self.show_volume:
            self.volume_widget.show()
        else:
            self.volume_widget.hide()
    
    def on_bid_ask_toggle(self, state):
        """Handle bid/ask display toggle"""
        self.show_bid_ask = bool(state)
        if not self.show_bid_ask:
            if self.bid_line:
                self.chart_widget.removeItem(self.bid_line)
            if self.ask_line:
                self.chart_widget.removeItem(self.ask_line)
    
    def on_chart_clicked(self, event):
        """Handle chart click events"""
        try:
            if event.button() == 1:  # Left click
                pos = event.pos()
                view_box = self.chart_widget.getViewBox()
                scene_pos = view_box.mapSceneToView(pos)
                
                price = scene_pos.y()
                timestamp = scene_pos.x()
                
                self.chart_clicked.emit(price, timestamp)
                
        except Exception as e:
            print(f"Error handling chart click: {e}")
