#!/usr/bin/env python3
"""
Test script for ML Prediction Accuracy Tracker
Demonstrates the new prediction accuracy tracking feature
"""

import time
import random
from ml.prediction_accuracy_tracker import PredictionAccuracyTracker

def test_prediction_accuracy():
    """Test the prediction accuracy tracking functionality"""
    print("🧪 Testing ML Prediction Accuracy Tracker")
    print("=" * 50)
    
    # Create tracker with 1-minute evaluation window for testing
    tracker = PredictionAccuracyTracker(evaluation_window_minutes=1)
    
    # Simulate initial price
    initial_price = 0.171500
    symbol = "DOGE/USDT:USDT"
    
    print(f"📊 Initial price: {initial_price:.6f}")
    
    # Record some predictions
    models = ["SVM", "Random Forest", "LSTM"]
    predictions = ["LONG", "SHORT", "WAIT"]
    
    print("\n🔮 Recording ML predictions...")
    
    for i, model in enumerate(models):
        prediction = random.choice(predictions)
        confidence = random.uniform(0.6, 0.9)
        
        tracker.record_prediction(
            model_name=model,
            prediction=prediction,
            confidence=confidence,
            price=initial_price,
            symbol=symbol
        )
        
        print(f"  {model}: {prediction} (confidence: {confidence:.1%})")
    
    # Simulate price movements over time
    print("\n📈 Simulating price movements...")
    current_price = initial_price
    
    for minute in range(1, 6):  # 5 minutes of price data
        # Simulate price change
        price_change = random.uniform(-0.002, 0.002)  # ±0.2% change
        current_price += price_change
        
        # Update tracker with new price
        tracker.update_current_price(symbol, current_price)
        
        print(f"  Minute {minute}: {current_price:.6f} (change: {price_change:+.6f})")
        
        # Wait a bit to simulate time passing
        time.sleep(0.1)
    
    # Check accuracies after evaluation window
    print(f"\n⏰ Waiting for evaluation window (1 minute)...")
    time.sleep(2)  # Simulate time passing
    
    # Force update accuracy cache
    tracker._update_accuracy_cache()
    
    print("\n📊 Prediction Accuracy Results:")
    print("-" * 30)
    
    for model in models:
        accuracy = tracker.get_model_accuracy(model)
        stats = tracker.get_prediction_stats(model)
        
        if accuracy is not None:
            print(f"  {model}:")
            print(f"    Accuracy: {accuracy:.1f}%")
            print(f"    Total predictions: {stats.get('total_predictions', 0)}")
        else:
            print(f"  {model}: No accuracy data yet (need more time)")
    
    print("\n✅ Test completed!")
    return tracker

if __name__ == "__main__":
    test_prediction_accuracy()
